﻿using System;
using System.Threading;

namespace System.Collections
{
	// Token: 0x02000495 RID: 1173
	[Serializable]
	internal class ListDictionaryInternal : IDictionary, ICollection, IEnumerable
	{
		// Token: 0x1700084F RID: 2127
		public object this[object key]
		{
			get
			{
				if (key == null)
				{
					throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
				}
				for (ListDictionaryInternal.DictionaryNode next = this.head; next != null; next = next.next)
				{
					if (next.key.Equals(key))
					{
						return next.value;
					}
				}
				return null;
			}
			set
			{
				if (key == null)
				{
					throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
				}
				if (!key.GetType().IsSerializable)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_NotSerializable"), "key");
				}
				if (value != null && !value.GetType().IsSerializable)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_NotSerializable"), "value");
				}
				this.version++;
				ListDictionaryInternal.DictionaryNode dictionaryNode = null;
				ListDictionaryInternal.DictionaryNode next = this.head;
				while (next != null && !next.key.Equals(key))
				{
					dictionaryNode = next;
					next = next.next;
				}
				if (next != null)
				{
					next.value = value;
					return;
				}
				ListDictionaryInternal.DictionaryNode dictionaryNode2 = new ListDictionaryInternal.DictionaryNode();
				dictionaryNode2.key = key;
				dictionaryNode2.value = value;
				if (dictionaryNode != null)
				{
					dictionaryNode.next = dictionaryNode2;
				}
				else
				{
					this.head = dictionaryNode2;
				}
				this.count++;
			}
		}

		// Token: 0x17000850 RID: 2128
		// (get) Token: 0x0600384A RID: 14410 RVA: 0x000D8087 File Offset: 0x000D6287
		public int Count
		{
			get
			{
				return this.count;
			}
		}

		// Token: 0x17000851 RID: 2129
		// (get) Token: 0x0600384B RID: 14411 RVA: 0x000D808F File Offset: 0x000D628F
		public ICollection Keys
		{
			get
			{
				return new ListDictionaryInternal.NodeKeyValueCollection(this, true);
			}
		}

		// Token: 0x17000852 RID: 2130
		// (get) Token: 0x0600384C RID: 14412 RVA: 0x000D8098 File Offset: 0x000D6298
		public bool IsReadOnly
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000853 RID: 2131
		// (get) Token: 0x0600384D RID: 14413 RVA: 0x000D809B File Offset: 0x000D629B
		public bool IsFixedSize
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000854 RID: 2132
		// (get) Token: 0x0600384E RID: 14414 RVA: 0x000D809E File Offset: 0x000D629E
		public bool IsSynchronized
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000855 RID: 2133
		// (get) Token: 0x0600384F RID: 14415 RVA: 0x000D80A1 File Offset: 0x000D62A1
		public object SyncRoot
		{
			get
			{
				if (this._syncRoot == null)
				{
					Interlocked.CompareExchange<object>(ref this._syncRoot, new object(), null);
				}
				return this._syncRoot;
			}
		}

		// Token: 0x17000856 RID: 2134
		// (get) Token: 0x06003850 RID: 14416 RVA: 0x000D80C3 File Offset: 0x000D62C3
		public ICollection Values
		{
			get
			{
				return new ListDictionaryInternal.NodeKeyValueCollection(this, false);
			}
		}

		// Token: 0x06003851 RID: 14417 RVA: 0x000D80CC File Offset: 0x000D62CC
		public void Add(object key, object value)
		{
			if (key == null)
			{
				throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
			}
			if (!key.GetType().IsSerializable)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_NotSerializable"), "key");
			}
			if (value != null && !value.GetType().IsSerializable)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_NotSerializable"), "value");
			}
			this.version++;
			ListDictionaryInternal.DictionaryNode dictionaryNode = null;
			ListDictionaryInternal.DictionaryNode next;
			for (next = this.head; next != null; next = next.next)
			{
				if (next.key.Equals(key))
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_AddingDuplicate__", new object[] { next.key, key }));
				}
				dictionaryNode = next;
			}
			if (next != null)
			{
				next.value = value;
				return;
			}
			ListDictionaryInternal.DictionaryNode dictionaryNode2 = new ListDictionaryInternal.DictionaryNode();
			dictionaryNode2.key = key;
			dictionaryNode2.value = value;
			if (dictionaryNode != null)
			{
				dictionaryNode.next = dictionaryNode2;
			}
			else
			{
				this.head = dictionaryNode2;
			}
			this.count++;
		}

		// Token: 0x06003852 RID: 14418 RVA: 0x000D81CE File Offset: 0x000D63CE
		public void Clear()
		{
			this.count = 0;
			this.head = null;
			this.version++;
		}

		// Token: 0x06003853 RID: 14419 RVA: 0x000D81EC File Offset: 0x000D63EC
		public bool Contains(object key)
		{
			if (key == null)
			{
				throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
			}
			for (ListDictionaryInternal.DictionaryNode next = this.head; next != null; next = next.next)
			{
				if (next.key.Equals(key))
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x06003854 RID: 14420 RVA: 0x000D8238 File Offset: 0x000D6438
		public void CopyTo(Array array, int index)
		{
			if (array == null)
			{
				throw new ArgumentNullException("array");
			}
			if (array.Rank != 1)
			{
				throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
			}
			if (index < 0)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (array.Length - index < this.Count)
			{
				throw new ArgumentException(Environment.GetResourceString("ArgumentOutOfRange_Index"), "index");
			}
			for (ListDictionaryInternal.DictionaryNode next = this.head; next != null; next = next.next)
			{
				array.SetValue(new DictionaryEntry(next.key, next.value), index);
				index++;
			}
		}

		// Token: 0x06003855 RID: 14421 RVA: 0x000D82DF File Offset: 0x000D64DF
		public IDictionaryEnumerator GetEnumerator()
		{
			return new ListDictionaryInternal.NodeEnumerator(this);
		}

		// Token: 0x06003856 RID: 14422 RVA: 0x000D82E7 File Offset: 0x000D64E7
		IEnumerator IEnumerable.GetEnumerator()
		{
			return new ListDictionaryInternal.NodeEnumerator(this);
		}

		// Token: 0x06003857 RID: 14423 RVA: 0x000D82F0 File Offset: 0x000D64F0
		public void Remove(object key)
		{
			if (key == null)
			{
				throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
			}
			this.version++;
			ListDictionaryInternal.DictionaryNode dictionaryNode = null;
			ListDictionaryInternal.DictionaryNode next = this.head;
			while (next != null && !next.key.Equals(key))
			{
				dictionaryNode = next;
				next = next.next;
			}
			if (next == null)
			{
				return;
			}
			if (next == this.head)
			{
				this.head = next.next;
			}
			else
			{
				dictionaryNode.next = next.next;
			}
			this.count--;
		}

		// Token: 0x040018DE RID: 6366
		private ListDictionaryInternal.DictionaryNode head;

		// Token: 0x040018DF RID: 6367
		private int version;

		// Token: 0x040018E0 RID: 6368
		private int count;

		// Token: 0x040018E1 RID: 6369
		[NonSerialized]
		private object _syncRoot;

		// Token: 0x02000BB4 RID: 2996
		private class NodeEnumerator : IDictionaryEnumerator, IEnumerator
		{
			// Token: 0x06006DEE RID: 28142 RVA: 0x0017BA40 File Offset: 0x00179C40
			public NodeEnumerator(ListDictionaryInternal list)
			{
				this.list = list;
				this.version = list.version;
				this.start = true;
				this.current = null;
			}

			// Token: 0x170012A5 RID: 4773
			// (get) Token: 0x06006DEF RID: 28143 RVA: 0x0017BA69 File Offset: 0x00179C69
			public object Current
			{
				get
				{
					return this.Entry;
				}
			}

			// Token: 0x170012A6 RID: 4774
			// (get) Token: 0x06006DF0 RID: 28144 RVA: 0x0017BA76 File Offset: 0x00179C76
			public DictionaryEntry Entry
			{
				get
				{
					if (this.current == null)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
					}
					return new DictionaryEntry(this.current.key, this.current.value);
				}
			}

			// Token: 0x170012A7 RID: 4775
			// (get) Token: 0x06006DF1 RID: 28145 RVA: 0x0017BAAB File Offset: 0x00179CAB
			public object Key
			{
				get
				{
					if (this.current == null)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
					}
					return this.current.key;
				}
			}

			// Token: 0x170012A8 RID: 4776
			// (get) Token: 0x06006DF2 RID: 28146 RVA: 0x0017BAD0 File Offset: 0x00179CD0
			public object Value
			{
				get
				{
					if (this.current == null)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
					}
					return this.current.value;
				}
			}

			// Token: 0x06006DF3 RID: 28147 RVA: 0x0017BAF8 File Offset: 0x00179CF8
			public bool MoveNext()
			{
				if (this.version != this.list.version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
				}
				if (this.start)
				{
					this.current = this.list.head;
					this.start = false;
				}
				else if (this.current != null)
				{
					this.current = this.current.next;
				}
				return this.current != null;
			}

			// Token: 0x06006DF4 RID: 28148 RVA: 0x0017BB6C File Offset: 0x00179D6C
			public void Reset()
			{
				if (this.version != this.list.version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
				}
				this.start = true;
				this.current = null;
			}

			// Token: 0x04003571 RID: 13681
			private ListDictionaryInternal list;

			// Token: 0x04003572 RID: 13682
			private ListDictionaryInternal.DictionaryNode current;

			// Token: 0x04003573 RID: 13683
			private int version;

			// Token: 0x04003574 RID: 13684
			private bool start;
		}

		// Token: 0x02000BB5 RID: 2997
		private class NodeKeyValueCollection : ICollection, IEnumerable
		{
			// Token: 0x06006DF5 RID: 28149 RVA: 0x0017BB9F File Offset: 0x00179D9F
			public NodeKeyValueCollection(ListDictionaryInternal list, bool isKeys)
			{
				this.list = list;
				this.isKeys = isKeys;
			}

			// Token: 0x06006DF6 RID: 28150 RVA: 0x0017BBB8 File Offset: 0x00179DB8
			void ICollection.CopyTo(Array array, int index)
			{
				if (array == null)
				{
					throw new ArgumentNullException("array");
				}
				if (array.Rank != 1)
				{
					throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
				}
				if (index < 0)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (array.Length - index < this.list.Count)
				{
					throw new ArgumentException(Environment.GetResourceString("ArgumentOutOfRange_Index"), "index");
				}
				for (ListDictionaryInternal.DictionaryNode dictionaryNode = this.list.head; dictionaryNode != null; dictionaryNode = dictionaryNode.next)
				{
					array.SetValue(this.isKeys ? dictionaryNode.key : dictionaryNode.value, index);
					index++;
				}
			}

			// Token: 0x170012A9 RID: 4777
			// (get) Token: 0x06006DF7 RID: 28151 RVA: 0x0017BC6C File Offset: 0x00179E6C
			int ICollection.Count
			{
				get
				{
					int num = 0;
					for (ListDictionaryInternal.DictionaryNode dictionaryNode = this.list.head; dictionaryNode != null; dictionaryNode = dictionaryNode.next)
					{
						num++;
					}
					return num;
				}
			}

			// Token: 0x170012AA RID: 4778
			// (get) Token: 0x06006DF8 RID: 28152 RVA: 0x0017BC98 File Offset: 0x00179E98
			bool ICollection.IsSynchronized
			{
				get
				{
					return false;
				}
			}

			// Token: 0x170012AB RID: 4779
			// (get) Token: 0x06006DF9 RID: 28153 RVA: 0x0017BC9B File Offset: 0x00179E9B
			object ICollection.SyncRoot
			{
				get
				{
					return this.list.SyncRoot;
				}
			}

			// Token: 0x06006DFA RID: 28154 RVA: 0x0017BCA8 File Offset: 0x00179EA8
			IEnumerator IEnumerable.GetEnumerator()
			{
				return new ListDictionaryInternal.NodeKeyValueCollection.NodeKeyValueEnumerator(this.list, this.isKeys);
			}

			// Token: 0x04003575 RID: 13685
			private ListDictionaryInternal list;

			// Token: 0x04003576 RID: 13686
			private bool isKeys;

			// Token: 0x02000D09 RID: 3337
			private class NodeKeyValueEnumerator : IEnumerator
			{
				// Token: 0x0600720A RID: 29194 RVA: 0x00188CFB File Offset: 0x00186EFB
				public NodeKeyValueEnumerator(ListDictionaryInternal list, bool isKeys)
				{
					this.list = list;
					this.isKeys = isKeys;
					this.version = list.version;
					this.start = true;
					this.current = null;
				}

				// Token: 0x17001385 RID: 4997
				// (get) Token: 0x0600720B RID: 29195 RVA: 0x00188D2B File Offset: 0x00186F2B
				public object Current
				{
					get
					{
						if (this.current == null)
						{
							throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
						}
						if (!this.isKeys)
						{
							return this.current.value;
						}
						return this.current.key;
					}
				}

				// Token: 0x0600720C RID: 29196 RVA: 0x00188D64 File Offset: 0x00186F64
				public bool MoveNext()
				{
					if (this.version != this.list.version)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
					}
					if (this.start)
					{
						this.current = this.list.head;
						this.start = false;
					}
					else if (this.current != null)
					{
						this.current = this.current.next;
					}
					return this.current != null;
				}

				// Token: 0x0600720D RID: 29197 RVA: 0x00188DD8 File Offset: 0x00186FD8
				public void Reset()
				{
					if (this.version != this.list.version)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
					}
					this.start = true;
					this.current = null;
				}

				// Token: 0x0400394E RID: 14670
				private ListDictionaryInternal list;

				// Token: 0x0400394F RID: 14671
				private ListDictionaryInternal.DictionaryNode current;

				// Token: 0x04003950 RID: 14672
				private int version;

				// Token: 0x04003951 RID: 14673
				private bool isKeys;

				// Token: 0x04003952 RID: 14674
				private bool start;
			}
		}

		// Token: 0x02000BB6 RID: 2998
		[Serializable]
		private class DictionaryNode
		{
			// Token: 0x04003577 RID: 13687
			public object key;

			// Token: 0x04003578 RID: 13688
			public object value;

			// Token: 0x04003579 RID: 13689
			public ListDictionaryInternal.DictionaryNode next;
		}
	}
}
