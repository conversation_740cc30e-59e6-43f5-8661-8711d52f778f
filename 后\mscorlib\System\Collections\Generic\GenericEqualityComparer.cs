﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004C1 RID: 1217
	[Serializable]
	internal class GenericEqualityComparer<T> : EqualityComparer<T> where T : IEquatable<T>
	{
		// Token: 0x06003A83 RID: 14979 RVA: 0x000DF16C File Offset: 0x000DD36C
		public override bool Equals(T x, T y)
		{
			if (x != null)
			{
				return y != null && x.Equals(y);
			}
			return y == null;
		}

		// Token: 0x06003A84 RID: 14980 RVA: 0x000DF19A File Offset: 0x000DD39A
		public override int GetHashCode(T obj)
		{
			if (obj == null)
			{
				return 0;
			}
			return obj.GetHashCode();
		}

		// Token: 0x06003A85 RID: 14981 RVA: 0x000DF1B4 File Offset: 0x000DD3B4
		internal override int IndexOf(T[] array, T value, int startIndex, int count)
		{
			int num = startIndex + count;
			if (value == null)
			{
				for (int i = startIndex; i < num; i++)
				{
					if (array[i] == null)
					{
						return i;
					}
				}
			}
			else
			{
				for (int j = startIndex; j < num; j++)
				{
					if (array[j] != null && array[j].Equals(value))
					{
						return j;
					}
				}
			}
			return -1;
		}

		// Token: 0x06003A86 RID: 14982 RVA: 0x000DF220 File Offset: 0x000DD420
		internal override int LastIndexOf(T[] array, T value, int startIndex, int count)
		{
			int num = startIndex - count + 1;
			if (value == null)
			{
				for (int i = startIndex; i >= num; i--)
				{
					if (array[i] == null)
					{
						return i;
					}
				}
			}
			else
			{
				for (int j = startIndex; j >= num; j--)
				{
					if (array[j] != null && array[j].Equals(value))
					{
						return j;
					}
				}
			}
			return -1;
		}

		// Token: 0x06003A87 RID: 14983 RVA: 0x000DF290 File Offset: 0x000DD490
		public override bool Equals(object obj)
		{
			GenericEqualityComparer<T> genericEqualityComparer = obj as GenericEqualityComparer<T>;
			return genericEqualityComparer != null;
		}

		// Token: 0x06003A88 RID: 14984 RVA: 0x000DF2A8 File Offset: 0x000DD4A8
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}
	}
}
