﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;

namespace System.Collections.ObjectModel
{
	// Token: 0x020004B7 RID: 1207
	[DebuggerTypeProxy(typeof(Mscorlib_DictionaryDebugView<, >))]
	[DebuggerDisplay("Count = {Count}")]
	[__DynamicallyInvokable]
	[Serializable]
	public class ReadOnlyDictionary<TKey, TValue> : IDictionary<TKey, TValue>, ICollection<KeyValuePair<TKey, TValue>>, IEnumerable<KeyValuePair<TKey, TValue>>, IEnumerable, IDictionary, ICollection, IReadOnlyDictionary<TKey, TValue>, IReadOnlyCollection<KeyValuePair<TKey, TValue>>
	{
		// Token: 0x060039F4 RID: 14836 RVA: 0x000DD3F8 File Offset: 0x000DB5F8
		[__DynamicallyInvokable]
		public ReadOnlyDictionary(IDictionary<TKey, TValue> dictionary)
		{
			if (dictionary == null)
			{
				throw new ArgumentNullException("dictionary");
			}
			this.m_dictionary = dictionary;
		}

		// Token: 0x170008B9 RID: 2233
		// (get) Token: 0x060039F5 RID: 14837 RVA: 0x000DD415 File Offset: 0x000DB615
		[__DynamicallyInvokable]
		protected IDictionary<TKey, TValue> Dictionary
		{
			[__DynamicallyInvokable]
			get
			{
				return this.m_dictionary;
			}
		}

		// Token: 0x170008BA RID: 2234
		// (get) Token: 0x060039F6 RID: 14838 RVA: 0x000DD41D File Offset: 0x000DB61D
		[__DynamicallyInvokable]
		public ReadOnlyDictionary<TKey, TValue>.KeyCollection Keys
		{
			[__DynamicallyInvokable]
			get
			{
				if (this.m_keys == null)
				{
					this.m_keys = new ReadOnlyDictionary<TKey, TValue>.KeyCollection(this.m_dictionary.Keys);
				}
				return this.m_keys;
			}
		}

		// Token: 0x170008BB RID: 2235
		// (get) Token: 0x060039F7 RID: 14839 RVA: 0x000DD443 File Offset: 0x000DB643
		[__DynamicallyInvokable]
		public ReadOnlyDictionary<TKey, TValue>.ValueCollection Values
		{
			[__DynamicallyInvokable]
			get
			{
				if (this.m_values == null)
				{
					this.m_values = new ReadOnlyDictionary<TKey, TValue>.ValueCollection(this.m_dictionary.Values);
				}
				return this.m_values;
			}
		}

		// Token: 0x060039F8 RID: 14840 RVA: 0x000DD469 File Offset: 0x000DB669
		[__DynamicallyInvokable]
		public bool ContainsKey(TKey key)
		{
			return this.m_dictionary.ContainsKey(key);
		}

		// Token: 0x170008BC RID: 2236
		// (get) Token: 0x060039F9 RID: 14841 RVA: 0x000DD477 File Offset: 0x000DB677
		[__DynamicallyInvokable]
		ICollection<TKey> IDictionary<TKey, TValue>.Keys
		{
			[__DynamicallyInvokable]
			get
			{
				return this.Keys;
			}
		}

		// Token: 0x060039FA RID: 14842 RVA: 0x000DD47F File Offset: 0x000DB67F
		[__DynamicallyInvokable]
		public bool TryGetValue(TKey key, out TValue value)
		{
			return this.m_dictionary.TryGetValue(key, out value);
		}

		// Token: 0x170008BD RID: 2237
		// (get) Token: 0x060039FB RID: 14843 RVA: 0x000DD48E File Offset: 0x000DB68E
		[__DynamicallyInvokable]
		ICollection<TValue> IDictionary<TKey, TValue>.Values
		{
			[__DynamicallyInvokable]
			get
			{
				return this.Values;
			}
		}

		// Token: 0x170008BE RID: 2238
		[__DynamicallyInvokable]
		public TValue this[TKey key]
		{
			[__DynamicallyInvokable]
			get
			{
				return this.m_dictionary[key];
			}
		}

		// Token: 0x060039FD RID: 14845 RVA: 0x000DD4A4 File Offset: 0x000DB6A4
		[__DynamicallyInvokable]
		void IDictionary<TKey, TValue>.Add(TKey key, TValue value)
		{
			ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
		}

		// Token: 0x060039FE RID: 14846 RVA: 0x000DD4AD File Offset: 0x000DB6AD
		[__DynamicallyInvokable]
		bool IDictionary<TKey, TValue>.Remove(TKey key)
		{
			ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
			return false;
		}

		// Token: 0x170008BF RID: 2239
		[__DynamicallyInvokable]
		TValue IDictionary<TKey, TValue>.this[TKey key]
		{
			[__DynamicallyInvokable]
			get
			{
				return this.m_dictionary[key];
			}
			[__DynamicallyInvokable]
			set
			{
				ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
			}
		}

		// Token: 0x170008C0 RID: 2240
		// (get) Token: 0x06003A01 RID: 14849 RVA: 0x000DD4CE File Offset: 0x000DB6CE
		[__DynamicallyInvokable]
		public int Count
		{
			[__DynamicallyInvokable]
			get
			{
				return this.m_dictionary.Count;
			}
		}

		// Token: 0x06003A02 RID: 14850 RVA: 0x000DD4DB File Offset: 0x000DB6DB
		[__DynamicallyInvokable]
		bool ICollection<KeyValuePair<TKey, TValue>>.Contains(KeyValuePair<TKey, TValue> item)
		{
			return this.m_dictionary.Contains(item);
		}

		// Token: 0x06003A03 RID: 14851 RVA: 0x000DD4E9 File Offset: 0x000DB6E9
		[__DynamicallyInvokable]
		void ICollection<KeyValuePair<TKey, TValue>>.CopyTo(KeyValuePair<TKey, TValue>[] array, int arrayIndex)
		{
			this.m_dictionary.CopyTo(array, arrayIndex);
		}

		// Token: 0x170008C1 RID: 2241
		// (get) Token: 0x06003A04 RID: 14852 RVA: 0x000DD4F8 File Offset: 0x000DB6F8
		[__DynamicallyInvokable]
		bool ICollection<KeyValuePair<TKey, TValue>>.IsReadOnly
		{
			[__DynamicallyInvokable]
			get
			{
				return true;
			}
		}

		// Token: 0x06003A05 RID: 14853 RVA: 0x000DD4FB File Offset: 0x000DB6FB
		[__DynamicallyInvokable]
		void ICollection<KeyValuePair<TKey, TValue>>.Add(KeyValuePair<TKey, TValue> item)
		{
			ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
		}

		// Token: 0x06003A06 RID: 14854 RVA: 0x000DD504 File Offset: 0x000DB704
		[__DynamicallyInvokable]
		void ICollection<KeyValuePair<TKey, TValue>>.Clear()
		{
			ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
		}

		// Token: 0x06003A07 RID: 14855 RVA: 0x000DD50D File Offset: 0x000DB70D
		[__DynamicallyInvokable]
		bool ICollection<KeyValuePair<TKey, TValue>>.Remove(KeyValuePair<TKey, TValue> item)
		{
			ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
			return false;
		}

		// Token: 0x06003A08 RID: 14856 RVA: 0x000DD517 File Offset: 0x000DB717
		[__DynamicallyInvokable]
		public IEnumerator<KeyValuePair<TKey, TValue>> GetEnumerator()
		{
			return this.m_dictionary.GetEnumerator();
		}

		// Token: 0x06003A09 RID: 14857 RVA: 0x000DD524 File Offset: 0x000DB724
		[__DynamicallyInvokable]
		IEnumerator IEnumerable.GetEnumerator()
		{
			return this.m_dictionary.GetEnumerator();
		}

		// Token: 0x06003A0A RID: 14858 RVA: 0x000DD531 File Offset: 0x000DB731
		private static bool IsCompatibleKey(object key)
		{
			if (key == null)
			{
				ThrowHelper.ThrowArgumentNullException(ExceptionArgument.key);
			}
			return key is TKey;
		}

		// Token: 0x06003A0B RID: 14859 RVA: 0x000DD545 File Offset: 0x000DB745
		[__DynamicallyInvokable]
		void IDictionary.Add(object key, object value)
		{
			ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
		}

		// Token: 0x06003A0C RID: 14860 RVA: 0x000DD54E File Offset: 0x000DB74E
		[__DynamicallyInvokable]
		void IDictionary.Clear()
		{
			ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
		}

		// Token: 0x06003A0D RID: 14861 RVA: 0x000DD557 File Offset: 0x000DB757
		[__DynamicallyInvokable]
		bool IDictionary.Contains(object key)
		{
			return ReadOnlyDictionary<TKey, TValue>.IsCompatibleKey(key) && this.ContainsKey((TKey)((object)key));
		}

		// Token: 0x06003A0E RID: 14862 RVA: 0x000DD570 File Offset: 0x000DB770
		[__DynamicallyInvokable]
		IDictionaryEnumerator IDictionary.GetEnumerator()
		{
			IDictionary dictionary = this.m_dictionary as IDictionary;
			if (dictionary != null)
			{
				return dictionary.GetEnumerator();
			}
			return new ReadOnlyDictionary<TKey, TValue>.DictionaryEnumerator(this.m_dictionary);
		}

		// Token: 0x170008C2 RID: 2242
		// (get) Token: 0x06003A0F RID: 14863 RVA: 0x000DD5A3 File Offset: 0x000DB7A3
		[__DynamicallyInvokable]
		bool IDictionary.IsFixedSize
		{
			[__DynamicallyInvokable]
			get
			{
				return true;
			}
		}

		// Token: 0x170008C3 RID: 2243
		// (get) Token: 0x06003A10 RID: 14864 RVA: 0x000DD5A6 File Offset: 0x000DB7A6
		[__DynamicallyInvokable]
		bool IDictionary.IsReadOnly
		{
			[__DynamicallyInvokable]
			get
			{
				return true;
			}
		}

		// Token: 0x170008C4 RID: 2244
		// (get) Token: 0x06003A11 RID: 14865 RVA: 0x000DD5A9 File Offset: 0x000DB7A9
		[__DynamicallyInvokable]
		ICollection IDictionary.Keys
		{
			[__DynamicallyInvokable]
			get
			{
				return this.Keys;
			}
		}

		// Token: 0x06003A12 RID: 14866 RVA: 0x000DD5B1 File Offset: 0x000DB7B1
		[__DynamicallyInvokable]
		void IDictionary.Remove(object key)
		{
			ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
		}

		// Token: 0x170008C5 RID: 2245
		// (get) Token: 0x06003A13 RID: 14867 RVA: 0x000DD5BA File Offset: 0x000DB7BA
		[__DynamicallyInvokable]
		ICollection IDictionary.Values
		{
			[__DynamicallyInvokable]
			get
			{
				return this.Values;
			}
		}

		// Token: 0x170008C6 RID: 2246
		[__DynamicallyInvokable]
		object IDictionary.this[object key]
		{
			[__DynamicallyInvokable]
			get
			{
				if (ReadOnlyDictionary<TKey, TValue>.IsCompatibleKey(key))
				{
					return this[(TKey)((object)key)];
				}
				return null;
			}
			[__DynamicallyInvokable]
			set
			{
				ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
			}
		}

		// Token: 0x06003A16 RID: 14870 RVA: 0x000DD5E8 File Offset: 0x000DB7E8
		[__DynamicallyInvokable]
		void ICollection.CopyTo(Array array, int index)
		{
			if (array == null)
			{
				ThrowHelper.ThrowArgumentNullException(ExceptionArgument.array);
			}
			if (array.Rank != 1)
			{
				ThrowHelper.ThrowArgumentException(ExceptionResource.Arg_RankMultiDimNotSupported);
			}
			if (array.GetLowerBound(0) != 0)
			{
				ThrowHelper.ThrowArgumentException(ExceptionResource.Arg_NonZeroLowerBound);
			}
			if (index < 0 || index > array.Length)
			{
				ThrowHelper.ThrowArgumentOutOfRangeException(ExceptionArgument.index, ExceptionResource.ArgumentOutOfRange_NeedNonNegNum);
			}
			if (array.Length - index < this.Count)
			{
				ThrowHelper.ThrowArgumentException(ExceptionResource.Arg_ArrayPlusOffTooSmall);
			}
			KeyValuePair<TKey, TValue>[] array2 = array as KeyValuePair<TKey, TValue>[];
			if (array2 != null)
			{
				this.m_dictionary.CopyTo(array2, index);
				return;
			}
			DictionaryEntry[] array3 = array as DictionaryEntry[];
			if (array3 != null)
			{
				using (IEnumerator<KeyValuePair<TKey, TValue>> enumerator = this.m_dictionary.GetEnumerator())
				{
					while (enumerator.MoveNext())
					{
						KeyValuePair<TKey, TValue> keyValuePair = enumerator.Current;
						array3[index++] = new DictionaryEntry(keyValuePair.Key, keyValuePair.Value);
					}
					return;
				}
			}
			object[] array4 = array as object[];
			if (array4 == null)
			{
				ThrowHelper.ThrowArgumentException(ExceptionResource.Argument_InvalidArrayType);
			}
			try
			{
				foreach (KeyValuePair<TKey, TValue> keyValuePair2 in this.m_dictionary)
				{
					array4[index++] = new KeyValuePair<TKey, TValue>(keyValuePair2.Key, keyValuePair2.Value);
				}
			}
			catch (ArrayTypeMismatchException)
			{
				ThrowHelper.ThrowArgumentException(ExceptionResource.Argument_InvalidArrayType);
			}
		}

		// Token: 0x170008C7 RID: 2247
		// (get) Token: 0x06003A17 RID: 14871 RVA: 0x000DD754 File Offset: 0x000DB954
		[__DynamicallyInvokable]
		bool ICollection.IsSynchronized
		{
			[__DynamicallyInvokable]
			get
			{
				return false;
			}
		}

		// Token: 0x170008C8 RID: 2248
		// (get) Token: 0x06003A18 RID: 14872 RVA: 0x000DD758 File Offset: 0x000DB958
		[__DynamicallyInvokable]
		object ICollection.SyncRoot
		{
			[__DynamicallyInvokable]
			get
			{
				if (this.m_syncRoot == null)
				{
					ICollection collection = this.m_dictionary as ICollection;
					if (collection != null)
					{
						this.m_syncRoot = collection.SyncRoot;
					}
					else
					{
						Interlocked.CompareExchange<object>(ref this.m_syncRoot, new object(), null);
					}
				}
				return this.m_syncRoot;
			}
		}

		// Token: 0x170008C9 RID: 2249
		// (get) Token: 0x06003A19 RID: 14873 RVA: 0x000DD7A2 File Offset: 0x000DB9A2
		[__DynamicallyInvokable]
		IEnumerable<TKey> IReadOnlyDictionary<TKey, TValue>.Keys
		{
			[__DynamicallyInvokable]
			get
			{
				return this.Keys;
			}
		}

		// Token: 0x170008CA RID: 2250
		// (get) Token: 0x06003A1A RID: 14874 RVA: 0x000DD7AA File Offset: 0x000DB9AA
		[__DynamicallyInvokable]
		IEnumerable<TValue> IReadOnlyDictionary<TKey, TValue>.Values
		{
			[__DynamicallyInvokable]
			get
			{
				return this.Values;
			}
		}

		// Token: 0x04001938 RID: 6456
		private readonly IDictionary<TKey, TValue> m_dictionary;

		// Token: 0x04001939 RID: 6457
		[NonSerialized]
		private object m_syncRoot;

		// Token: 0x0400193A RID: 6458
		[NonSerialized]
		private ReadOnlyDictionary<TKey, TValue>.KeyCollection m_keys;

		// Token: 0x0400193B RID: 6459
		[NonSerialized]
		private ReadOnlyDictionary<TKey, TValue>.ValueCollection m_values;

		// Token: 0x02000BDE RID: 3038
		[Serializable]
		private struct DictionaryEnumerator : IDictionaryEnumerator, IEnumerator
		{
			// Token: 0x06006EF1 RID: 28401 RVA: 0x0017E3BF File Offset: 0x0017C5BF
			public DictionaryEnumerator(IDictionary<TKey, TValue> dictionary)
			{
				this.m_dictionary = dictionary;
				this.m_enumerator = this.m_dictionary.GetEnumerator();
			}

			// Token: 0x170012FD RID: 4861
			// (get) Token: 0x06006EF2 RID: 28402 RVA: 0x0017E3DC File Offset: 0x0017C5DC
			public DictionaryEntry Entry
			{
				get
				{
					KeyValuePair<TKey, TValue> keyValuePair = this.m_enumerator.Current;
					object obj = keyValuePair.Key;
					keyValuePair = this.m_enumerator.Current;
					return new DictionaryEntry(obj, keyValuePair.Value);
				}
			}

			// Token: 0x170012FE RID: 4862
			// (get) Token: 0x06006EF3 RID: 28403 RVA: 0x0017E420 File Offset: 0x0017C620
			public object Key
			{
				get
				{
					KeyValuePair<TKey, TValue> keyValuePair = this.m_enumerator.Current;
					return keyValuePair.Key;
				}
			}

			// Token: 0x170012FF RID: 4863
			// (get) Token: 0x06006EF4 RID: 28404 RVA: 0x0017E448 File Offset: 0x0017C648
			public object Value
			{
				get
				{
					KeyValuePair<TKey, TValue> keyValuePair = this.m_enumerator.Current;
					return keyValuePair.Value;
				}
			}

			// Token: 0x17001300 RID: 4864
			// (get) Token: 0x06006EF5 RID: 28405 RVA: 0x0017E46D File Offset: 0x0017C66D
			public object Current
			{
				get
				{
					return this.Entry;
				}
			}

			// Token: 0x06006EF6 RID: 28406 RVA: 0x0017E47A File Offset: 0x0017C67A
			public bool MoveNext()
			{
				return this.m_enumerator.MoveNext();
			}

			// Token: 0x06006EF7 RID: 28407 RVA: 0x0017E487 File Offset: 0x0017C687
			public void Reset()
			{
				this.m_enumerator.Reset();
			}

			// Token: 0x040035F3 RID: 13811
			private readonly IDictionary<TKey, TValue> m_dictionary;

			// Token: 0x040035F4 RID: 13812
			private IEnumerator<KeyValuePair<TKey, TValue>> m_enumerator;
		}

		// Token: 0x02000BDF RID: 3039
		[DebuggerTypeProxy(typeof(Mscorlib_CollectionDebugView<>))]
		[DebuggerDisplay("Count = {Count}")]
		[__DynamicallyInvokable]
		[Serializable]
		public sealed class KeyCollection : ICollection<TKey>, IEnumerable<TKey>, IEnumerable, ICollection, IReadOnlyCollection<TKey>
		{
			// Token: 0x06006EF8 RID: 28408 RVA: 0x0017E494 File Offset: 0x0017C694
			internal KeyCollection(ICollection<TKey> collection)
			{
				if (collection == null)
				{
					ThrowHelper.ThrowArgumentNullException(ExceptionArgument.collection);
				}
				this.m_collection = collection;
			}

			// Token: 0x06006EF9 RID: 28409 RVA: 0x0017E4AC File Offset: 0x0017C6AC
			[__DynamicallyInvokable]
			void ICollection<TKey>.Add(TKey item)
			{
				ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
			}

			// Token: 0x06006EFA RID: 28410 RVA: 0x0017E4B5 File Offset: 0x0017C6B5
			[__DynamicallyInvokable]
			void ICollection<TKey>.Clear()
			{
				ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
			}

			// Token: 0x06006EFB RID: 28411 RVA: 0x0017E4BE File Offset: 0x0017C6BE
			[__DynamicallyInvokable]
			bool ICollection<TKey>.Contains(TKey item)
			{
				return this.m_collection.Contains(item);
			}

			// Token: 0x06006EFC RID: 28412 RVA: 0x0017E4CC File Offset: 0x0017C6CC
			[__DynamicallyInvokable]
			public void CopyTo(TKey[] array, int arrayIndex)
			{
				this.m_collection.CopyTo(array, arrayIndex);
			}

			// Token: 0x17001301 RID: 4865
			// (get) Token: 0x06006EFD RID: 28413 RVA: 0x0017E4DB File Offset: 0x0017C6DB
			[__DynamicallyInvokable]
			public int Count
			{
				[__DynamicallyInvokable]
				get
				{
					return this.m_collection.Count;
				}
			}

			// Token: 0x17001302 RID: 4866
			// (get) Token: 0x06006EFE RID: 28414 RVA: 0x0017E4E8 File Offset: 0x0017C6E8
			[__DynamicallyInvokable]
			bool ICollection<TKey>.IsReadOnly
			{
				[__DynamicallyInvokable]
				get
				{
					return true;
				}
			}

			// Token: 0x06006EFF RID: 28415 RVA: 0x0017E4EB File Offset: 0x0017C6EB
			[__DynamicallyInvokable]
			bool ICollection<TKey>.Remove(TKey item)
			{
				ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
				return false;
			}

			// Token: 0x06006F00 RID: 28416 RVA: 0x0017E4F5 File Offset: 0x0017C6F5
			[__DynamicallyInvokable]
			public IEnumerator<TKey> GetEnumerator()
			{
				return this.m_collection.GetEnumerator();
			}

			// Token: 0x06006F01 RID: 28417 RVA: 0x0017E502 File Offset: 0x0017C702
			[__DynamicallyInvokable]
			IEnumerator IEnumerable.GetEnumerator()
			{
				return this.m_collection.GetEnumerator();
			}

			// Token: 0x06006F02 RID: 28418 RVA: 0x0017E50F File Offset: 0x0017C70F
			[__DynamicallyInvokable]
			void ICollection.CopyTo(Array array, int index)
			{
				ReadOnlyDictionaryHelpers.CopyToNonGenericICollectionHelper<TKey>(this.m_collection, array, index);
			}

			// Token: 0x17001303 RID: 4867
			// (get) Token: 0x06006F03 RID: 28419 RVA: 0x0017E51E File Offset: 0x0017C71E
			[__DynamicallyInvokable]
			bool ICollection.IsSynchronized
			{
				[__DynamicallyInvokable]
				get
				{
					return false;
				}
			}

			// Token: 0x17001304 RID: 4868
			// (get) Token: 0x06006F04 RID: 28420 RVA: 0x0017E524 File Offset: 0x0017C724
			[__DynamicallyInvokable]
			object ICollection.SyncRoot
			{
				[__DynamicallyInvokable]
				get
				{
					if (this.m_syncRoot == null)
					{
						ICollection collection = this.m_collection as ICollection;
						if (collection != null)
						{
							this.m_syncRoot = collection.SyncRoot;
						}
						else
						{
							Interlocked.CompareExchange<object>(ref this.m_syncRoot, new object(), null);
						}
					}
					return this.m_syncRoot;
				}
			}

			// Token: 0x040035F5 RID: 13813
			private readonly ICollection<TKey> m_collection;

			// Token: 0x040035F6 RID: 13814
			[NonSerialized]
			private object m_syncRoot;
		}

		// Token: 0x02000BE0 RID: 3040
		[DebuggerTypeProxy(typeof(Mscorlib_CollectionDebugView<>))]
		[DebuggerDisplay("Count = {Count}")]
		[__DynamicallyInvokable]
		[Serializable]
		public sealed class ValueCollection : ICollection<TValue>, IEnumerable<TValue>, IEnumerable, ICollection, IReadOnlyCollection<TValue>
		{
			// Token: 0x06006F05 RID: 28421 RVA: 0x0017E56E File Offset: 0x0017C76E
			internal ValueCollection(ICollection<TValue> collection)
			{
				if (collection == null)
				{
					ThrowHelper.ThrowArgumentNullException(ExceptionArgument.collection);
				}
				this.m_collection = collection;
			}

			// Token: 0x06006F06 RID: 28422 RVA: 0x0017E586 File Offset: 0x0017C786
			[__DynamicallyInvokable]
			void ICollection<TValue>.Add(TValue item)
			{
				ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
			}

			// Token: 0x06006F07 RID: 28423 RVA: 0x0017E58F File Offset: 0x0017C78F
			[__DynamicallyInvokable]
			void ICollection<TValue>.Clear()
			{
				ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
			}

			// Token: 0x06006F08 RID: 28424 RVA: 0x0017E598 File Offset: 0x0017C798
			[__DynamicallyInvokable]
			bool ICollection<TValue>.Contains(TValue item)
			{
				return this.m_collection.Contains(item);
			}

			// Token: 0x06006F09 RID: 28425 RVA: 0x0017E5A6 File Offset: 0x0017C7A6
			[__DynamicallyInvokable]
			public void CopyTo(TValue[] array, int arrayIndex)
			{
				this.m_collection.CopyTo(array, arrayIndex);
			}

			// Token: 0x17001305 RID: 4869
			// (get) Token: 0x06006F0A RID: 28426 RVA: 0x0017E5B5 File Offset: 0x0017C7B5
			[__DynamicallyInvokable]
			public int Count
			{
				[__DynamicallyInvokable]
				get
				{
					return this.m_collection.Count;
				}
			}

			// Token: 0x17001306 RID: 4870
			// (get) Token: 0x06006F0B RID: 28427 RVA: 0x0017E5C2 File Offset: 0x0017C7C2
			[__DynamicallyInvokable]
			bool ICollection<TValue>.IsReadOnly
			{
				[__DynamicallyInvokable]
				get
				{
					return true;
				}
			}

			// Token: 0x06006F0C RID: 28428 RVA: 0x0017E5C5 File Offset: 0x0017C7C5
			[__DynamicallyInvokable]
			bool ICollection<TValue>.Remove(TValue item)
			{
				ThrowHelper.ThrowNotSupportedException(ExceptionResource.NotSupported_ReadOnlyCollection);
				return false;
			}

			// Token: 0x06006F0D RID: 28429 RVA: 0x0017E5CF File Offset: 0x0017C7CF
			[__DynamicallyInvokable]
			public IEnumerator<TValue> GetEnumerator()
			{
				return this.m_collection.GetEnumerator();
			}

			// Token: 0x06006F0E RID: 28430 RVA: 0x0017E5DC File Offset: 0x0017C7DC
			[__DynamicallyInvokable]
			IEnumerator IEnumerable.GetEnumerator()
			{
				return this.m_collection.GetEnumerator();
			}

			// Token: 0x06006F0F RID: 28431 RVA: 0x0017E5E9 File Offset: 0x0017C7E9
			[__DynamicallyInvokable]
			void ICollection.CopyTo(Array array, int index)
			{
				ReadOnlyDictionaryHelpers.CopyToNonGenericICollectionHelper<TValue>(this.m_collection, array, index);
			}

			// Token: 0x17001307 RID: 4871
			// (get) Token: 0x06006F10 RID: 28432 RVA: 0x0017E5F8 File Offset: 0x0017C7F8
			[__DynamicallyInvokable]
			bool ICollection.IsSynchronized
			{
				[__DynamicallyInvokable]
				get
				{
					return false;
				}
			}

			// Token: 0x17001308 RID: 4872
			// (get) Token: 0x06006F11 RID: 28433 RVA: 0x0017E5FC File Offset: 0x0017C7FC
			[__DynamicallyInvokable]
			object ICollection.SyncRoot
			{
				[__DynamicallyInvokable]
				get
				{
					if (this.m_syncRoot == null)
					{
						ICollection collection = this.m_collection as ICollection;
						if (collection != null)
						{
							this.m_syncRoot = collection.SyncRoot;
						}
						else
						{
							Interlocked.CompareExchange<object>(ref this.m_syncRoot, new object(), null);
						}
					}
					return this.m_syncRoot;
				}
			}

			// Token: 0x040035F7 RID: 13815
			private readonly ICollection<TValue> m_collection;

			// Token: 0x040035F8 RID: 13816
			[NonSerialized]
			private object m_syncRoot;
		}
	}
}
