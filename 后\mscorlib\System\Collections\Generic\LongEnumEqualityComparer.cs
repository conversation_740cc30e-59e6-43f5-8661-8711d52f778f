﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using System.Security;

namespace System.Collections.Generic
{
	// Token: 0x020004C8 RID: 1224
	[Serializable]
	internal sealed class LongEnumEqualityComparer<T> : EqualityComparer<T>, ISerializable where T : struct
	{
		// Token: 0x06003AAC RID: 15020 RVA: 0x000DF79C File Offset: 0x000DD99C
		public override bool Equals(T x, T y)
		{
			long num = JitHelpers.UnsafeEnumCastLong<T>(x);
			long num2 = JitHelpers.UnsafeEnumCastLong<T>(y);
			return num == num2;
		}

		// Token: 0x06003AAD RID: 15021 RVA: 0x000DF7BC File Offset: 0x000DD9BC
		public override int GetHashCode(T obj)
		{
			return JitHelpers.UnsafeEnumCastLong<T>(obj).GetHashCode();
		}

		// Token: 0x06003AAE RID: 15022 RVA: 0x000DF7D8 File Offset: 0x000DD9D8
		public override bool Equals(object obj)
		{
			LongEnumEqualityComparer<T> longEnumEqualityComparer = obj as LongEnumEqualityComparer<T>;
			return longEnumEqualityComparer != null;
		}

		// Token: 0x06003AAF RID: 15023 RVA: 0x000DF7F0 File Offset: 0x000DD9F0
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}

		// Token: 0x06003AB0 RID: 15024 RVA: 0x000DF802 File Offset: 0x000DDA02
		public LongEnumEqualityComparer()
		{
		}

		// Token: 0x06003AB1 RID: 15025 RVA: 0x000DF80A File Offset: 0x000DDA0A
		public LongEnumEqualityComparer(SerializationInfo information, StreamingContext context)
		{
		}

		// Token: 0x06003AB2 RID: 15026 RVA: 0x000DF812 File Offset: 0x000DDA12
		[SecurityCritical]
		public void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			info.SetType(typeof(ObjectEqualityComparer<T>));
		}
	}
}
