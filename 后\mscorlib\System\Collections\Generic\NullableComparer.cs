﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004BC RID: 1212
	[Serializable]
	internal class NullableComparer<T> : Comparer<T?> where T : struct, IComparable<T>
	{
		// Token: 0x06003A39 RID: 14905 RVA: 0x000DDF22 File Offset: 0x000DC122
		public override int Compare(T? x, T? y)
		{
			if (x != null)
			{
				if (y != null)
				{
					return x.value.CompareTo(y.value);
				}
				return 1;
			}
			else
			{
				if (y != null)
				{
					return -1;
				}
				return 0;
			}
		}

		// Token: 0x06003A3A RID: 14906 RVA: 0x000DDF60 File Offset: 0x000DC160
		public override bool Equals(object obj)
		{
			NullableComparer<T> nullableComparer = obj as NullableComparer<T>;
			return nullableComparer != null;
		}

		// Token: 0x06003A3B RID: 14907 RVA: 0x000DDF78 File Offset: 0x000DC178
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}
	}
}
