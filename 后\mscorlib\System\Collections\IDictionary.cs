﻿using System;
using System.Runtime.InteropServices;

namespace System.Collections
{
	// Token: 0x0200049C RID: 1180
	[ComVisible(true)]
	[__DynamicallyInvokable]
	public interface IDictionary : ICollection, IEnumerable
	{
		// Token: 0x17000870 RID: 2160
		[__DynamicallyInvokable]
		object this[object key]
		{
			[__DynamicallyInvokable]
			get;
			[__DynamicallyInvokable]
			set;
		}

		// Token: 0x17000871 RID: 2161
		// (get) Token: 0x060038B8 RID: 14520
		[__DynamicallyInvokable]
		ICollection Keys
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x17000872 RID: 2162
		// (get) Token: 0x060038B9 RID: 14521
		[__DynamicallyInvokable]
		ICollection Values
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x060038BA RID: 14522
		[__DynamicallyInvokable]
		bool Contains(object key);

		// Token: 0x060038BB RID: 14523
		[__DynamicallyInvokable]
		void Add(object key, object value);

		// Token: 0x060038BC RID: 14524
		[__DynamicallyInvokable]
		void Clear();

		// Token: 0x17000873 RID: 2163
		// (get) Token: 0x060038BD RID: 14525
		[__DynamicallyInvokable]
		bool IsReadOnly
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x17000874 RID: 2164
		// (get) Token: 0x060038BE RID: 14526
		[__DynamicallyInvokable]
		bool IsFixedSize
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x060038BF RID: 14527
		[__DynamicallyInvokable]
		IDictionaryEnumerator GetEnumerator();

		// Token: 0x060038C0 RID: 14528
		[__DynamicallyInvokable]
		void Remove(object key);
	}
}
