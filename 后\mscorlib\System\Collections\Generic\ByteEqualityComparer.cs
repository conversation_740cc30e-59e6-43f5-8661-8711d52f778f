﻿using System;
using System.Security;

namespace System.Collections.Generic
{
	// Token: 0x020004C4 RID: 1220
	[Serializable]
	internal class ByteEqualityComparer : EqualityComparer<byte>
	{
		// Token: 0x06003A98 RID: 15000 RVA: 0x000DF596 File Offset: 0x000DD796
		public override bool Equals(byte x, byte y)
		{
			return x == y;
		}

		// Token: 0x06003A99 RID: 15001 RVA: 0x000DF59C File Offset: 0x000DD79C
		public override int GetHashCode(byte b)
		{
			return b.GetHashCode();
		}

		// Token: 0x06003A9A RID: 15002 RVA: 0x000DF5A8 File Offset: 0x000DD7A8
		[SecuritySafeCritical]
		internal unsafe override int IndexOf(byte[] array, byte value, int startIndex, int count)
		{
			if (array == null)
			{
				throw new ArgumentNullException("array");
			}
			if (startIndex < 0)
			{
				throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			if (count < 0)
			{
				throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_Count"));
			}
			if (count > array.Length - startIndex)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
			}
			if (count == 0)
			{
				return -1;
			}
			fixed (byte* ptr = array)
			{
				return Buffer.IndexOfByte(ptr, value, startIndex, count);
			}
		}

		// Token: 0x06003A9B RID: 15003 RVA: 0x000DF638 File Offset: 0x000DD838
		internal override int LastIndexOf(byte[] array, byte value, int startIndex, int count)
		{
			int num = startIndex - count + 1;
			for (int i = startIndex; i >= num; i--)
			{
				if (array[i] == value)
				{
					return i;
				}
			}
			return -1;
		}

		// Token: 0x06003A9C RID: 15004 RVA: 0x000DF664 File Offset: 0x000DD864
		public override bool Equals(object obj)
		{
			ByteEqualityComparer byteEqualityComparer = obj as ByteEqualityComparer;
			return byteEqualityComparer != null;
		}

		// Token: 0x06003A9D RID: 15005 RVA: 0x000DF67C File Offset: 0x000DD87C
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}
	}
}
