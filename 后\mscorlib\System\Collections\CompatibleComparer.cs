﻿using System;

namespace System.Collections
{
	// Token: 0x02000494 RID: 1172
	[Serializable]
	internal class CompatibleComparer : IEqualityComparer
	{
		// Token: 0x06003841 RID: 14401 RVA: 0x000D7E9C File Offset: 0x000D609C
		internal CompatibleComparer(IComparer comparer, IHashCodeProvider hashCodeProvider)
		{
			this._comparer = comparer;
			this._hcp = hashCodeProvider;
		}

		// Token: 0x06003842 RID: 14402 RVA: 0x000D7EB4 File Offset: 0x000D60B4
		public int Compare(object a, object b)
		{
			if (a == b)
			{
				return 0;
			}
			if (a == null)
			{
				return -1;
			}
			if (b == null)
			{
				return 1;
			}
			if (this._comparer != null)
			{
				return this._comparer.Compare(a, b);
			}
			IComparable comparable = a as IComparable;
			if (comparable != null)
			{
				return comparable.CompareTo(b);
			}
			throw new ArgumentException(Environment.GetResourceString("Argument_ImplementIComparable"));
		}

		// Token: 0x06003843 RID: 14403 RVA: 0x000D7F08 File Offset: 0x000D6108
		public bool Equals(object a, object b)
		{
			return this.Compare(a, b) == 0;
		}

		// Token: 0x06003844 RID: 14404 RVA: 0x000D7F15 File Offset: 0x000D6115
		public int GetHashCode(object obj)
		{
			if (obj == null)
			{
				throw new ArgumentNullException("obj");
			}
			if (this._hcp != null)
			{
				return this._hcp.GetHashCode(obj);
			}
			return obj.GetHashCode();
		}

		// Token: 0x1700084D RID: 2125
		// (get) Token: 0x06003845 RID: 14405 RVA: 0x000D7F40 File Offset: 0x000D6140
		internal IComparer Comparer
		{
			get
			{
				return this._comparer;
			}
		}

		// Token: 0x1700084E RID: 2126
		// (get) Token: 0x06003846 RID: 14406 RVA: 0x000D7F48 File Offset: 0x000D6148
		internal IHashCodeProvider HashCodeProvider
		{
			get
			{
				return this._hcp;
			}
		}

		// Token: 0x040018DC RID: 6364
		private IComparer _comparer;

		// Token: 0x040018DD RID: 6365
		private IHashCodeProvider _hcp;
	}
}
