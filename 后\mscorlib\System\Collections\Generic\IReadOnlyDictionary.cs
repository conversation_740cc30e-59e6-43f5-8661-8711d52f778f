﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004D9 RID: 1241
	[__DynamicallyInvokable]
	public interface IReadOnlyDictionary<TKey, TValue> : IReadOnlyCollection<KeyValuePair<TKey, TValue>>, IEnumerable<KeyValuePair<TKey, TValue>>, IEnumerable
	{
		// Token: 0x06003AE8 RID: 15080
		[__DynamicallyInvokable]
		bool ContainsKey(TKey key);

		// Token: 0x06003AE9 RID: 15081
		[__DynamicallyInvokable]
		bool TryGetValue(TKey key, out TValue value);

		// Token: 0x170008EF RID: 2287
		[__DynamicallyInvokable]
		TValue this[TKey key]
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x170008F0 RID: 2288
		// (get) Token: 0x06003AEB RID: 15083
		[__DynamicallyInvokable]
		IEnumerable<TKey> Keys
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x170008F1 RID: 2289
		// (get) Token: 0x06003AEC RID: 15084
		[__DynamicallyInvokable]
		IEnumerable<TValue> Values
		{
			[__DynamicallyInvokable]
			get;
		}
	}
}
