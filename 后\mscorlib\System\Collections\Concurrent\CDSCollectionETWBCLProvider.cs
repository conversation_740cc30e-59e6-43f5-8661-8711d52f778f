﻿using System;
using System.Diagnostics.Tracing;
using System.Runtime.CompilerServices;

namespace System.Collections.Concurrent
{
	// Token: 0x020004AD RID: 1197
	[FriendAccessAllowed]
	[EventSource(Name = "System.Collections.Concurrent.ConcurrentCollectionsEventSource", Guid = "35167F8E-49B2-4b96-AB86-435B59336B5E", LocalizationResources = "mscorlib")]
	internal sealed class CDSCollectionETWBCLProvider : EventSource
	{
		// Token: 0x06003932 RID: 14642 RVA: 0x000DACAE File Offset: 0x000D8EAE
		private CDSCollectionETWBCLProvider()
		{
		}

		// Token: 0x06003933 RID: 14643 RVA: 0x000DACB6 File Offset: 0x000D8EB6
		[Event(1, Level = EventLevel.Warning)]
		public void ConcurrentStack_FastPushFailed(int spinCount)
		{
			if (base.IsEnabled(EventLevel.Warning, EventKeywords.All))
			{
				base.WriteEvent(1, spinCount);
			}
		}

		// Token: 0x06003934 RID: 14644 RVA: 0x000DACCB File Offset: 0x000D8ECB
		[Event(2, Level = EventLevel.Warning)]
		public void ConcurrentStack_FastPopFailed(int spinCount)
		{
			if (base.IsEnabled(EventLevel.Warning, EventKeywords.All))
			{
				base.WriteEvent(2, spinCount);
			}
		}

		// Token: 0x06003935 RID: 14645 RVA: 0x000DACE0 File Offset: 0x000D8EE0
		[Event(3, Level = EventLevel.Warning)]
		public void ConcurrentDictionary_AcquiringAllLocks(int numOfBuckets)
		{
			if (base.IsEnabled(EventLevel.Warning, EventKeywords.All))
			{
				base.WriteEvent(3, numOfBuckets);
			}
		}

		// Token: 0x06003936 RID: 14646 RVA: 0x000DACF5 File Offset: 0x000D8EF5
		[Event(4, Level = EventLevel.Verbose)]
		public void ConcurrentBag_TryTakeSteals()
		{
			if (base.IsEnabled(EventLevel.Verbose, EventKeywords.All))
			{
				base.WriteEvent(4);
			}
		}

		// Token: 0x06003937 RID: 14647 RVA: 0x000DAD09 File Offset: 0x000D8F09
		[Event(5, Level = EventLevel.Verbose)]
		public void ConcurrentBag_TryPeekSteals()
		{
			if (base.IsEnabled(EventLevel.Verbose, EventKeywords.All))
			{
				base.WriteEvent(5);
			}
		}

		// Token: 0x04001915 RID: 6421
		public static CDSCollectionETWBCLProvider Log = new CDSCollectionETWBCLProvider();

		// Token: 0x04001916 RID: 6422
		private const EventKeywords ALL_KEYWORDS = EventKeywords.All;

		// Token: 0x04001917 RID: 6423
		private const int CONCURRENTSTACK_FASTPUSHFAILED_ID = 1;

		// Token: 0x04001918 RID: 6424
		private const int CONCURRENTSTACK_FASTPOPFAILED_ID = 2;

		// Token: 0x04001919 RID: 6425
		private const int CONCURRENTDICTIONARY_ACQUIRINGALLLOCKS_ID = 3;

		// Token: 0x0400191A RID: 6426
		private const int CONCURRENTBAG_TRYTAKESTEALS_ID = 4;

		// Token: 0x0400191B RID: 6427
		private const int CONCURRENTBAG_TRYPEEKSTEALS_ID = 5;
	}
}
