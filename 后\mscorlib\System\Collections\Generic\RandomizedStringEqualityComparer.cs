﻿using System;
using System.Security;

namespace System.Collections.Generic
{
	// Token: 0x020004C9 RID: 1225
	internal sealed class RandomizedStringEqualityComparer : IEqualityComparer<string>, IEqualityComparer, IWellKnownStringEqualityComparer
	{
		// Token: 0x06003AB3 RID: 15027 RVA: 0x000DF824 File Offset: 0x000DDA24
		public RandomizedStringEqualityComparer()
		{
			this._entropy = HashHelpers.GetEntropy();
		}

		// Token: 0x06003AB4 RID: 15028 RVA: 0x000DF837 File Offset: 0x000DDA37
		public bool Equals(object x, object y)
		{
			if (x == y)
			{
				return true;
			}
			if (x == null || y == null)
			{
				return false;
			}
			if (x is string && y is string)
			{
				return this.Equals((string)x, (string)y);
			}
			ThrowHelper.ThrowArgumentException(ExceptionResource.Argument_InvalidArgumentForComparison);
			return false;
		}

		// Token: 0x06003AB5 RID: 15029 RVA: 0x000DF871 File Offset: 0x000DDA71
		public bool Equals(string x, string y)
		{
			if (x != null)
			{
				return y != null && x.Equals(y);
			}
			return y == null;
		}

		// Token: 0x06003AB6 RID: 15030 RVA: 0x000DF889 File Offset: 0x000DDA89
		[SecuritySafeCritical]
		public int GetHashCode(string obj)
		{
			if (obj == null)
			{
				return 0;
			}
			return string.InternalMarvin32HashString(obj, obj.Length, this._entropy);
		}

		// Token: 0x06003AB7 RID: 15031 RVA: 0x000DF8A4 File Offset: 0x000DDAA4
		[SecuritySafeCritical]
		public int GetHashCode(object obj)
		{
			if (obj == null)
			{
				return 0;
			}
			string text = obj as string;
			if (text != null)
			{
				return string.InternalMarvin32HashString(text, text.Length, this._entropy);
			}
			return obj.GetHashCode();
		}

		// Token: 0x06003AB8 RID: 15032 RVA: 0x000DF8DC File Offset: 0x000DDADC
		public override bool Equals(object obj)
		{
			RandomizedStringEqualityComparer randomizedStringEqualityComparer = obj as RandomizedStringEqualityComparer;
			return randomizedStringEqualityComparer != null && this._entropy == randomizedStringEqualityComparer._entropy;
		}

		// Token: 0x06003AB9 RID: 15033 RVA: 0x000DF903 File Offset: 0x000DDB03
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode() ^ (int)(this._entropy & 2147483647L);
		}

		// Token: 0x06003ABA RID: 15034 RVA: 0x000DF924 File Offset: 0x000DDB24
		IEqualityComparer IWellKnownStringEqualityComparer.GetRandomizedEqualityComparer()
		{
			return new RandomizedStringEqualityComparer();
		}

		// Token: 0x06003ABB RID: 15035 RVA: 0x000DF92B File Offset: 0x000DDB2B
		IEqualityComparer IWellKnownStringEqualityComparer.GetEqualityComparerForSerialization()
		{
			return EqualityComparer<string>.Default;
		}

		// Token: 0x04001952 RID: 6482
		private long _entropy;
	}
}
