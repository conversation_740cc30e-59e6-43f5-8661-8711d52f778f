﻿using System;
using System.Diagnostics;

namespace System.Collections.Generic
{
	// Token: 0x020004CE RID: 1230
	internal sealed class Mscorlib_DictionaryDebugView<K, V>
	{
		// Token: 0x06003AC9 RID: 15049 RVA: 0x000DFAB8 File Offset: 0x000DDCB8
		public Mscorlib_DictionaryDebugView(IDictionary<K, V> dictionary)
		{
			if (dictionary == null)
			{
				ThrowHelper.ThrowArgumentNullException(ExceptionArgument.dictionary);
			}
			this.dict = dictionary;
		}

		// Token: 0x170008E4 RID: 2276
		// (get) Token: 0x06003ACA RID: 15050 RVA: 0x000DFAD0 File Offset: 0x000DDCD0
		[DebuggerBrowsable(DebuggerBrowsableState.RootHidden)]
		public KeyValuePair<K, V>[] Items
		{
			get
			{
				KeyValuePair<K, V>[] array = new KeyValuePair<K, V>[this.dict.Count];
				this.dict.CopyTo(array, 0);
				return array;
			}
		}

		// Token: 0x04001957 RID: 6487
		private IDictionary<K, V> dict;
	}
}
