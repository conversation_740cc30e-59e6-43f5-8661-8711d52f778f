﻿using System;
using System.Diagnostics.Tracing;
using System.Runtime.ConstrainedExecution;
using System.Runtime.InteropServices;
using System.Security;
using System.Text;

namespace Microsoft.Win32
{
	// Token: 0x02000018 RID: 24
	[SecurityCritical]
	[SuppressUnmanagedCodeSecurity]
	internal static class UnsafeNativeMethods
	{
		// Token: 0x06000150 RID: 336
		[DllImport("kernel32.dll", ExactSpelling = true, SetLastError = true)]
		internal static extern int GetTimeZoneInformation(out Win32Native.TimeZoneInformation lpTimeZoneInformation);

		// Token: 0x06000151 RID: 337
		[DllImport("kernel32.dll", ExactSpelling = true, SetLastError = true)]
		internal static extern int GetDynamicTimeZoneInformation(out Win32Native.DynamicTimeZoneInformation lpDynamicTimeZoneInformation);

		// Token: 0x06000152 RID: 338
		[DllImport("kernel32.dll", ExactSpelling = true, SetLastError = true)]
		[return: MarshalAs(UnmanagedType.Bool)]
		internal static extern bool GetFileMUIPath(int flags, [MarshalAs(UnmanagedType.LPWStr)] string filePath, [MarshalAs(UnmanagedType.LPWStr)] StringBuilder language, ref int languageLength, [MarshalAs(UnmanagedType.LPWStr)] StringBuilder fileMuiPath, ref int fileMuiPathLength, ref long enumerator);

		// Token: 0x06000153 RID: 339
		[DllImport("user32.dll", CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Unicode, EntryPoint = "LoadStringW", ExactSpelling = true, SetLastError = true)]
		internal static extern int LoadString(SafeLibraryHandle handle, int id, StringBuilder buffer, int bufferLength);

		// Token: 0x06000154 RID: 340
		[DllImport("kernel32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
		internal static extern SafeLibraryHandle LoadLibraryEx(string libFilename, IntPtr reserved, int flags);

		// Token: 0x06000155 RID: 341
		[ReliabilityContract(Consistency.WillNotCorruptState, Cer.Success)]
		[DllImport("kernel32.dll", CharSet = CharSet.Unicode)]
		[return: MarshalAs(UnmanagedType.Bool)]
		internal static extern bool FreeLibrary(IntPtr hModule);

		// Token: 0x06000156 RID: 342
		[SecurityCritical]
		[DllImport("combase.dll")]
		internal static extern int RoGetActivationFactory([MarshalAs(UnmanagedType.HString)] string activatableClassId, [In] ref Guid iid, [MarshalAs(UnmanagedType.IInspectable)] out object factory);

		// Token: 0x02000ABE RID: 2750
		[SecurityCritical]
		[SuppressUnmanagedCodeSecurity]
		internal static class ManifestEtw
		{
			// Token: 0x0600699F RID: 27039
			[SecurityCritical]
			[DllImport("advapi32.dll", CharSet = CharSet.Unicode, ExactSpelling = true)]
			internal unsafe static extern uint EventRegister([In] ref Guid providerId, [In] UnsafeNativeMethods.ManifestEtw.EtwEnableCallback enableCallback, [In] void* callbackContext, [In] [Out] ref long registrationHandle);

			// Token: 0x060069A0 RID: 27040
			[SecurityCritical]
			[DllImport("advapi32.dll", CharSet = CharSet.Unicode, ExactSpelling = true)]
			internal static extern uint EventUnregister([In] long registrationHandle);

			// Token: 0x060069A1 RID: 27041
			[SecurityCritical]
			[DllImport("advapi32.dll", CharSet = CharSet.Unicode, ExactSpelling = true)]
			internal unsafe static extern int EventWrite([In] long registrationHandle, [In] ref EventDescriptor eventDescriptor, [In] int userDataCount, [In] EventProvider.EventData* userData);

			// Token: 0x060069A2 RID: 27042
			[SecurityCritical]
			[DllImport("advapi32.dll", CharSet = CharSet.Unicode, ExactSpelling = true)]
			internal static extern int EventWriteString([In] long registrationHandle, [In] byte level, [In] long keyword, [In] string msg);

			// Token: 0x060069A3 RID: 27043 RVA: 0x0016B938 File Offset: 0x00169B38
			internal unsafe static int EventWriteTransferWrapper(long registrationHandle, ref EventDescriptor eventDescriptor, Guid* activityId, Guid* relatedActivityId, int userDataCount, EventProvider.EventData* userData)
			{
				int num = UnsafeNativeMethods.ManifestEtw.EventWriteTransfer(registrationHandle, ref eventDescriptor, activityId, relatedActivityId, userDataCount, userData);
				if (num == 87 && relatedActivityId == null)
				{
					Guid empty = Guid.Empty;
					num = UnsafeNativeMethods.ManifestEtw.EventWriteTransfer(registrationHandle, ref eventDescriptor, activityId, &empty, userDataCount, userData);
				}
				return num;
			}

			// Token: 0x060069A4 RID: 27044
			[SuppressUnmanagedCodeSecurity]
			[DllImport("advapi32.dll", CharSet = CharSet.Unicode, ExactSpelling = true)]
			private unsafe static extern int EventWriteTransfer([In] long registrationHandle, [In] ref EventDescriptor eventDescriptor, [In] Guid* activityId, [In] Guid* relatedActivityId, [In] int userDataCount, [In] EventProvider.EventData* userData);

			// Token: 0x060069A5 RID: 27045
			[SuppressUnmanagedCodeSecurity]
			[DllImport("advapi32.dll", CharSet = CharSet.Unicode, ExactSpelling = true)]
			internal static extern int EventActivityIdControl([In] UnsafeNativeMethods.ManifestEtw.ActivityControl ControlCode, [In] [Out] ref Guid ActivityId);

			// Token: 0x060069A6 RID: 27046
			[SuppressUnmanagedCodeSecurity]
			[DllImport("advapi32.dll", CharSet = CharSet.Unicode, ExactSpelling = true)]
			internal unsafe static extern int EventSetInformation([In] long registrationHandle, [In] UnsafeNativeMethods.ManifestEtw.EVENT_INFO_CLASS informationClass, [In] void* eventInformation, [In] int informationLength);

			// Token: 0x060069A7 RID: 27047
			[SuppressUnmanagedCodeSecurity]
			[DllImport("advapi32.dll", CharSet = CharSet.Unicode, ExactSpelling = true)]
			internal unsafe static extern int EnumerateTraceGuidsEx(UnsafeNativeMethods.ManifestEtw.TRACE_QUERY_INFO_CLASS TraceQueryInfoClass, void* InBuffer, int InBufferSize, void* OutBuffer, int OutBufferSize, ref int ReturnLength);

			// Token: 0x040030B8 RID: 12472
			internal const int ERROR_ARITHMETIC_OVERFLOW = 534;

			// Token: 0x040030B9 RID: 12473
			internal const int ERROR_NOT_ENOUGH_MEMORY = 8;

			// Token: 0x040030BA RID: 12474
			internal const int ERROR_MORE_DATA = 234;

			// Token: 0x040030BB RID: 12475
			internal const int ERROR_NOT_SUPPORTED = 50;

			// Token: 0x040030BC RID: 12476
			internal const int ERROR_INVALID_PARAMETER = 87;

			// Token: 0x040030BD RID: 12477
			internal const int EVENT_CONTROL_CODE_DISABLE_PROVIDER = 0;

			// Token: 0x040030BE RID: 12478
			internal const int EVENT_CONTROL_CODE_ENABLE_PROVIDER = 1;

			// Token: 0x040030BF RID: 12479
			internal const int EVENT_CONTROL_CODE_CAPTURE_STATE = 2;

			// Token: 0x02000CF5 RID: 3317
			// (Invoke) Token: 0x060071DC RID: 29148
			[SecurityCritical]
			internal unsafe delegate void EtwEnableCallback([In] ref Guid sourceId, [In] int isEnabled, [In] byte level, [In] long matchAnyKeywords, [In] long matchAllKeywords, [In] UnsafeNativeMethods.ManifestEtw.EVENT_FILTER_DESCRIPTOR* filterData, [In] void* callbackContext);

			// Token: 0x02000CF6 RID: 3318
			internal struct EVENT_FILTER_DESCRIPTOR
			{
				// Token: 0x04003900 RID: 14592
				public long Ptr;

				// Token: 0x04003901 RID: 14593
				public int Size;

				// Token: 0x04003902 RID: 14594
				public int Type;
			}

			// Token: 0x02000CF7 RID: 3319
			internal enum ActivityControl : uint
			{
				// Token: 0x04003904 RID: 14596
				EVENT_ACTIVITY_CTRL_GET_ID = 1U,
				// Token: 0x04003905 RID: 14597
				EVENT_ACTIVITY_CTRL_SET_ID,
				// Token: 0x04003906 RID: 14598
				EVENT_ACTIVITY_CTRL_CREATE_ID,
				// Token: 0x04003907 RID: 14599
				EVENT_ACTIVITY_CTRL_GET_SET_ID,
				// Token: 0x04003908 RID: 14600
				EVENT_ACTIVITY_CTRL_CREATE_SET_ID
			}

			// Token: 0x02000CF8 RID: 3320
			internal enum EVENT_INFO_CLASS
			{
				// Token: 0x0400390A RID: 14602
				BinaryTrackInfo,
				// Token: 0x0400390B RID: 14603
				SetEnableAllKeywords,
				// Token: 0x0400390C RID: 14604
				SetTraits
			}

			// Token: 0x02000CF9 RID: 3321
			internal enum TRACE_QUERY_INFO_CLASS
			{
				// Token: 0x0400390E RID: 14606
				TraceGuidQueryList,
				// Token: 0x0400390F RID: 14607
				TraceGuidQueryInfo,
				// Token: 0x04003910 RID: 14608
				TraceGuidQueryProcess,
				// Token: 0x04003911 RID: 14609
				TraceStackTracingInfo,
				// Token: 0x04003912 RID: 14610
				MaxTraceSetInfoClass
			}

			// Token: 0x02000CFA RID: 3322
			internal struct TRACE_GUID_INFO
			{
				// Token: 0x04003913 RID: 14611
				public int InstanceCount;

				// Token: 0x04003914 RID: 14612
				public int Reserved;
			}

			// Token: 0x02000CFB RID: 3323
			internal struct TRACE_PROVIDER_INSTANCE_INFO
			{
				// Token: 0x04003915 RID: 14613
				public int NextOffset;

				// Token: 0x04003916 RID: 14614
				public int EnableCount;

				// Token: 0x04003917 RID: 14615
				public int Pid;

				// Token: 0x04003918 RID: 14616
				public int Flags;
			}

			// Token: 0x02000CFC RID: 3324
			internal struct TRACE_ENABLE_INFO
			{
				// Token: 0x04003919 RID: 14617
				public int IsEnabled;

				// Token: 0x0400391A RID: 14618
				public byte Level;

				// Token: 0x0400391B RID: 14619
				public byte Reserved1;

				// Token: 0x0400391C RID: 14620
				public ushort LoggerId;

				// Token: 0x0400391D RID: 14621
				public int EnableProperty;

				// Token: 0x0400391E RID: 14622
				public int Reserved2;

				// Token: 0x0400391F RID: 14623
				public long MatchAnyKeyword;

				// Token: 0x04003920 RID: 14624
				public long MatchAllKeyword;
			}
		}
	}
}
