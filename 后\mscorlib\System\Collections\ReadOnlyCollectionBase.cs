﻿using System;
using System.Runtime.InteropServices;

namespace System.Collections
{
	// Token: 0x0200048E RID: 1166
	[ComVisible(true)]
	[Serializable]
	public abstract class ReadOnlyCollectionBase : ICollection, IEnumerable
	{
		// Token: 0x17000836 RID: 2102
		// (get) Token: 0x060037BD RID: 14269 RVA: 0x000D5D8B File Offset: 0x000D3F8B
		protected ArrayList InnerList
		{
			get
			{
				if (this.list == null)
				{
					this.list = new ArrayList();
				}
				return this.list;
			}
		}

		// Token: 0x17000837 RID: 2103
		// (get) Token: 0x060037BE RID: 14270 RVA: 0x000D5DA6 File Offset: 0x000D3FA6
		public virtual int Count
		{
			get
			{
				return this.InnerList.Count;
			}
		}

		// Token: 0x17000838 RID: 2104
		// (get) Token: 0x060037BF RID: 14271 RVA: 0x000D5DB3 File Offset: 0x000D3FB3
		bool ICollection.IsSynchronized
		{
			get
			{
				return this.InnerList.IsSynchronized;
			}
		}

		// Token: 0x17000839 RID: 2105
		// (get) Token: 0x060037C0 RID: 14272 RVA: 0x000D5DC0 File Offset: 0x000D3FC0
		object ICollection.SyncRoot
		{
			get
			{
				return this.InnerList.SyncRoot;
			}
		}

		// Token: 0x060037C1 RID: 14273 RVA: 0x000D5DCD File Offset: 0x000D3FCD
		void ICollection.CopyTo(Array array, int index)
		{
			this.InnerList.CopyTo(array, index);
		}

		// Token: 0x060037C2 RID: 14274 RVA: 0x000D5DDC File Offset: 0x000D3FDC
		public virtual IEnumerator GetEnumerator()
		{
			return this.InnerList.GetEnumerator();
		}

		// Token: 0x040018BB RID: 6331
		private ArrayList list;
	}
}
