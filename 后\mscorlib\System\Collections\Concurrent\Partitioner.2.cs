﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Security.Permissions;
using System.Threading;

namespace System.Collections.Concurrent
{
	// Token: 0x020004B4 RID: 1204
	[__DynamicallyInvokable]
	[HostProtection(SecurityAction.LinkDemand, Synchronization = true, ExternalThreading = true)]
	public static class Partitioner
	{
		// Token: 0x060039A7 RID: 14759 RVA: 0x000DC9F6 File Offset: 0x000DABF6
		[__DynamicallyInvokable]
		public static OrderablePartitioner<TSource> Create<TSource>(IList<TSource> list, bool loadBalance)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			if (loadBalance)
			{
				return new Partitioner.DynamicPartitionerForIList<TSource>(list);
			}
			return new Partitioner.StaticIndexRangePartitionerForIList<TSource>(list);
		}

		// Token: 0x060039A8 RID: 14760 RVA: 0x000DCA16 File Offset: 0x000DAC16
		[__DynamicallyInvokable]
		public static OrderablePartitioner<TSource> Create<TSource>(TSource[] array, bool loadBalance)
		{
			if (array == null)
			{
				throw new ArgumentNullException("array");
			}
			if (loadBalance)
			{
				return new Partitioner.DynamicPartitionerForArray<TSource>(array);
			}
			return new Partitioner.StaticIndexRangePartitionerForArray<TSource>(array);
		}

		// Token: 0x060039A9 RID: 14761 RVA: 0x000DCA36 File Offset: 0x000DAC36
		[__DynamicallyInvokable]
		public static OrderablePartitioner<TSource> Create<TSource>(IEnumerable<TSource> source)
		{
			return Partitioner.Create<TSource>(source, EnumerablePartitionerOptions.None);
		}

		// Token: 0x060039AA RID: 14762 RVA: 0x000DCA3F File Offset: 0x000DAC3F
		[__DynamicallyInvokable]
		public static OrderablePartitioner<TSource> Create<TSource>(IEnumerable<TSource> source, EnumerablePartitionerOptions partitionerOptions)
		{
			if (source == null)
			{
				throw new ArgumentNullException("source");
			}
			if ((partitionerOptions & ~EnumerablePartitionerOptions.NoBuffering) != EnumerablePartitionerOptions.None)
			{
				throw new ArgumentOutOfRangeException("partitionerOptions");
			}
			return new Partitioner.DynamicPartitionerForIEnumerable<TSource>(source, partitionerOptions);
		}

		// Token: 0x060039AB RID: 14763 RVA: 0x000DCA68 File Offset: 0x000DAC68
		[__DynamicallyInvokable]
		public static OrderablePartitioner<Tuple<long, long>> Create(long fromInclusive, long toExclusive)
		{
			int num = 3;
			if (toExclusive <= fromInclusive)
			{
				throw new ArgumentOutOfRangeException("toExclusive");
			}
			long num2 = (toExclusive - fromInclusive) / (long)(PlatformHelper.ProcessorCount * num);
			if (num2 == 0L)
			{
				num2 = 1L;
			}
			return Partitioner.Create<Tuple<long, long>>(Partitioner.CreateRanges(fromInclusive, toExclusive, num2), EnumerablePartitionerOptions.NoBuffering);
		}

		// Token: 0x060039AC RID: 14764 RVA: 0x000DCAA7 File Offset: 0x000DACA7
		[__DynamicallyInvokable]
		public static OrderablePartitioner<Tuple<long, long>> Create(long fromInclusive, long toExclusive, long rangeSize)
		{
			if (toExclusive <= fromInclusive)
			{
				throw new ArgumentOutOfRangeException("toExclusive");
			}
			if (rangeSize <= 0L)
			{
				throw new ArgumentOutOfRangeException("rangeSize");
			}
			return Partitioner.Create<Tuple<long, long>>(Partitioner.CreateRanges(fromInclusive, toExclusive, rangeSize), EnumerablePartitionerOptions.NoBuffering);
		}

		// Token: 0x060039AD RID: 14765 RVA: 0x000DCAD6 File Offset: 0x000DACD6
		private static IEnumerable<Tuple<long, long>> CreateRanges(long fromInclusive, long toExclusive, long rangeSize)
		{
			bool shouldQuit = false;
			long i = fromInclusive;
			while (i < toExclusive && !shouldQuit)
			{
				long num = i;
				long num2;
				try
				{
					num2 = checked(i + rangeSize);
				}
				catch (OverflowException)
				{
					num2 = toExclusive;
					shouldQuit = true;
				}
				if (num2 > toExclusive)
				{
					num2 = toExclusive;
				}
				yield return new Tuple<long, long>(num, num2);
				i += rangeSize;
			}
			yield break;
		}

		// Token: 0x060039AE RID: 14766 RVA: 0x000DCAF4 File Offset: 0x000DACF4
		[__DynamicallyInvokable]
		public static OrderablePartitioner<Tuple<int, int>> Create(int fromInclusive, int toExclusive)
		{
			int num = 3;
			if (toExclusive <= fromInclusive)
			{
				throw new ArgumentOutOfRangeException("toExclusive");
			}
			int num2 = (toExclusive - fromInclusive) / (PlatformHelper.ProcessorCount * num);
			if (num2 == 0)
			{
				num2 = 1;
			}
			return Partitioner.Create<Tuple<int, int>>(Partitioner.CreateRanges(fromInclusive, toExclusive, num2), EnumerablePartitionerOptions.NoBuffering);
		}

		// Token: 0x060039AF RID: 14767 RVA: 0x000DCB31 File Offset: 0x000DAD31
		[__DynamicallyInvokable]
		public static OrderablePartitioner<Tuple<int, int>> Create(int fromInclusive, int toExclusive, int rangeSize)
		{
			if (toExclusive <= fromInclusive)
			{
				throw new ArgumentOutOfRangeException("toExclusive");
			}
			if (rangeSize <= 0)
			{
				throw new ArgumentOutOfRangeException("rangeSize");
			}
			return Partitioner.Create<Tuple<int, int>>(Partitioner.CreateRanges(fromInclusive, toExclusive, rangeSize), EnumerablePartitionerOptions.NoBuffering);
		}

		// Token: 0x060039B0 RID: 14768 RVA: 0x000DCB5F File Offset: 0x000DAD5F
		private static IEnumerable<Tuple<int, int>> CreateRanges(int fromInclusive, int toExclusive, int rangeSize)
		{
			bool shouldQuit = false;
			int i = fromInclusive;
			while (i < toExclusive && !shouldQuit)
			{
				int num = i;
				int num2;
				try
				{
					num2 = checked(i + rangeSize);
				}
				catch (OverflowException)
				{
					num2 = toExclusive;
					shouldQuit = true;
				}
				if (num2 > toExclusive)
				{
					num2 = toExclusive;
				}
				yield return new Tuple<int, int>(num, num2);
				i += rangeSize;
			}
			yield break;
		}

		// Token: 0x060039B1 RID: 14769 RVA: 0x000DCB80 File Offset: 0x000DAD80
		private static int GetDefaultChunkSize<TSource>()
		{
			int num;
			if (typeof(TSource).IsValueType)
			{
				if (typeof(TSource).StructLayoutAttribute.Value == LayoutKind.Explicit)
				{
					num = Math.Max(1, 512 / Marshal.SizeOf(typeof(TSource)));
				}
				else
				{
					num = 128;
				}
			}
			else
			{
				num = 512 / IntPtr.Size;
			}
			return num;
		}

		// Token: 0x04001933 RID: 6451
		private const int DEFAULT_BYTES_PER_CHUNK = 512;

		// Token: 0x02000BCD RID: 3021
		private abstract class DynamicPartitionEnumerator_Abstract<TSource, TSourceReader> : IEnumerator<KeyValuePair<long, TSource>>, IDisposable, IEnumerator
		{
			// Token: 0x06006EAC RID: 28332 RVA: 0x0017DBC7 File Offset: 0x0017BDC7
			protected DynamicPartitionEnumerator_Abstract(TSourceReader sharedReader, Partitioner.SharedLong sharedIndex)
				: this(sharedReader, sharedIndex, false)
			{
			}

			// Token: 0x06006EAD RID: 28333 RVA: 0x0017DBD2 File Offset: 0x0017BDD2
			protected DynamicPartitionEnumerator_Abstract(TSourceReader sharedReader, Partitioner.SharedLong sharedIndex, bool useSingleChunking)
			{
				this.m_sharedReader = sharedReader;
				this.m_sharedIndex = sharedIndex;
				this.m_maxChunkSize = (useSingleChunking ? 1 : Partitioner.DynamicPartitionEnumerator_Abstract<TSource, TSourceReader>.s_defaultMaxChunkSize);
			}

			// Token: 0x06006EAE RID: 28334
			protected abstract bool GrabNextChunk(int requestedChunkSize);

			// Token: 0x170012EB RID: 4843
			// (get) Token: 0x06006EAF RID: 28335
			// (set) Token: 0x06006EB0 RID: 28336
			protected abstract bool HasNoElementsLeft { get; set; }

			// Token: 0x170012EC RID: 4844
			// (get) Token: 0x06006EB1 RID: 28337
			public abstract KeyValuePair<long, TSource> Current { get; }

			// Token: 0x06006EB2 RID: 28338
			public abstract void Dispose();

			// Token: 0x06006EB3 RID: 28339 RVA: 0x0017DBF9 File Offset: 0x0017BDF9
			public void Reset()
			{
				throw new NotSupportedException();
			}

			// Token: 0x170012ED RID: 4845
			// (get) Token: 0x06006EB4 RID: 28340 RVA: 0x0017DC00 File Offset: 0x0017BE00
			object IEnumerator.Current
			{
				get
				{
					return this.Current;
				}
			}

			// Token: 0x06006EB5 RID: 28341 RVA: 0x0017DC10 File Offset: 0x0017BE10
			public bool MoveNext()
			{
				if (this.m_localOffset == null)
				{
					this.m_localOffset = new Partitioner.SharedInt(-1);
					this.m_currentChunkSize = new Partitioner.SharedInt(0);
					this.m_doublingCountdown = 3;
				}
				if (this.m_localOffset.Value < this.m_currentChunkSize.Value - 1)
				{
					this.m_localOffset.Value++;
					return true;
				}
				int num;
				if (this.m_currentChunkSize.Value == 0)
				{
					num = 1;
				}
				else if (this.m_doublingCountdown > 0)
				{
					num = this.m_currentChunkSize.Value;
				}
				else
				{
					num = Math.Min(this.m_currentChunkSize.Value * 2, this.m_maxChunkSize);
					this.m_doublingCountdown = 3;
				}
				this.m_doublingCountdown--;
				if (this.GrabNextChunk(num))
				{
					this.m_localOffset.Value = 0;
					return true;
				}
				return false;
			}

			// Token: 0x040035C7 RID: 13767
			protected readonly TSourceReader m_sharedReader;

			// Token: 0x040035C8 RID: 13768
			protected static int s_defaultMaxChunkSize = Partitioner.GetDefaultChunkSize<TSource>();

			// Token: 0x040035C9 RID: 13769
			protected Partitioner.SharedInt m_currentChunkSize;

			// Token: 0x040035CA RID: 13770
			protected Partitioner.SharedInt m_localOffset;

			// Token: 0x040035CB RID: 13771
			private const int CHUNK_DOUBLING_RATE = 3;

			// Token: 0x040035CC RID: 13772
			private int m_doublingCountdown;

			// Token: 0x040035CD RID: 13773
			protected readonly int m_maxChunkSize;

			// Token: 0x040035CE RID: 13774
			protected readonly Partitioner.SharedLong m_sharedIndex;
		}

		// Token: 0x02000BCE RID: 3022
		private class DynamicPartitionerForIEnumerable<TSource> : OrderablePartitioner<TSource>
		{
			// Token: 0x06006EB7 RID: 28343 RVA: 0x0017DCFD File Offset: 0x0017BEFD
			internal DynamicPartitionerForIEnumerable(IEnumerable<TSource> source, EnumerablePartitionerOptions partitionerOptions)
				: base(true, false, true)
			{
				this.m_source = source;
				this.m_useSingleChunking = (partitionerOptions & EnumerablePartitionerOptions.NoBuffering) > EnumerablePartitionerOptions.None;
			}

			// Token: 0x06006EB8 RID: 28344 RVA: 0x0017DD1C File Offset: 0x0017BF1C
			public override IList<IEnumerator<KeyValuePair<long, TSource>>> GetOrderablePartitions(int partitionCount)
			{
				if (partitionCount <= 0)
				{
					throw new ArgumentOutOfRangeException("partitionCount");
				}
				IEnumerator<KeyValuePair<long, TSource>>[] array = new IEnumerator<KeyValuePair<long, TSource>>[partitionCount];
				IEnumerable<KeyValuePair<long, TSource>> enumerable = new Partitioner.DynamicPartitionerForIEnumerable<TSource>.InternalPartitionEnumerable(this.m_source.GetEnumerator(), this.m_useSingleChunking, true);
				for (int i = 0; i < partitionCount; i++)
				{
					array[i] = enumerable.GetEnumerator();
				}
				return array;
			}

			// Token: 0x06006EB9 RID: 28345 RVA: 0x0017DD6D File Offset: 0x0017BF6D
			public override IEnumerable<KeyValuePair<long, TSource>> GetOrderableDynamicPartitions()
			{
				return new Partitioner.DynamicPartitionerForIEnumerable<TSource>.InternalPartitionEnumerable(this.m_source.GetEnumerator(), this.m_useSingleChunking, false);
			}

			// Token: 0x170012EE RID: 4846
			// (get) Token: 0x06006EBA RID: 28346 RVA: 0x0017DD86 File Offset: 0x0017BF86
			public override bool SupportsDynamicPartitions
			{
				get
				{
					return true;
				}
			}

			// Token: 0x040035CF RID: 13775
			private IEnumerable<TSource> m_source;

			// Token: 0x040035D0 RID: 13776
			private readonly bool m_useSingleChunking;

			// Token: 0x02000D0A RID: 3338
			private class InternalPartitionEnumerable : IEnumerable<KeyValuePair<long, TSource>>, IEnumerable, IDisposable
			{
				// Token: 0x0600720E RID: 29198 RVA: 0x00188E0C File Offset: 0x0018700C
				internal InternalPartitionEnumerable(IEnumerator<TSource> sharedReader, bool useSingleChunking, bool isStaticPartitioning)
				{
					this.m_sharedReader = sharedReader;
					this.m_sharedIndex = new Partitioner.SharedLong(-1L);
					this.m_hasNoElementsLeft = new Partitioner.SharedBool(false);
					this.m_sourceDepleted = new Partitioner.SharedBool(false);
					this.m_sharedLock = new object();
					this.m_useSingleChunking = useSingleChunking;
					if (!this.m_useSingleChunking)
					{
						int num = ((PlatformHelper.ProcessorCount > 4) ? 4 : 1);
						this.m_FillBuffer = new KeyValuePair<long, TSource>[num * Partitioner.GetDefaultChunkSize<TSource>()];
					}
					if (isStaticPartitioning)
					{
						this.m_activePartitionCount = new Partitioner.SharedInt(0);
						return;
					}
					this.m_activePartitionCount = null;
				}

				// Token: 0x0600720F RID: 29199 RVA: 0x00188EA0 File Offset: 0x001870A0
				public IEnumerator<KeyValuePair<long, TSource>> GetEnumerator()
				{
					if (this.m_disposed)
					{
						throw new ObjectDisposedException(Environment.GetResourceString("PartitionerStatic_CanNotCallGetEnumeratorAfterSourceHasBeenDisposed"));
					}
					return new Partitioner.DynamicPartitionerForIEnumerable<TSource>.InternalPartitionEnumerator(this.m_sharedReader, this.m_sharedIndex, this.m_hasNoElementsLeft, this.m_sharedLock, this.m_activePartitionCount, this, this.m_useSingleChunking);
				}

				// Token: 0x06007210 RID: 29200 RVA: 0x00188EEF File Offset: 0x001870EF
				IEnumerator IEnumerable.GetEnumerator()
				{
					return this.GetEnumerator();
				}

				// Token: 0x06007211 RID: 29201 RVA: 0x00188EF8 File Offset: 0x001870F8
				private void TryCopyFromFillBuffer(KeyValuePair<long, TSource>[] destArray, int requestedChunkSize, ref int actualNumElementsGrabbed)
				{
					actualNumElementsGrabbed = 0;
					KeyValuePair<long, TSource>[] fillBuffer = this.m_FillBuffer;
					if (fillBuffer == null)
					{
						return;
					}
					if (this.m_FillBufferCurrentPosition >= this.m_FillBufferSize)
					{
						return;
					}
					Interlocked.Increment(ref this.m_activeCopiers);
					int num = Interlocked.Add(ref this.m_FillBufferCurrentPosition, requestedChunkSize);
					int num2 = num - requestedChunkSize;
					if (num2 < this.m_FillBufferSize)
					{
						actualNumElementsGrabbed = ((num < this.m_FillBufferSize) ? num : (this.m_FillBufferSize - num2));
						Array.Copy(fillBuffer, num2, destArray, 0, actualNumElementsGrabbed);
					}
					Interlocked.Decrement(ref this.m_activeCopiers);
				}

				// Token: 0x06007212 RID: 29202 RVA: 0x00188F81 File Offset: 0x00187181
				internal bool GrabChunk(KeyValuePair<long, TSource>[] destArray, int requestedChunkSize, ref int actualNumElementsGrabbed)
				{
					actualNumElementsGrabbed = 0;
					if (this.m_hasNoElementsLeft.Value)
					{
						return false;
					}
					if (this.m_useSingleChunking)
					{
						return this.GrabChunk_Single(destArray, requestedChunkSize, ref actualNumElementsGrabbed);
					}
					return this.GrabChunk_Buffered(destArray, requestedChunkSize, ref actualNumElementsGrabbed);
				}

				// Token: 0x06007213 RID: 29203 RVA: 0x00188FB4 File Offset: 0x001871B4
				internal bool GrabChunk_Single(KeyValuePair<long, TSource>[] destArray, int requestedChunkSize, ref int actualNumElementsGrabbed)
				{
					object sharedLock = this.m_sharedLock;
					bool flag2;
					lock (sharedLock)
					{
						if (this.m_hasNoElementsLeft.Value)
						{
							flag2 = false;
						}
						else
						{
							try
							{
								if (this.m_sharedReader.MoveNext())
								{
									this.m_sharedIndex.Value = checked(this.m_sharedIndex.Value + 1L);
									destArray[0] = new KeyValuePair<long, TSource>(this.m_sharedIndex.Value, this.m_sharedReader.Current);
									actualNumElementsGrabbed = 1;
									flag2 = true;
								}
								else
								{
									this.m_sourceDepleted.Value = true;
									this.m_hasNoElementsLeft.Value = true;
									flag2 = false;
								}
							}
							catch
							{
								this.m_sourceDepleted.Value = true;
								this.m_hasNoElementsLeft.Value = true;
								throw;
							}
						}
					}
					return flag2;
				}

				// Token: 0x06007214 RID: 29204 RVA: 0x001890A0 File Offset: 0x001872A0
				internal bool GrabChunk_Buffered(KeyValuePair<long, TSource>[] destArray, int requestedChunkSize, ref int actualNumElementsGrabbed)
				{
					this.TryCopyFromFillBuffer(destArray, requestedChunkSize, ref actualNumElementsGrabbed);
					if (actualNumElementsGrabbed == requestedChunkSize)
					{
						return true;
					}
					if (this.m_sourceDepleted.Value)
					{
						this.m_hasNoElementsLeft.Value = true;
						this.m_FillBuffer = null;
						return actualNumElementsGrabbed > 0;
					}
					object sharedLock = this.m_sharedLock;
					lock (sharedLock)
					{
						if (this.m_sourceDepleted.Value)
						{
							return actualNumElementsGrabbed > 0;
						}
						try
						{
							if (this.m_activeCopiers > 0)
							{
								SpinWait spinWait = default(SpinWait);
								while (this.m_activeCopiers > 0)
								{
									spinWait.SpinOnce();
								}
							}
							while (actualNumElementsGrabbed < requestedChunkSize)
							{
								if (!this.m_sharedReader.MoveNext())
								{
									this.m_sourceDepleted.Value = true;
									break;
								}
								this.m_sharedIndex.Value = checked(this.m_sharedIndex.Value + 1L);
								destArray[actualNumElementsGrabbed] = new KeyValuePair<long, TSource>(this.m_sharedIndex.Value, this.m_sharedReader.Current);
								actualNumElementsGrabbed++;
							}
							KeyValuePair<long, TSource>[] fillBuffer = this.m_FillBuffer;
							if (!this.m_sourceDepleted.Value && fillBuffer != null && this.m_FillBufferCurrentPosition >= fillBuffer.Length)
							{
								for (int i = 0; i < fillBuffer.Length; i++)
								{
									if (!this.m_sharedReader.MoveNext())
									{
										this.m_sourceDepleted.Value = true;
										this.m_FillBufferSize = i;
										break;
									}
									this.m_sharedIndex.Value = checked(this.m_sharedIndex.Value + 1L);
									fillBuffer[i] = new KeyValuePair<long, TSource>(this.m_sharedIndex.Value, this.m_sharedReader.Current);
								}
								this.m_FillBufferCurrentPosition = 0;
							}
						}
						catch
						{
							this.m_sourceDepleted.Value = true;
							this.m_hasNoElementsLeft.Value = true;
							throw;
						}
					}
					return actualNumElementsGrabbed > 0;
				}

				// Token: 0x06007215 RID: 29205 RVA: 0x001892BC File Offset: 0x001874BC
				public void Dispose()
				{
					if (!this.m_disposed)
					{
						this.m_disposed = true;
						this.m_sharedReader.Dispose();
					}
				}

				// Token: 0x04003953 RID: 14675
				private readonly IEnumerator<TSource> m_sharedReader;

				// Token: 0x04003954 RID: 14676
				private Partitioner.SharedLong m_sharedIndex;

				// Token: 0x04003955 RID: 14677
				private volatile KeyValuePair<long, TSource>[] m_FillBuffer;

				// Token: 0x04003956 RID: 14678
				private volatile int m_FillBufferSize;

				// Token: 0x04003957 RID: 14679
				private volatile int m_FillBufferCurrentPosition;

				// Token: 0x04003958 RID: 14680
				private volatile int m_activeCopiers;

				// Token: 0x04003959 RID: 14681
				private Partitioner.SharedBool m_hasNoElementsLeft;

				// Token: 0x0400395A RID: 14682
				private Partitioner.SharedBool m_sourceDepleted;

				// Token: 0x0400395B RID: 14683
				private object m_sharedLock;

				// Token: 0x0400395C RID: 14684
				private bool m_disposed;

				// Token: 0x0400395D RID: 14685
				private Partitioner.SharedInt m_activePartitionCount;

				// Token: 0x0400395E RID: 14686
				private readonly bool m_useSingleChunking;
			}

			// Token: 0x02000D0B RID: 3339
			private class InternalPartitionEnumerator : Partitioner.DynamicPartitionEnumerator_Abstract<TSource, IEnumerator<TSource>>
			{
				// Token: 0x06007216 RID: 29206 RVA: 0x001892D8 File Offset: 0x001874D8
				internal InternalPartitionEnumerator(IEnumerator<TSource> sharedReader, Partitioner.SharedLong sharedIndex, Partitioner.SharedBool hasNoElementsLeft, object sharedLock, Partitioner.SharedInt activePartitionCount, Partitioner.DynamicPartitionerForIEnumerable<TSource>.InternalPartitionEnumerable enumerable, bool useSingleChunking)
					: base(sharedReader, sharedIndex, useSingleChunking)
				{
					this.m_hasNoElementsLeft = hasNoElementsLeft;
					this.m_sharedLock = sharedLock;
					this.m_enumerable = enumerable;
					this.m_activePartitionCount = activePartitionCount;
					if (this.m_activePartitionCount != null)
					{
						Interlocked.Increment(ref this.m_activePartitionCount.Value);
					}
				}

				// Token: 0x06007217 RID: 29207 RVA: 0x00189328 File Offset: 0x00187528
				protected override bool GrabNextChunk(int requestedChunkSize)
				{
					if (this.HasNoElementsLeft)
					{
						return false;
					}
					if (this.m_localList == null)
					{
						this.m_localList = new KeyValuePair<long, TSource>[this.m_maxChunkSize];
					}
					return this.m_enumerable.GrabChunk(this.m_localList, requestedChunkSize, ref this.m_currentChunkSize.Value);
				}

				// Token: 0x17001386 RID: 4998
				// (get) Token: 0x06007218 RID: 29208 RVA: 0x00189375 File Offset: 0x00187575
				// (set) Token: 0x06007219 RID: 29209 RVA: 0x00189384 File Offset: 0x00187584
				protected override bool HasNoElementsLeft
				{
					get
					{
						return this.m_hasNoElementsLeft.Value;
					}
					set
					{
						this.m_hasNoElementsLeft.Value = true;
					}
				}

				// Token: 0x17001387 RID: 4999
				// (get) Token: 0x0600721A RID: 29210 RVA: 0x00189394 File Offset: 0x00187594
				public override KeyValuePair<long, TSource> Current
				{
					get
					{
						if (this.m_currentChunkSize == null)
						{
							throw new InvalidOperationException(Environment.GetResourceString("PartitionerStatic_CurrentCalledBeforeMoveNext"));
						}
						return this.m_localList[this.m_localOffset.Value];
					}
				}

				// Token: 0x0600721B RID: 29211 RVA: 0x001893C6 File Offset: 0x001875C6
				public override void Dispose()
				{
					if (this.m_activePartitionCount != null && Interlocked.Decrement(ref this.m_activePartitionCount.Value) == 0)
					{
						this.m_enumerable.Dispose();
					}
				}

				// Token: 0x0400395F RID: 14687
				private KeyValuePair<long, TSource>[] m_localList;

				// Token: 0x04003960 RID: 14688
				private readonly Partitioner.SharedBool m_hasNoElementsLeft;

				// Token: 0x04003961 RID: 14689
				private readonly object m_sharedLock;

				// Token: 0x04003962 RID: 14690
				private readonly Partitioner.SharedInt m_activePartitionCount;

				// Token: 0x04003963 RID: 14691
				private Partitioner.DynamicPartitionerForIEnumerable<TSource>.InternalPartitionEnumerable m_enumerable;
			}
		}

		// Token: 0x02000BCF RID: 3023
		private abstract class DynamicPartitionerForIndexRange_Abstract<TSource, TCollection> : OrderablePartitioner<TSource>
		{
			// Token: 0x06006EBB RID: 28347 RVA: 0x0017DD89 File Offset: 0x0017BF89
			protected DynamicPartitionerForIndexRange_Abstract(TCollection data)
				: base(true, false, true)
			{
				this.m_data = data;
			}

			// Token: 0x06006EBC RID: 28348
			protected abstract IEnumerable<KeyValuePair<long, TSource>> GetOrderableDynamicPartitions_Factory(TCollection data);

			// Token: 0x06006EBD RID: 28349 RVA: 0x0017DD9C File Offset: 0x0017BF9C
			public override IList<IEnumerator<KeyValuePair<long, TSource>>> GetOrderablePartitions(int partitionCount)
			{
				if (partitionCount <= 0)
				{
					throw new ArgumentOutOfRangeException("partitionCount");
				}
				IEnumerator<KeyValuePair<long, TSource>>[] array = new IEnumerator<KeyValuePair<long, TSource>>[partitionCount];
				IEnumerable<KeyValuePair<long, TSource>> orderableDynamicPartitions_Factory = this.GetOrderableDynamicPartitions_Factory(this.m_data);
				for (int i = 0; i < partitionCount; i++)
				{
					array[i] = orderableDynamicPartitions_Factory.GetEnumerator();
				}
				return array;
			}

			// Token: 0x06006EBE RID: 28350 RVA: 0x0017DDE2 File Offset: 0x0017BFE2
			public override IEnumerable<KeyValuePair<long, TSource>> GetOrderableDynamicPartitions()
			{
				return this.GetOrderableDynamicPartitions_Factory(this.m_data);
			}

			// Token: 0x170012EF RID: 4847
			// (get) Token: 0x06006EBF RID: 28351 RVA: 0x0017DDF0 File Offset: 0x0017BFF0
			public override bool SupportsDynamicPartitions
			{
				get
				{
					return true;
				}
			}

			// Token: 0x040035D1 RID: 13777
			private TCollection m_data;
		}

		// Token: 0x02000BD0 RID: 3024
		private abstract class DynamicPartitionEnumeratorForIndexRange_Abstract<TSource, TSourceReader> : Partitioner.DynamicPartitionEnumerator_Abstract<TSource, TSourceReader>
		{
			// Token: 0x06006EC0 RID: 28352 RVA: 0x0017DDF3 File Offset: 0x0017BFF3
			protected DynamicPartitionEnumeratorForIndexRange_Abstract(TSourceReader sharedReader, Partitioner.SharedLong sharedIndex)
				: base(sharedReader, sharedIndex)
			{
			}

			// Token: 0x170012F0 RID: 4848
			// (get) Token: 0x06006EC1 RID: 28353
			protected abstract int SourceCount { get; }

			// Token: 0x06006EC2 RID: 28354 RVA: 0x0017DE00 File Offset: 0x0017C000
			protected override bool GrabNextChunk(int requestedChunkSize)
			{
				while (!this.HasNoElementsLeft)
				{
					long num = Volatile.Read(ref this.m_sharedIndex.Value);
					if (this.HasNoElementsLeft)
					{
						return false;
					}
					long num2 = Math.Min((long)(this.SourceCount - 1), num + (long)requestedChunkSize);
					if (Interlocked.CompareExchange(ref this.m_sharedIndex.Value, num2, num) == num)
					{
						this.m_currentChunkSize.Value = (int)(num2 - num);
						this.m_localOffset.Value = -1;
						this.m_startIndex = (int)(num + 1L);
						return true;
					}
				}
				return false;
			}

			// Token: 0x170012F1 RID: 4849
			// (get) Token: 0x06006EC3 RID: 28355 RVA: 0x0017DE87 File Offset: 0x0017C087
			// (set) Token: 0x06006EC4 RID: 28356 RVA: 0x0017DEA7 File Offset: 0x0017C0A7
			protected override bool HasNoElementsLeft
			{
				get
				{
					return Volatile.Read(ref this.m_sharedIndex.Value) >= (long)(this.SourceCount - 1);
				}
				set
				{
				}
			}

			// Token: 0x06006EC5 RID: 28357 RVA: 0x0017DEA9 File Offset: 0x0017C0A9
			public override void Dispose()
			{
			}

			// Token: 0x040035D2 RID: 13778
			protected int m_startIndex;
		}

		// Token: 0x02000BD1 RID: 3025
		private class DynamicPartitionerForIList<TSource> : Partitioner.DynamicPartitionerForIndexRange_Abstract<TSource, IList<TSource>>
		{
			// Token: 0x06006EC6 RID: 28358 RVA: 0x0017DEAB File Offset: 0x0017C0AB
			internal DynamicPartitionerForIList(IList<TSource> source)
				: base(source)
			{
			}

			// Token: 0x06006EC7 RID: 28359 RVA: 0x0017DEB4 File Offset: 0x0017C0B4
			protected override IEnumerable<KeyValuePair<long, TSource>> GetOrderableDynamicPartitions_Factory(IList<TSource> m_data)
			{
				return new Partitioner.DynamicPartitionerForIList<TSource>.InternalPartitionEnumerable(m_data);
			}

			// Token: 0x02000D0C RID: 3340
			private class InternalPartitionEnumerable : IEnumerable<KeyValuePair<long, TSource>>, IEnumerable
			{
				// Token: 0x0600721C RID: 29212 RVA: 0x001893ED File Offset: 0x001875ED
				internal InternalPartitionEnumerable(IList<TSource> sharedReader)
				{
					this.m_sharedReader = sharedReader;
					this.m_sharedIndex = new Partitioner.SharedLong(-1L);
				}

				// Token: 0x0600721D RID: 29213 RVA: 0x00189409 File Offset: 0x00187609
				public IEnumerator<KeyValuePair<long, TSource>> GetEnumerator()
				{
					return new Partitioner.DynamicPartitionerForIList<TSource>.InternalPartitionEnumerator(this.m_sharedReader, this.m_sharedIndex);
				}

				// Token: 0x0600721E RID: 29214 RVA: 0x0018941C File Offset: 0x0018761C
				IEnumerator IEnumerable.GetEnumerator()
				{
					return this.GetEnumerator();
				}

				// Token: 0x04003964 RID: 14692
				private readonly IList<TSource> m_sharedReader;

				// Token: 0x04003965 RID: 14693
				private Partitioner.SharedLong m_sharedIndex;
			}

			// Token: 0x02000D0D RID: 3341
			private class InternalPartitionEnumerator : Partitioner.DynamicPartitionEnumeratorForIndexRange_Abstract<TSource, IList<TSource>>
			{
				// Token: 0x0600721F RID: 29215 RVA: 0x00189424 File Offset: 0x00187624
				internal InternalPartitionEnumerator(IList<TSource> sharedReader, Partitioner.SharedLong sharedIndex)
					: base(sharedReader, sharedIndex)
				{
				}

				// Token: 0x17001388 RID: 5000
				// (get) Token: 0x06007220 RID: 29216 RVA: 0x0018942E File Offset: 0x0018762E
				protected override int SourceCount
				{
					get
					{
						return this.m_sharedReader.Count;
					}
				}

				// Token: 0x17001389 RID: 5001
				// (get) Token: 0x06007221 RID: 29217 RVA: 0x0018943C File Offset: 0x0018763C
				public override KeyValuePair<long, TSource> Current
				{
					get
					{
						if (this.m_currentChunkSize == null)
						{
							throw new InvalidOperationException(Environment.GetResourceString("PartitionerStatic_CurrentCalledBeforeMoveNext"));
						}
						return new KeyValuePair<long, TSource>((long)(this.m_startIndex + this.m_localOffset.Value), this.m_sharedReader[this.m_startIndex + this.m_localOffset.Value]);
					}
				}
			}
		}

		// Token: 0x02000BD2 RID: 3026
		private class DynamicPartitionerForArray<TSource> : Partitioner.DynamicPartitionerForIndexRange_Abstract<TSource, TSource[]>
		{
			// Token: 0x06006EC8 RID: 28360 RVA: 0x0017DEBC File Offset: 0x0017C0BC
			internal DynamicPartitionerForArray(TSource[] source)
				: base(source)
			{
			}

			// Token: 0x06006EC9 RID: 28361 RVA: 0x0017DEC5 File Offset: 0x0017C0C5
			protected override IEnumerable<KeyValuePair<long, TSource>> GetOrderableDynamicPartitions_Factory(TSource[] m_data)
			{
				return new Partitioner.DynamicPartitionerForArray<TSource>.InternalPartitionEnumerable(m_data);
			}

			// Token: 0x02000D0E RID: 3342
			private class InternalPartitionEnumerable : IEnumerable<KeyValuePair<long, TSource>>, IEnumerable
			{
				// Token: 0x06007222 RID: 29218 RVA: 0x0018949A File Offset: 0x0018769A
				internal InternalPartitionEnumerable(TSource[] sharedReader)
				{
					this.m_sharedReader = sharedReader;
					this.m_sharedIndex = new Partitioner.SharedLong(-1L);
				}

				// Token: 0x06007223 RID: 29219 RVA: 0x001894B6 File Offset: 0x001876B6
				IEnumerator IEnumerable.GetEnumerator()
				{
					return this.GetEnumerator();
				}

				// Token: 0x06007224 RID: 29220 RVA: 0x001894BE File Offset: 0x001876BE
				public IEnumerator<KeyValuePair<long, TSource>> GetEnumerator()
				{
					return new Partitioner.DynamicPartitionerForArray<TSource>.InternalPartitionEnumerator(this.m_sharedReader, this.m_sharedIndex);
				}

				// Token: 0x04003966 RID: 14694
				private readonly TSource[] m_sharedReader;

				// Token: 0x04003967 RID: 14695
				private Partitioner.SharedLong m_sharedIndex;
			}

			// Token: 0x02000D0F RID: 3343
			private class InternalPartitionEnumerator : Partitioner.DynamicPartitionEnumeratorForIndexRange_Abstract<TSource, TSource[]>
			{
				// Token: 0x06007225 RID: 29221 RVA: 0x001894D1 File Offset: 0x001876D1
				internal InternalPartitionEnumerator(TSource[] sharedReader, Partitioner.SharedLong sharedIndex)
					: base(sharedReader, sharedIndex)
				{
				}

				// Token: 0x1700138A RID: 5002
				// (get) Token: 0x06007226 RID: 29222 RVA: 0x001894DB File Offset: 0x001876DB
				protected override int SourceCount
				{
					get
					{
						return this.m_sharedReader.Length;
					}
				}

				// Token: 0x1700138B RID: 5003
				// (get) Token: 0x06007227 RID: 29223 RVA: 0x001894E8 File Offset: 0x001876E8
				public override KeyValuePair<long, TSource> Current
				{
					get
					{
						if (this.m_currentChunkSize == null)
						{
							throw new InvalidOperationException(Environment.GetResourceString("PartitionerStatic_CurrentCalledBeforeMoveNext"));
						}
						return new KeyValuePair<long, TSource>((long)(this.m_startIndex + this.m_localOffset.Value), this.m_sharedReader[this.m_startIndex + this.m_localOffset.Value]);
					}
				}
			}
		}

		// Token: 0x02000BD3 RID: 3027
		private abstract class StaticIndexRangePartitioner<TSource, TCollection> : OrderablePartitioner<TSource>
		{
			// Token: 0x06006ECA RID: 28362 RVA: 0x0017DECD File Offset: 0x0017C0CD
			protected StaticIndexRangePartitioner()
				: base(true, true, true)
			{
			}

			// Token: 0x170012F2 RID: 4850
			// (get) Token: 0x06006ECB RID: 28363
			protected abstract int SourceCount { get; }

			// Token: 0x06006ECC RID: 28364
			protected abstract IEnumerator<KeyValuePair<long, TSource>> CreatePartition(int startIndex, int endIndex);

			// Token: 0x06006ECD RID: 28365 RVA: 0x0017DED8 File Offset: 0x0017C0D8
			public override IList<IEnumerator<KeyValuePair<long, TSource>>> GetOrderablePartitions(int partitionCount)
			{
				if (partitionCount <= 0)
				{
					throw new ArgumentOutOfRangeException("partitionCount");
				}
				int num2;
				int num = Math.DivRem(this.SourceCount, partitionCount, out num2);
				IEnumerator<KeyValuePair<long, TSource>>[] array = new IEnumerator<KeyValuePair<long, TSource>>[partitionCount];
				int num3 = -1;
				for (int i = 0; i < partitionCount; i++)
				{
					int num4 = num3 + 1;
					if (i < num2)
					{
						num3 = num4 + num;
					}
					else
					{
						num3 = num4 + num - 1;
					}
					array[i] = this.CreatePartition(num4, num3);
				}
				return array;
			}
		}

		// Token: 0x02000BD4 RID: 3028
		private abstract class StaticIndexRangePartition<TSource> : IEnumerator<KeyValuePair<long, TSource>>, IDisposable, IEnumerator
		{
			// Token: 0x06006ECE RID: 28366 RVA: 0x0017DF42 File Offset: 0x0017C142
			protected StaticIndexRangePartition(int startIndex, int endIndex)
			{
				this.m_startIndex = startIndex;
				this.m_endIndex = endIndex;
				this.m_offset = startIndex - 1;
			}

			// Token: 0x170012F3 RID: 4851
			// (get) Token: 0x06006ECF RID: 28367
			public abstract KeyValuePair<long, TSource> Current { get; }

			// Token: 0x06006ED0 RID: 28368 RVA: 0x0017DF63 File Offset: 0x0017C163
			public void Dispose()
			{
			}

			// Token: 0x06006ED1 RID: 28369 RVA: 0x0017DF65 File Offset: 0x0017C165
			public void Reset()
			{
				throw new NotSupportedException();
			}

			// Token: 0x06006ED2 RID: 28370 RVA: 0x0017DF6C File Offset: 0x0017C16C
			public bool MoveNext()
			{
				if (this.m_offset < this.m_endIndex)
				{
					this.m_offset++;
					return true;
				}
				this.m_offset = this.m_endIndex + 1;
				return false;
			}

			// Token: 0x170012F4 RID: 4852
			// (get) Token: 0x06006ED3 RID: 28371 RVA: 0x0017DFA3 File Offset: 0x0017C1A3
			object IEnumerator.Current
			{
				get
				{
					return this.Current;
				}
			}

			// Token: 0x040035D3 RID: 13779
			protected readonly int m_startIndex;

			// Token: 0x040035D4 RID: 13780
			protected readonly int m_endIndex;

			// Token: 0x040035D5 RID: 13781
			protected volatile int m_offset;
		}

		// Token: 0x02000BD5 RID: 3029
		private class StaticIndexRangePartitionerForIList<TSource> : Partitioner.StaticIndexRangePartitioner<TSource, IList<TSource>>
		{
			// Token: 0x06006ED4 RID: 28372 RVA: 0x0017DFB0 File Offset: 0x0017C1B0
			internal StaticIndexRangePartitionerForIList(IList<TSource> list)
			{
				this.m_list = list;
			}

			// Token: 0x170012F5 RID: 4853
			// (get) Token: 0x06006ED5 RID: 28373 RVA: 0x0017DFBF File Offset: 0x0017C1BF
			protected override int SourceCount
			{
				get
				{
					return this.m_list.Count;
				}
			}

			// Token: 0x06006ED6 RID: 28374 RVA: 0x0017DFCC File Offset: 0x0017C1CC
			protected override IEnumerator<KeyValuePair<long, TSource>> CreatePartition(int startIndex, int endIndex)
			{
				return new Partitioner.StaticIndexRangePartitionForIList<TSource>(this.m_list, startIndex, endIndex);
			}

			// Token: 0x040035D6 RID: 13782
			private IList<TSource> m_list;
		}

		// Token: 0x02000BD6 RID: 3030
		private class StaticIndexRangePartitionForIList<TSource> : Partitioner.StaticIndexRangePartition<TSource>
		{
			// Token: 0x06006ED7 RID: 28375 RVA: 0x0017DFDB File Offset: 0x0017C1DB
			internal StaticIndexRangePartitionForIList(IList<TSource> list, int startIndex, int endIndex)
				: base(startIndex, endIndex)
			{
				this.m_list = list;
			}

			// Token: 0x170012F6 RID: 4854
			// (get) Token: 0x06006ED8 RID: 28376 RVA: 0x0017DFF0 File Offset: 0x0017C1F0
			public override KeyValuePair<long, TSource> Current
			{
				get
				{
					if (this.m_offset < this.m_startIndex)
					{
						throw new InvalidOperationException(Environment.GetResourceString("PartitionerStatic_CurrentCalledBeforeMoveNext"));
					}
					return new KeyValuePair<long, TSource>((long)this.m_offset, this.m_list[this.m_offset]);
				}
			}

			// Token: 0x040035D7 RID: 13783
			private volatile IList<TSource> m_list;
		}

		// Token: 0x02000BD7 RID: 3031
		private class StaticIndexRangePartitionerForArray<TSource> : Partitioner.StaticIndexRangePartitioner<TSource, TSource[]>
		{
			// Token: 0x06006ED9 RID: 28377 RVA: 0x0017E040 File Offset: 0x0017C240
			internal StaticIndexRangePartitionerForArray(TSource[] array)
			{
				this.m_array = array;
			}

			// Token: 0x170012F7 RID: 4855
			// (get) Token: 0x06006EDA RID: 28378 RVA: 0x0017E04F File Offset: 0x0017C24F
			protected override int SourceCount
			{
				get
				{
					return this.m_array.Length;
				}
			}

			// Token: 0x06006EDB RID: 28379 RVA: 0x0017E059 File Offset: 0x0017C259
			protected override IEnumerator<KeyValuePair<long, TSource>> CreatePartition(int startIndex, int endIndex)
			{
				return new Partitioner.StaticIndexRangePartitionForArray<TSource>(this.m_array, startIndex, endIndex);
			}

			// Token: 0x040035D8 RID: 13784
			private TSource[] m_array;
		}

		// Token: 0x02000BD8 RID: 3032
		private class StaticIndexRangePartitionForArray<TSource> : Partitioner.StaticIndexRangePartition<TSource>
		{
			// Token: 0x06006EDC RID: 28380 RVA: 0x0017E068 File Offset: 0x0017C268
			internal StaticIndexRangePartitionForArray(TSource[] array, int startIndex, int endIndex)
				: base(startIndex, endIndex)
			{
				this.m_array = array;
			}

			// Token: 0x170012F8 RID: 4856
			// (get) Token: 0x06006EDD RID: 28381 RVA: 0x0017E07C File Offset: 0x0017C27C
			public override KeyValuePair<long, TSource> Current
			{
				get
				{
					if (this.m_offset < this.m_startIndex)
					{
						throw new InvalidOperationException(Environment.GetResourceString("PartitionerStatic_CurrentCalledBeforeMoveNext"));
					}
					return new KeyValuePair<long, TSource>((long)this.m_offset, this.m_array[this.m_offset]);
				}
			}

			// Token: 0x040035D9 RID: 13785
			private volatile TSource[] m_array;
		}

		// Token: 0x02000BD9 RID: 3033
		private class SharedInt
		{
			// Token: 0x06006EDE RID: 28382 RVA: 0x0017E0CC File Offset: 0x0017C2CC
			internal SharedInt(int value)
			{
				this.Value = value;
			}

			// Token: 0x040035DA RID: 13786
			internal volatile int Value;
		}

		// Token: 0x02000BDA RID: 3034
		private class SharedBool
		{
			// Token: 0x06006EDF RID: 28383 RVA: 0x0017E0DD File Offset: 0x0017C2DD
			internal SharedBool(bool value)
			{
				this.Value = value;
			}

			// Token: 0x040035DB RID: 13787
			internal volatile bool Value;
		}

		// Token: 0x02000BDB RID: 3035
		private class SharedLong
		{
			// Token: 0x06006EE0 RID: 28384 RVA: 0x0017E0EE File Offset: 0x0017C2EE
			internal SharedLong(long value)
			{
				this.Value = value;
			}

			// Token: 0x040035DC RID: 13788
			internal long Value;
		}
	}
}
