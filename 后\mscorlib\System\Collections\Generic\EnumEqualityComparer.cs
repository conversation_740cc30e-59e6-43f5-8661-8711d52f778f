﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;
using System.Security;

namespace System.Collections.Generic
{
	// Token: 0x020004C5 RID: 1221
	[Serializable]
	internal class EnumEqualityComparer<T> : EqualityComparer<T>, ISerializable where T : struct
	{
		// Token: 0x06003A9F RID: 15007 RVA: 0x000DF698 File Offset: 0x000DD898
		public override bool Equals(T x, T y)
		{
			int num = JitHelpers.UnsafeEnumCast<T>(x);
			int num2 = JitHelpers.UnsafeEnumCast<T>(y);
			return num == num2;
		}

		// Token: 0x06003AA0 RID: 15008 RVA: 0x000DF6B8 File Offset: 0x000DD8B8
		public override int GetHashCode(T obj)
		{
			return JitHelpers.UnsafeEnumCast<T>(obj).GetHashCode();
		}

		// Token: 0x06003AA1 RID: 15009 RVA: 0x000DF6D3 File Offset: 0x000DD8D3
		public EnumEqualityComparer()
		{
		}

		// Token: 0x06003AA2 RID: 15010 RVA: 0x000DF6DB File Offset: 0x000DD8DB
		protected EnumEqualityComparer(SerializationInfo information, StreamingContext context)
		{
		}

		// Token: 0x06003AA3 RID: 15011 RVA: 0x000DF6E3 File Offset: 0x000DD8E3
		[SecurityCritical]
		public void GetObjectData(SerializationInfo info, StreamingContext context)
		{
			if (Type.GetTypeCode(Enum.GetUnderlyingType(typeof(T))) != TypeCode.Int32)
			{
				info.SetType(typeof(ObjectEqualityComparer<T>));
			}
		}

		// Token: 0x06003AA4 RID: 15012 RVA: 0x000DF710 File Offset: 0x000DD910
		public override bool Equals(object obj)
		{
			EnumEqualityComparer<T> enumEqualityComparer = obj as EnumEqualityComparer<T>;
			return enumEqualityComparer != null;
		}

		// Token: 0x06003AA5 RID: 15013 RVA: 0x000DF728 File Offset: 0x000DD928
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}
	}
}
