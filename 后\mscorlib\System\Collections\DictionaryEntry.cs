﻿using System;
using System.Runtime.InteropServices;

namespace System.Collections
{
	// Token: 0x02000499 RID: 1177
	[ComVisible(true)]
	[__DynamicallyInvokable]
	[Serializable]
	public struct DictionaryEntry
	{
		// Token: 0x060038AC RID: 14508 RVA: 0x000D9D5B File Offset: 0x000D7F5B
		[__DynamicallyInvokable]
		public DictionaryEntry(object key, object value)
		{
			this._key = key;
			this._value = value;
		}

		// Token: 0x1700086B RID: 2155
		// (get) Token: 0x060038AD RID: 14509 RVA: 0x000D9D6B File Offset: 0x000D7F6B
		// (set) Token: 0x060038AE RID: 14510 RVA: 0x000D9D73 File Offset: 0x000D7F73
		[__DynamicallyInvokable]
		public object Key
		{
			[__DynamicallyInvokable]
			get
			{
				return this._key;
			}
			[__DynamicallyInvokable]
			set
			{
				this._key = value;
			}
		}

		// Token: 0x1700086C RID: 2156
		// (get) Token: 0x060038AF RID: 14511 RVA: 0x000D9D7C File Offset: 0x000D7F7C
		// (set) Token: 0x060038B0 RID: 14512 RVA: 0x000D9D84 File Offset: 0x000D7F84
		[__DynamicallyInvokable]
		public object Value
		{
			[__DynamicallyInvokable]
			get
			{
				return this._value;
			}
			[__DynamicallyInvokable]
			set
			{
				this._value = value;
			}
		}

		// Token: 0x04001901 RID: 6401
		private object _key;

		// Token: 0x04001902 RID: 6402
		private object _value;
	}
}
