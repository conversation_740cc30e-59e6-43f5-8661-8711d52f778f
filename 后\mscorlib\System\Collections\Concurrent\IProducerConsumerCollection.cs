﻿using System;
using System.Collections.Generic;

namespace System.Collections.Concurrent
{
	// Token: 0x020004AB RID: 1195
	[__DynamicallyInvokable]
	public interface IProducerConsumerCollection<T> : IEnumerable<T>, IEnumerable, ICollection
	{
		// Token: 0x0600392C RID: 14636
		[__DynamicallyInvokable]
		void CopyTo(T[] array, int index);

		// Token: 0x0600392D RID: 14637
		[__DynamicallyInvokable]
		bool TryAdd(T item);

		// Token: 0x0600392E RID: 14638
		[__DynamicallyInvokable]
		bool TryTake(out T item);

		// Token: 0x0600392F RID: 14639
		[__DynamicallyInvokable]
		T[] ToArray();
	}
}
