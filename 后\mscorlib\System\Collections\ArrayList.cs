﻿using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Security;
using System.Security.Permissions;
using System.Threading;

namespace System.Collections
{
	// Token: 0x02000490 RID: 1168
	[DebuggerTypeProxy(typeof(ArrayList.ArrayListDebugView))]
	[DebuggerDisplay("Count = {Count}")]
	[ComVisible(true)]
	[Serializable]
	public class ArrayList : IList, ICollection, IEnumerable, ICloneable
	{
		// Token: 0x060037D8 RID: 14296 RVA: 0x000D6460 File Offset: 0x000D4660
		internal ArrayList(bool trash)
		{
		}

		// Token: 0x060037D9 RID: 14297 RVA: 0x000D6468 File Offset: 0x000D4668
		public ArrayList()
		{
			this._items = ArrayList.emptyArray;
		}

		// Token: 0x060037DA RID: 14298 RVA: 0x000D647C File Offset: 0x000D467C
		public ArrayList(int capacity)
		{
			if (capacity < 0)
			{
				throw new ArgumentOutOfRangeException("capacity", Environment.GetResourceString("ArgumentOutOfRange_MustBeNonNegNum", new object[] { "capacity" }));
			}
			if (capacity == 0)
			{
				this._items = ArrayList.emptyArray;
				return;
			}
			this._items = new object[capacity];
		}

		// Token: 0x060037DB RID: 14299 RVA: 0x000D64D4 File Offset: 0x000D46D4
		public ArrayList(ICollection c)
		{
			if (c == null)
			{
				throw new ArgumentNullException("c", Environment.GetResourceString("ArgumentNull_Collection"));
			}
			int count = c.Count;
			if (count == 0)
			{
				this._items = ArrayList.emptyArray;
				return;
			}
			this._items = new object[count];
			this.AddRange(c);
		}

		// Token: 0x1700083D RID: 2109
		// (get) Token: 0x060037DC RID: 14300 RVA: 0x000D6528 File Offset: 0x000D4728
		// (set) Token: 0x060037DD RID: 14301 RVA: 0x000D6534 File Offset: 0x000D4734
		public virtual int Capacity
		{
			get
			{
				return this._items.Length;
			}
			set
			{
				if (value < this._size)
				{
					throw new ArgumentOutOfRangeException("value", Environment.GetResourceString("ArgumentOutOfRange_SmallCapacity"));
				}
				if (value != this._items.Length)
				{
					if (value > 0)
					{
						object[] array = new object[value];
						if (this._size > 0)
						{
							Array.Copy(this._items, 0, array, 0, this._size);
						}
						this._items = array;
						return;
					}
					this._items = new object[4];
				}
			}
		}

		// Token: 0x1700083E RID: 2110
		// (get) Token: 0x060037DE RID: 14302 RVA: 0x000D65A6 File Offset: 0x000D47A6
		public virtual int Count
		{
			get
			{
				return this._size;
			}
		}

		// Token: 0x1700083F RID: 2111
		// (get) Token: 0x060037DF RID: 14303 RVA: 0x000D65AE File Offset: 0x000D47AE
		public virtual bool IsFixedSize
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000840 RID: 2112
		// (get) Token: 0x060037E0 RID: 14304 RVA: 0x000D65B1 File Offset: 0x000D47B1
		public virtual bool IsReadOnly
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000841 RID: 2113
		// (get) Token: 0x060037E1 RID: 14305 RVA: 0x000D65B4 File Offset: 0x000D47B4
		public virtual bool IsSynchronized
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000842 RID: 2114
		// (get) Token: 0x060037E2 RID: 14306 RVA: 0x000D65B7 File Offset: 0x000D47B7
		public virtual object SyncRoot
		{
			get
			{
				if (this._syncRoot == null)
				{
					Interlocked.CompareExchange<object>(ref this._syncRoot, new object(), null);
				}
				return this._syncRoot;
			}
		}

		// Token: 0x17000843 RID: 2115
		public virtual object this[int index]
		{
			get
			{
				if (index < 0 || index >= this._size)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				return this._items[index];
			}
			set
			{
				if (index < 0 || index >= this._size)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				this._items[index] = value;
				this._version++;
			}
		}

		// Token: 0x060037E5 RID: 14309 RVA: 0x000D6640 File Offset: 0x000D4840
		public static ArrayList Adapter(IList list)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			return new ArrayList.IListWrapper(list);
		}

		// Token: 0x060037E6 RID: 14310 RVA: 0x000D6658 File Offset: 0x000D4858
		public virtual int Add(object value)
		{
			if (this._size == this._items.Length)
			{
				this.EnsureCapacity(this._size + 1);
			}
			this._items[this._size] = value;
			this._version++;
			int size = this._size;
			this._size = size + 1;
			return size;
		}

		// Token: 0x060037E7 RID: 14311 RVA: 0x000D66B0 File Offset: 0x000D48B0
		public virtual void AddRange(ICollection c)
		{
			this.InsertRange(this._size, c);
		}

		// Token: 0x060037E8 RID: 14312 RVA: 0x000D66C0 File Offset: 0x000D48C0
		public virtual int BinarySearch(int index, int count, object value, IComparer comparer)
		{
			if (index < 0)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (count < 0)
			{
				throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (this._size - index < count)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
			}
			return Array.BinarySearch(this._items, index, count, value, comparer);
		}

		// Token: 0x060037E9 RID: 14313 RVA: 0x000D672A File Offset: 0x000D492A
		public virtual int BinarySearch(object value)
		{
			return this.BinarySearch(0, this.Count, value, null);
		}

		// Token: 0x060037EA RID: 14314 RVA: 0x000D673B File Offset: 0x000D493B
		public virtual int BinarySearch(object value, IComparer comparer)
		{
			return this.BinarySearch(0, this.Count, value, comparer);
		}

		// Token: 0x060037EB RID: 14315 RVA: 0x000D674C File Offset: 0x000D494C
		public virtual void Clear()
		{
			if (this._size > 0)
			{
				Array.Clear(this._items, 0, this._size);
				this._size = 0;
			}
			this._version++;
		}

		// Token: 0x060037EC RID: 14316 RVA: 0x000D6780 File Offset: 0x000D4980
		public virtual object Clone()
		{
			ArrayList arrayList = new ArrayList(this._size);
			arrayList._size = this._size;
			arrayList._version = this._version;
			Array.Copy(this._items, 0, arrayList._items, 0, this._size);
			return arrayList;
		}

		// Token: 0x060037ED RID: 14317 RVA: 0x000D67CC File Offset: 0x000D49CC
		public virtual bool Contains(object item)
		{
			if (item == null)
			{
				for (int i = 0; i < this._size; i++)
				{
					if (this._items[i] == null)
					{
						return true;
					}
				}
				return false;
			}
			for (int j = 0; j < this._size; j++)
			{
				if (this._items[j] != null && this._items[j].Equals(item))
				{
					return true;
				}
			}
			return false;
		}

		// Token: 0x060037EE RID: 14318 RVA: 0x000D6829 File Offset: 0x000D4A29
		public virtual void CopyTo(Array array)
		{
			this.CopyTo(array, 0);
		}

		// Token: 0x060037EF RID: 14319 RVA: 0x000D6833 File Offset: 0x000D4A33
		public virtual void CopyTo(Array array, int arrayIndex)
		{
			if (array != null && array.Rank != 1)
			{
				throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
			}
			Array.Copy(this._items, 0, array, arrayIndex, this._size);
		}

		// Token: 0x060037F0 RID: 14320 RVA: 0x000D6868 File Offset: 0x000D4A68
		public virtual void CopyTo(int index, Array array, int arrayIndex, int count)
		{
			if (this._size - index < count)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
			}
			if (array != null && array.Rank != 1)
			{
				throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
			}
			Array.Copy(this._items, index, array, arrayIndex, count);
		}

		// Token: 0x060037F1 RID: 14321 RVA: 0x000D68C0 File Offset: 0x000D4AC0
		private void EnsureCapacity(int min)
		{
			if (this._items.Length < min)
			{
				int num = ((this._items.Length == 0) ? 4 : (this._items.Length * 2));
				if (num > 2146435071)
				{
					num = 2146435071;
				}
				if (num < min)
				{
					num = min;
				}
				this.Capacity = num;
			}
		}

		// Token: 0x060037F2 RID: 14322 RVA: 0x000D690A File Offset: 0x000D4B0A
		public static IList FixedSize(IList list)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			return new ArrayList.FixedSizeList(list);
		}

		// Token: 0x060037F3 RID: 14323 RVA: 0x000D6920 File Offset: 0x000D4B20
		public static ArrayList FixedSize(ArrayList list)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			return new ArrayList.FixedSizeArrayList(list);
		}

		// Token: 0x060037F4 RID: 14324 RVA: 0x000D6936 File Offset: 0x000D4B36
		public virtual IEnumerator GetEnumerator()
		{
			return new ArrayList.ArrayListEnumeratorSimple(this);
		}

		// Token: 0x060037F5 RID: 14325 RVA: 0x000D6940 File Offset: 0x000D4B40
		public virtual IEnumerator GetEnumerator(int index, int count)
		{
			if (index < 0)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (count < 0)
			{
				throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (this._size - index < count)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
			}
			return new ArrayList.ArrayListEnumerator(this, index, count);
		}

		// Token: 0x060037F6 RID: 14326 RVA: 0x000D69A2 File Offset: 0x000D4BA2
		public virtual int IndexOf(object value)
		{
			return Array.IndexOf(this._items, value, 0, this._size);
		}

		// Token: 0x060037F7 RID: 14327 RVA: 0x000D69B7 File Offset: 0x000D4BB7
		public virtual int IndexOf(object value, int startIndex)
		{
			if (startIndex > this._size)
			{
				throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			return Array.IndexOf(this._items, value, startIndex, this._size - startIndex);
		}

		// Token: 0x060037F8 RID: 14328 RVA: 0x000D69EC File Offset: 0x000D4BEC
		public virtual int IndexOf(object value, int startIndex, int count)
		{
			if (startIndex > this._size)
			{
				throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			if (count < 0 || startIndex > this._size - count)
			{
				throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_Count"));
			}
			return Array.IndexOf(this._items, value, startIndex, count);
		}

		// Token: 0x060037F9 RID: 14329 RVA: 0x000D6A4C File Offset: 0x000D4C4C
		public virtual void Insert(int index, object value)
		{
			if (index < 0 || index > this._size)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_ArrayListInsert"));
			}
			if (this._size == this._items.Length)
			{
				this.EnsureCapacity(this._size + 1);
			}
			if (index < this._size)
			{
				Array.Copy(this._items, index, this._items, index + 1, this._size - index);
			}
			this._items[index] = value;
			this._size++;
			this._version++;
		}

		// Token: 0x060037FA RID: 14330 RVA: 0x000D6AE4 File Offset: 0x000D4CE4
		public virtual void InsertRange(int index, ICollection c)
		{
			if (c == null)
			{
				throw new ArgumentNullException("c", Environment.GetResourceString("ArgumentNull_Collection"));
			}
			if (index < 0 || index > this._size)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			int count = c.Count;
			if (count > 0)
			{
				this.EnsureCapacity(this._size + count);
				if (index < this._size)
				{
					Array.Copy(this._items, index, this._items, index + count, this._size - index);
				}
				object[] array = new object[count];
				c.CopyTo(array, 0);
				array.CopyTo(this._items, index);
				this._size += count;
				this._version++;
			}
		}

		// Token: 0x060037FB RID: 14331 RVA: 0x000D6BA2 File Offset: 0x000D4DA2
		public virtual int LastIndexOf(object value)
		{
			return this.LastIndexOf(value, this._size - 1, this._size);
		}

		// Token: 0x060037FC RID: 14332 RVA: 0x000D6BB9 File Offset: 0x000D4DB9
		public virtual int LastIndexOf(object value, int startIndex)
		{
			if (startIndex >= this._size)
			{
				throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			return this.LastIndexOf(value, startIndex, startIndex + 1);
		}

		// Token: 0x060037FD RID: 14333 RVA: 0x000D6BE4 File Offset: 0x000D4DE4
		public virtual int LastIndexOf(object value, int startIndex, int count)
		{
			if (this.Count != 0 && (startIndex < 0 || count < 0))
			{
				throw new ArgumentOutOfRangeException((startIndex < 0) ? "startIndex" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (this._size == 0)
			{
				return -1;
			}
			if (startIndex >= this._size || count > startIndex + 1)
			{
				throw new ArgumentOutOfRangeException((startIndex >= this._size) ? "startIndex" : "count", Environment.GetResourceString("ArgumentOutOfRange_BiggerThanCollection"));
			}
			return Array.LastIndexOf(this._items, value, startIndex, count);
		}

		// Token: 0x060037FE RID: 14334 RVA: 0x000D6C6D File Offset: 0x000D4E6D
		public static IList ReadOnly(IList list)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			return new ArrayList.ReadOnlyList(list);
		}

		// Token: 0x060037FF RID: 14335 RVA: 0x000D6C83 File Offset: 0x000D4E83
		public static ArrayList ReadOnly(ArrayList list)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			return new ArrayList.ReadOnlyArrayList(list);
		}

		// Token: 0x06003800 RID: 14336 RVA: 0x000D6C9C File Offset: 0x000D4E9C
		public virtual void Remove(object obj)
		{
			int num = this.IndexOf(obj);
			if (num >= 0)
			{
				this.RemoveAt(num);
			}
		}

		// Token: 0x06003801 RID: 14337 RVA: 0x000D6CBC File Offset: 0x000D4EBC
		public virtual void RemoveAt(int index)
		{
			if (index < 0 || index >= this._size)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			this._size--;
			if (index < this._size)
			{
				Array.Copy(this._items, index + 1, this._items, index, this._size - index);
			}
			this._items[this._size] = null;
			this._version++;
		}

		// Token: 0x06003802 RID: 14338 RVA: 0x000D6D3C File Offset: 0x000D4F3C
		public virtual void RemoveRange(int index, int count)
		{
			if (index < 0)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (count < 0)
			{
				throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (this._size - index < count)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
			}
			if (count > 0)
			{
				int i = this._size;
				this._size -= count;
				if (index < this._size)
				{
					Array.Copy(this._items, index + count, this._items, index, this._size - index);
				}
				while (i > this._size)
				{
					this._items[--i] = null;
				}
				this._version++;
			}
		}

		// Token: 0x06003803 RID: 14339 RVA: 0x000D6DFC File Offset: 0x000D4FFC
		public static ArrayList Repeat(object value, int count)
		{
			if (count < 0)
			{
				throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			ArrayList arrayList = new ArrayList((count > 4) ? count : 4);
			for (int i = 0; i < count; i++)
			{
				arrayList.Add(value);
			}
			return arrayList;
		}

		// Token: 0x06003804 RID: 14340 RVA: 0x000D6E45 File Offset: 0x000D5045
		public virtual void Reverse()
		{
			this.Reverse(0, this.Count);
		}

		// Token: 0x06003805 RID: 14341 RVA: 0x000D6E54 File Offset: 0x000D5054
		public virtual void Reverse(int index, int count)
		{
			if (index < 0)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (count < 0)
			{
				throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (this._size - index < count)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
			}
			Array.Reverse(this._items, index, count);
			this._version++;
		}

		// Token: 0x06003806 RID: 14342 RVA: 0x000D6ECC File Offset: 0x000D50CC
		public virtual void SetRange(int index, ICollection c)
		{
			if (c == null)
			{
				throw new ArgumentNullException("c", Environment.GetResourceString("ArgumentNull_Collection"));
			}
			int count = c.Count;
			if (index < 0 || index > this._size - count)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			if (count > 0)
			{
				c.CopyTo(this._items, index);
				this._version++;
			}
		}

		// Token: 0x06003807 RID: 14343 RVA: 0x000D6F3C File Offset: 0x000D513C
		public virtual ArrayList GetRange(int index, int count)
		{
			if (index < 0 || count < 0)
			{
				throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (this._size - index < count)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
			}
			return new ArrayList.Range(this, index, count);
		}

		// Token: 0x06003808 RID: 14344 RVA: 0x000D6F94 File Offset: 0x000D5194
		public virtual void Sort()
		{
			this.Sort(0, this.Count, Comparer.Default);
		}

		// Token: 0x06003809 RID: 14345 RVA: 0x000D6FA8 File Offset: 0x000D51A8
		public virtual void Sort(IComparer comparer)
		{
			this.Sort(0, this.Count, comparer);
		}

		// Token: 0x0600380A RID: 14346 RVA: 0x000D6FB8 File Offset: 0x000D51B8
		public virtual void Sort(int index, int count, IComparer comparer)
		{
			if (index < 0)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (count < 0)
			{
				throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (this._size - index < count)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
			}
			Array.Sort(this._items, index, count, comparer);
			this._version++;
		}

		// Token: 0x0600380B RID: 14347 RVA: 0x000D702E File Offset: 0x000D522E
		[HostProtection(SecurityAction.LinkDemand, Synchronization = true)]
		public static IList Synchronized(IList list)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			return new ArrayList.SyncIList(list);
		}

		// Token: 0x0600380C RID: 14348 RVA: 0x000D7044 File Offset: 0x000D5244
		[HostProtection(SecurityAction.LinkDemand, Synchronization = true)]
		public static ArrayList Synchronized(ArrayList list)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			return new ArrayList.SyncArrayList(list);
		}

		// Token: 0x0600380D RID: 14349 RVA: 0x000D705C File Offset: 0x000D525C
		public virtual object[] ToArray()
		{
			object[] array = new object[this._size];
			Array.Copy(this._items, 0, array, 0, this._size);
			return array;
		}

		// Token: 0x0600380E RID: 14350 RVA: 0x000D708C File Offset: 0x000D528C
		[SecuritySafeCritical]
		public virtual Array ToArray(Type type)
		{
			if (type == null)
			{
				throw new ArgumentNullException("type");
			}
			Array array = Array.UnsafeCreateInstance(type, this._size);
			Array.Copy(this._items, 0, array, 0, this._size);
			return array;
		}

		// Token: 0x0600380F RID: 14351 RVA: 0x000D70CF File Offset: 0x000D52CF
		public virtual void TrimToSize()
		{
			this.Capacity = this._size;
		}

		// Token: 0x040018C5 RID: 6341
		private object[] _items;

		// Token: 0x040018C6 RID: 6342
		private int _size;

		// Token: 0x040018C7 RID: 6343
		private int _version;

		// Token: 0x040018C8 RID: 6344
		[NonSerialized]
		private object _syncRoot;

		// Token: 0x040018C9 RID: 6345
		private const int _defaultCapacity = 4;

		// Token: 0x040018CA RID: 6346
		private static readonly object[] emptyArray = EmptyArray<object>.Value;

		// Token: 0x02000BA5 RID: 2981
		[Serializable]
		private class IListWrapper : ArrayList
		{
			// Token: 0x06006CD1 RID: 27857 RVA: 0x001786C8 File Offset: 0x001768C8
			internal IListWrapper(IList list)
			{
				this._list = list;
				this._version = 0;
			}

			// Token: 0x17001267 RID: 4711
			// (get) Token: 0x06006CD2 RID: 27858 RVA: 0x001786DE File Offset: 0x001768DE
			// (set) Token: 0x06006CD3 RID: 27859 RVA: 0x001786EB File Offset: 0x001768EB
			public override int Capacity
			{
				get
				{
					return this._list.Count;
				}
				set
				{
					if (value < this.Count)
					{
						throw new ArgumentOutOfRangeException("value", Environment.GetResourceString("ArgumentOutOfRange_SmallCapacity"));
					}
				}
			}

			// Token: 0x17001268 RID: 4712
			// (get) Token: 0x06006CD4 RID: 27860 RVA: 0x0017870B File Offset: 0x0017690B
			public override int Count
			{
				get
				{
					return this._list.Count;
				}
			}

			// Token: 0x17001269 RID: 4713
			// (get) Token: 0x06006CD5 RID: 27861 RVA: 0x00178718 File Offset: 0x00176918
			public override bool IsReadOnly
			{
				get
				{
					return this._list.IsReadOnly;
				}
			}

			// Token: 0x1700126A RID: 4714
			// (get) Token: 0x06006CD6 RID: 27862 RVA: 0x00178725 File Offset: 0x00176925
			public override bool IsFixedSize
			{
				get
				{
					return this._list.IsFixedSize;
				}
			}

			// Token: 0x1700126B RID: 4715
			// (get) Token: 0x06006CD7 RID: 27863 RVA: 0x00178732 File Offset: 0x00176932
			public override bool IsSynchronized
			{
				get
				{
					return this._list.IsSynchronized;
				}
			}

			// Token: 0x1700126C RID: 4716
			public override object this[int index]
			{
				get
				{
					return this._list[index];
				}
				set
				{
					this._list[index] = value;
					this._version++;
				}
			}

			// Token: 0x1700126D RID: 4717
			// (get) Token: 0x06006CDA RID: 27866 RVA: 0x0017876A File Offset: 0x0017696A
			public override object SyncRoot
			{
				get
				{
					return this._list.SyncRoot;
				}
			}

			// Token: 0x06006CDB RID: 27867 RVA: 0x00178778 File Offset: 0x00176978
			public override int Add(object obj)
			{
				int num = this._list.Add(obj);
				this._version++;
				return num;
			}

			// Token: 0x06006CDC RID: 27868 RVA: 0x001787A1 File Offset: 0x001769A1
			public override void AddRange(ICollection c)
			{
				this.InsertRange(this.Count, c);
			}

			// Token: 0x06006CDD RID: 27869 RVA: 0x001787B0 File Offset: 0x001769B0
			public override int BinarySearch(int index, int count, object value, IComparer comparer)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				if (comparer == null)
				{
					comparer = Comparer.Default;
				}
				int i = index;
				int num = index + count - 1;
				while (i <= num)
				{
					int num2 = (i + num) / 2;
					int num3 = comparer.Compare(value, this._list[num2]);
					if (num3 == 0)
					{
						return num2;
					}
					if (num3 < 0)
					{
						num = num2 - 1;
					}
					else
					{
						i = num2 + 1;
					}
				}
				return ~i;
			}

			// Token: 0x06006CDE RID: 27870 RVA: 0x00178849 File Offset: 0x00176A49
			public override void Clear()
			{
				if (this._list.IsFixedSize)
				{
					throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
				}
				this._list.Clear();
				this._version++;
			}

			// Token: 0x06006CDF RID: 27871 RVA: 0x00178881 File Offset: 0x00176A81
			public override object Clone()
			{
				return new ArrayList.IListWrapper(this._list);
			}

			// Token: 0x06006CE0 RID: 27872 RVA: 0x0017888E File Offset: 0x00176A8E
			public override bool Contains(object obj)
			{
				return this._list.Contains(obj);
			}

			// Token: 0x06006CE1 RID: 27873 RVA: 0x0017889C File Offset: 0x00176A9C
			public override void CopyTo(Array array, int index)
			{
				this._list.CopyTo(array, index);
			}

			// Token: 0x06006CE2 RID: 27874 RVA: 0x001788AC File Offset: 0x00176AAC
			public override void CopyTo(int index, Array array, int arrayIndex, int count)
			{
				if (array == null)
				{
					throw new ArgumentNullException("array");
				}
				if (index < 0 || arrayIndex < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "arrayIndex", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (count < 0)
				{
					throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (array.Length - arrayIndex < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				if (array.Rank != 1)
				{
					throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
				}
				if (this._list.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				for (int i = index; i < index + count; i++)
				{
					array.SetValue(this._list[i], arrayIndex++);
				}
			}

			// Token: 0x06006CE3 RID: 27875 RVA: 0x00178986 File Offset: 0x00176B86
			public override IEnumerator GetEnumerator()
			{
				return this._list.GetEnumerator();
			}

			// Token: 0x06006CE4 RID: 27876 RVA: 0x00178994 File Offset: 0x00176B94
			public override IEnumerator GetEnumerator(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._list.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				return new ArrayList.IListWrapper.IListWrapperEnumWrapper(this, index, count);
			}

			// Token: 0x06006CE5 RID: 27877 RVA: 0x001789F1 File Offset: 0x00176BF1
			public override int IndexOf(object value)
			{
				return this._list.IndexOf(value);
			}

			// Token: 0x06006CE6 RID: 27878 RVA: 0x001789FF File Offset: 0x00176BFF
			public override int IndexOf(object value, int startIndex)
			{
				return this.IndexOf(value, startIndex, this._list.Count - startIndex);
			}

			// Token: 0x06006CE7 RID: 27879 RVA: 0x00178A18 File Offset: 0x00176C18
			public override int IndexOf(object value, int startIndex, int count)
			{
				if (startIndex < 0 || startIndex > this.Count)
				{
					throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				if (count < 0 || startIndex > this.Count - count)
				{
					throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_Count"));
				}
				int num = startIndex + count;
				if (value == null)
				{
					for (int i = startIndex; i < num; i++)
					{
						if (this._list[i] == null)
						{
							return i;
						}
					}
					return -1;
				}
				for (int j = startIndex; j < num; j++)
				{
					if (this._list[j] != null && this._list[j].Equals(value))
					{
						return j;
					}
				}
				return -1;
			}

			// Token: 0x06006CE8 RID: 27880 RVA: 0x00178AC1 File Offset: 0x00176CC1
			public override void Insert(int index, object obj)
			{
				this._list.Insert(index, obj);
				this._version++;
			}

			// Token: 0x06006CE9 RID: 27881 RVA: 0x00178AE0 File Offset: 0x00176CE0
			public override void InsertRange(int index, ICollection c)
			{
				if (c == null)
				{
					throw new ArgumentNullException("c", Environment.GetResourceString("ArgumentNull_Collection"));
				}
				if (index < 0 || index > this.Count)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				if (c.Count > 0)
				{
					ArrayList arrayList = this._list as ArrayList;
					if (arrayList != null)
					{
						arrayList.InsertRange(index, c);
					}
					else
					{
						foreach (object obj in c)
						{
							this._list.Insert(index++, obj);
						}
					}
					this._version++;
				}
			}

			// Token: 0x06006CEA RID: 27882 RVA: 0x00178B7F File Offset: 0x00176D7F
			public override int LastIndexOf(object value)
			{
				return this.LastIndexOf(value, this._list.Count - 1, this._list.Count);
			}

			// Token: 0x06006CEB RID: 27883 RVA: 0x00178BA0 File Offset: 0x00176DA0
			public override int LastIndexOf(object value, int startIndex)
			{
				return this.LastIndexOf(value, startIndex, startIndex + 1);
			}

			// Token: 0x06006CEC RID: 27884 RVA: 0x00178BB0 File Offset: 0x00176DB0
			public override int LastIndexOf(object value, int startIndex, int count)
			{
				if (this._list.Count == 0)
				{
					return -1;
				}
				if (startIndex < 0 || startIndex >= this._list.Count)
				{
					throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				if (count < 0 || count > startIndex + 1)
				{
					throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_Count"));
				}
				int num = startIndex - count + 1;
				if (value == null)
				{
					for (int i = startIndex; i >= num; i--)
					{
						if (this._list[i] == null)
						{
							return i;
						}
					}
					return -1;
				}
				for (int j = startIndex; j >= num; j--)
				{
					if (this._list[j] != null && this._list[j].Equals(value))
					{
						return j;
					}
				}
				return -1;
			}

			// Token: 0x06006CED RID: 27885 RVA: 0x00178C6C File Offset: 0x00176E6C
			public override void Remove(object value)
			{
				int num = this.IndexOf(value);
				if (num >= 0)
				{
					this.RemoveAt(num);
				}
			}

			// Token: 0x06006CEE RID: 27886 RVA: 0x00178C8C File Offset: 0x00176E8C
			public override void RemoveAt(int index)
			{
				this._list.RemoveAt(index);
				this._version++;
			}

			// Token: 0x06006CEF RID: 27887 RVA: 0x00178CA8 File Offset: 0x00176EA8
			public override void RemoveRange(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._list.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				if (count > 0)
				{
					this._version++;
				}
				while (count > 0)
				{
					this._list.RemoveAt(index);
					count--;
				}
			}

			// Token: 0x06006CF0 RID: 27888 RVA: 0x00178D28 File Offset: 0x00176F28
			public override void Reverse(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._list.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				int i = index;
				int num = index + count - 1;
				while (i < num)
				{
					object obj = this._list[i];
					this._list[i++] = this._list[num];
					this._list[num--] = obj;
				}
				this._version++;
			}

			// Token: 0x06006CF1 RID: 27889 RVA: 0x00178DD4 File Offset: 0x00176FD4
			public override void SetRange(int index, ICollection c)
			{
				if (c == null)
				{
					throw new ArgumentNullException("c", Environment.GetResourceString("ArgumentNull_Collection"));
				}
				if (index < 0 || index > this._list.Count - c.Count)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				if (c.Count > 0)
				{
					foreach (object obj in c)
					{
						this._list[index++] = obj;
					}
					this._version++;
				}
			}

			// Token: 0x06006CF2 RID: 27890 RVA: 0x00178E68 File Offset: 0x00177068
			public override ArrayList GetRange(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._list.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				return new ArrayList.Range(this, index, count);
			}

			// Token: 0x06006CF3 RID: 27891 RVA: 0x00178EC8 File Offset: 0x001770C8
			public override void Sort(int index, int count, IComparer comparer)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._list.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				object[] array = new object[count];
				this.CopyTo(index, array, 0, count);
				Array.Sort(array, 0, count, comparer);
				for (int i = 0; i < count; i++)
				{
					this._list[i + index] = array[i];
				}
				this._version++;
			}

			// Token: 0x06006CF4 RID: 27892 RVA: 0x00178F64 File Offset: 0x00177164
			public override object[] ToArray()
			{
				object[] array = new object[this.Count];
				this._list.CopyTo(array, 0);
				return array;
			}

			// Token: 0x06006CF5 RID: 27893 RVA: 0x00178F8C File Offset: 0x0017718C
			[SecuritySafeCritical]
			public override Array ToArray(Type type)
			{
				if (type == null)
				{
					throw new ArgumentNullException("type");
				}
				Array array = Array.UnsafeCreateInstance(type, this._list.Count);
				this._list.CopyTo(array, 0);
				return array;
			}

			// Token: 0x06006CF6 RID: 27894 RVA: 0x00178FCD File Offset: 0x001771CD
			public override void TrimToSize()
			{
			}

			// Token: 0x0400354C RID: 13644
			private IList _list;

			// Token: 0x02000D08 RID: 3336
			[Serializable]
			private sealed class IListWrapperEnumWrapper : IEnumerator, ICloneable
			{
				// Token: 0x06007204 RID: 29188 RVA: 0x00188B4C File Offset: 0x00186D4C
				private IListWrapperEnumWrapper()
				{
				}

				// Token: 0x06007205 RID: 29189 RVA: 0x00188B54 File Offset: 0x00186D54
				internal IListWrapperEnumWrapper(ArrayList.IListWrapper listWrapper, int startIndex, int count)
				{
					this._en = listWrapper.GetEnumerator();
					this._initialStartIndex = startIndex;
					this._initialCount = count;
					while (startIndex-- > 0 && this._en.MoveNext())
					{
					}
					this._remaining = count;
					this._firstCall = true;
				}

				// Token: 0x06007206 RID: 29190 RVA: 0x00188BA8 File Offset: 0x00186DA8
				public object Clone()
				{
					return new ArrayList.IListWrapper.IListWrapperEnumWrapper
					{
						_en = (IEnumerator)((ICloneable)this._en).Clone(),
						_initialStartIndex = this._initialStartIndex,
						_initialCount = this._initialCount,
						_remaining = this._remaining,
						_firstCall = this._firstCall
					};
				}

				// Token: 0x06007207 RID: 29191 RVA: 0x00188C08 File Offset: 0x00186E08
				public bool MoveNext()
				{
					if (this._firstCall)
					{
						this._firstCall = false;
						int num = this._remaining;
						this._remaining = num - 1;
						return num > 0 && this._en.MoveNext();
					}
					if (this._remaining < 0)
					{
						return false;
					}
					bool flag = this._en.MoveNext();
					if (flag)
					{
						int num = this._remaining;
						this._remaining = num - 1;
						return num > 0;
					}
					return false;
				}

				// Token: 0x17001384 RID: 4996
				// (get) Token: 0x06007208 RID: 29192 RVA: 0x00188C76 File Offset: 0x00186E76
				public object Current
				{
					get
					{
						if (this._firstCall)
						{
							throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumNotStarted"));
						}
						if (this._remaining < 0)
						{
							throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumEnded"));
						}
						return this._en.Current;
					}
				}

				// Token: 0x06007209 RID: 29193 RVA: 0x00188CB4 File Offset: 0x00186EB4
				public void Reset()
				{
					this._en.Reset();
					int initialStartIndex = this._initialStartIndex;
					while (initialStartIndex-- > 0 && this._en.MoveNext())
					{
					}
					this._remaining = this._initialCount;
					this._firstCall = true;
				}

				// Token: 0x04003949 RID: 14665
				private IEnumerator _en;

				// Token: 0x0400394A RID: 14666
				private int _remaining;

				// Token: 0x0400394B RID: 14667
				private int _initialStartIndex;

				// Token: 0x0400394C RID: 14668
				private int _initialCount;

				// Token: 0x0400394D RID: 14669
				private bool _firstCall;
			}
		}

		// Token: 0x02000BA6 RID: 2982
		[Serializable]
		private class SyncArrayList : ArrayList
		{
			// Token: 0x06006CF7 RID: 27895 RVA: 0x00178FCF File Offset: 0x001771CF
			internal SyncArrayList(ArrayList list)
				: base(false)
			{
				this._list = list;
				this._root = list.SyncRoot;
			}

			// Token: 0x1700126E RID: 4718
			// (get) Token: 0x06006CF8 RID: 27896 RVA: 0x00178FEC File Offset: 0x001771EC
			// (set) Token: 0x06006CF9 RID: 27897 RVA: 0x00179034 File Offset: 0x00177234
			public override int Capacity
			{
				get
				{
					object root = this._root;
					int capacity;
					lock (root)
					{
						capacity = this._list.Capacity;
					}
					return capacity;
				}
				set
				{
					object root = this._root;
					lock (root)
					{
						this._list.Capacity = value;
					}
				}
			}

			// Token: 0x1700126F RID: 4719
			// (get) Token: 0x06006CFA RID: 27898 RVA: 0x0017907C File Offset: 0x0017727C
			public override int Count
			{
				get
				{
					object root = this._root;
					int count;
					lock (root)
					{
						count = this._list.Count;
					}
					return count;
				}
			}

			// Token: 0x17001270 RID: 4720
			// (get) Token: 0x06006CFB RID: 27899 RVA: 0x001790C4 File Offset: 0x001772C4
			public override bool IsReadOnly
			{
				get
				{
					return this._list.IsReadOnly;
				}
			}

			// Token: 0x17001271 RID: 4721
			// (get) Token: 0x06006CFC RID: 27900 RVA: 0x001790D1 File Offset: 0x001772D1
			public override bool IsFixedSize
			{
				get
				{
					return this._list.IsFixedSize;
				}
			}

			// Token: 0x17001272 RID: 4722
			// (get) Token: 0x06006CFD RID: 27901 RVA: 0x001790DE File Offset: 0x001772DE
			public override bool IsSynchronized
			{
				get
				{
					return true;
				}
			}

			// Token: 0x17001273 RID: 4723
			public override object this[int index]
			{
				get
				{
					object root = this._root;
					object obj;
					lock (root)
					{
						obj = this._list[index];
					}
					return obj;
				}
				set
				{
					object root = this._root;
					lock (root)
					{
						this._list[index] = value;
					}
				}
			}

			// Token: 0x17001274 RID: 4724
			// (get) Token: 0x06006D00 RID: 27904 RVA: 0x00179174 File Offset: 0x00177374
			public override object SyncRoot
			{
				get
				{
					return this._root;
				}
			}

			// Token: 0x06006D01 RID: 27905 RVA: 0x0017917C File Offset: 0x0017737C
			public override int Add(object value)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.Add(value);
				}
				return num;
			}

			// Token: 0x06006D02 RID: 27906 RVA: 0x001791C4 File Offset: 0x001773C4
			public override void AddRange(ICollection c)
			{
				object root = this._root;
				lock (root)
				{
					this._list.AddRange(c);
				}
			}

			// Token: 0x06006D03 RID: 27907 RVA: 0x0017920C File Offset: 0x0017740C
			public override int BinarySearch(object value)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.BinarySearch(value);
				}
				return num;
			}

			// Token: 0x06006D04 RID: 27908 RVA: 0x00179254 File Offset: 0x00177454
			public override int BinarySearch(object value, IComparer comparer)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.BinarySearch(value, comparer);
				}
				return num;
			}

			// Token: 0x06006D05 RID: 27909 RVA: 0x001792A0 File Offset: 0x001774A0
			public override int BinarySearch(int index, int count, object value, IComparer comparer)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.BinarySearch(index, count, value, comparer);
				}
				return num;
			}

			// Token: 0x06006D06 RID: 27910 RVA: 0x001792EC File Offset: 0x001774EC
			public override void Clear()
			{
				object root = this._root;
				lock (root)
				{
					this._list.Clear();
				}
			}

			// Token: 0x06006D07 RID: 27911 RVA: 0x00179334 File Offset: 0x00177534
			public override object Clone()
			{
				object root = this._root;
				object obj;
				lock (root)
				{
					obj = new ArrayList.SyncArrayList((ArrayList)this._list.Clone());
				}
				return obj;
			}

			// Token: 0x06006D08 RID: 27912 RVA: 0x00179388 File Offset: 0x00177588
			public override bool Contains(object item)
			{
				object root = this._root;
				bool flag2;
				lock (root)
				{
					flag2 = this._list.Contains(item);
				}
				return flag2;
			}

			// Token: 0x06006D09 RID: 27913 RVA: 0x001793D0 File Offset: 0x001775D0
			public override void CopyTo(Array array)
			{
				object root = this._root;
				lock (root)
				{
					this._list.CopyTo(array);
				}
			}

			// Token: 0x06006D0A RID: 27914 RVA: 0x00179418 File Offset: 0x00177618
			public override void CopyTo(Array array, int index)
			{
				object root = this._root;
				lock (root)
				{
					this._list.CopyTo(array, index);
				}
			}

			// Token: 0x06006D0B RID: 27915 RVA: 0x00179460 File Offset: 0x00177660
			public override void CopyTo(int index, Array array, int arrayIndex, int count)
			{
				object root = this._root;
				lock (root)
				{
					this._list.CopyTo(index, array, arrayIndex, count);
				}
			}

			// Token: 0x06006D0C RID: 27916 RVA: 0x001794AC File Offset: 0x001776AC
			public override IEnumerator GetEnumerator()
			{
				object root = this._root;
				IEnumerator enumerator;
				lock (root)
				{
					enumerator = this._list.GetEnumerator();
				}
				return enumerator;
			}

			// Token: 0x06006D0D RID: 27917 RVA: 0x001794F4 File Offset: 0x001776F4
			public override IEnumerator GetEnumerator(int index, int count)
			{
				object root = this._root;
				IEnumerator enumerator;
				lock (root)
				{
					enumerator = this._list.GetEnumerator(index, count);
				}
				return enumerator;
			}

			// Token: 0x06006D0E RID: 27918 RVA: 0x00179540 File Offset: 0x00177740
			public override int IndexOf(object value)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.IndexOf(value);
				}
				return num;
			}

			// Token: 0x06006D0F RID: 27919 RVA: 0x00179588 File Offset: 0x00177788
			public override int IndexOf(object value, int startIndex)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.IndexOf(value, startIndex);
				}
				return num;
			}

			// Token: 0x06006D10 RID: 27920 RVA: 0x001795D4 File Offset: 0x001777D4
			public override int IndexOf(object value, int startIndex, int count)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.IndexOf(value, startIndex, count);
				}
				return num;
			}

			// Token: 0x06006D11 RID: 27921 RVA: 0x00179620 File Offset: 0x00177820
			public override void Insert(int index, object value)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Insert(index, value);
				}
			}

			// Token: 0x06006D12 RID: 27922 RVA: 0x00179668 File Offset: 0x00177868
			public override void InsertRange(int index, ICollection c)
			{
				object root = this._root;
				lock (root)
				{
					this._list.InsertRange(index, c);
				}
			}

			// Token: 0x06006D13 RID: 27923 RVA: 0x001796B0 File Offset: 0x001778B0
			public override int LastIndexOf(object value)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.LastIndexOf(value);
				}
				return num;
			}

			// Token: 0x06006D14 RID: 27924 RVA: 0x001796F8 File Offset: 0x001778F8
			public override int LastIndexOf(object value, int startIndex)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.LastIndexOf(value, startIndex);
				}
				return num;
			}

			// Token: 0x06006D15 RID: 27925 RVA: 0x00179744 File Offset: 0x00177944
			public override int LastIndexOf(object value, int startIndex, int count)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.LastIndexOf(value, startIndex, count);
				}
				return num;
			}

			// Token: 0x06006D16 RID: 27926 RVA: 0x00179790 File Offset: 0x00177990
			public override void Remove(object value)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Remove(value);
				}
			}

			// Token: 0x06006D17 RID: 27927 RVA: 0x001797D8 File Offset: 0x001779D8
			public override void RemoveAt(int index)
			{
				object root = this._root;
				lock (root)
				{
					this._list.RemoveAt(index);
				}
			}

			// Token: 0x06006D18 RID: 27928 RVA: 0x00179820 File Offset: 0x00177A20
			public override void RemoveRange(int index, int count)
			{
				object root = this._root;
				lock (root)
				{
					this._list.RemoveRange(index, count);
				}
			}

			// Token: 0x06006D19 RID: 27929 RVA: 0x00179868 File Offset: 0x00177A68
			public override void Reverse(int index, int count)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Reverse(index, count);
				}
			}

			// Token: 0x06006D1A RID: 27930 RVA: 0x001798B0 File Offset: 0x00177AB0
			public override void SetRange(int index, ICollection c)
			{
				object root = this._root;
				lock (root)
				{
					this._list.SetRange(index, c);
				}
			}

			// Token: 0x06006D1B RID: 27931 RVA: 0x001798F8 File Offset: 0x00177AF8
			public override ArrayList GetRange(int index, int count)
			{
				object root = this._root;
				ArrayList range;
				lock (root)
				{
					range = this._list.GetRange(index, count);
				}
				return range;
			}

			// Token: 0x06006D1C RID: 27932 RVA: 0x00179944 File Offset: 0x00177B44
			public override void Sort()
			{
				object root = this._root;
				lock (root)
				{
					this._list.Sort();
				}
			}

			// Token: 0x06006D1D RID: 27933 RVA: 0x0017998C File Offset: 0x00177B8C
			public override void Sort(IComparer comparer)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Sort(comparer);
				}
			}

			// Token: 0x06006D1E RID: 27934 RVA: 0x001799D4 File Offset: 0x00177BD4
			public override void Sort(int index, int count, IComparer comparer)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Sort(index, count, comparer);
				}
			}

			// Token: 0x06006D1F RID: 27935 RVA: 0x00179A1C File Offset: 0x00177C1C
			public override object[] ToArray()
			{
				object root = this._root;
				object[] array;
				lock (root)
				{
					array = this._list.ToArray();
				}
				return array;
			}

			// Token: 0x06006D20 RID: 27936 RVA: 0x00179A64 File Offset: 0x00177C64
			public override Array ToArray(Type type)
			{
				object root = this._root;
				Array array;
				lock (root)
				{
					array = this._list.ToArray(type);
				}
				return array;
			}

			// Token: 0x06006D21 RID: 27937 RVA: 0x00179AAC File Offset: 0x00177CAC
			public override void TrimToSize()
			{
				object root = this._root;
				lock (root)
				{
					this._list.TrimToSize();
				}
			}

			// Token: 0x0400354D RID: 13645
			private ArrayList _list;

			// Token: 0x0400354E RID: 13646
			private object _root;
		}

		// Token: 0x02000BA7 RID: 2983
		[Serializable]
		private class SyncIList : IList, ICollection, IEnumerable
		{
			// Token: 0x06006D22 RID: 27938 RVA: 0x00179AF4 File Offset: 0x00177CF4
			internal SyncIList(IList list)
			{
				this._list = list;
				this._root = list.SyncRoot;
			}

			// Token: 0x17001275 RID: 4725
			// (get) Token: 0x06006D23 RID: 27939 RVA: 0x00179B10 File Offset: 0x00177D10
			public virtual int Count
			{
				get
				{
					object root = this._root;
					int count;
					lock (root)
					{
						count = this._list.Count;
					}
					return count;
				}
			}

			// Token: 0x17001276 RID: 4726
			// (get) Token: 0x06006D24 RID: 27940 RVA: 0x00179B58 File Offset: 0x00177D58
			public virtual bool IsReadOnly
			{
				get
				{
					return this._list.IsReadOnly;
				}
			}

			// Token: 0x17001277 RID: 4727
			// (get) Token: 0x06006D25 RID: 27941 RVA: 0x00179B65 File Offset: 0x00177D65
			public virtual bool IsFixedSize
			{
				get
				{
					return this._list.IsFixedSize;
				}
			}

			// Token: 0x17001278 RID: 4728
			// (get) Token: 0x06006D26 RID: 27942 RVA: 0x00179B72 File Offset: 0x00177D72
			public virtual bool IsSynchronized
			{
				get
				{
					return true;
				}
			}

			// Token: 0x17001279 RID: 4729
			public virtual object this[int index]
			{
				get
				{
					object root = this._root;
					object obj;
					lock (root)
					{
						obj = this._list[index];
					}
					return obj;
				}
				set
				{
					object root = this._root;
					lock (root)
					{
						this._list[index] = value;
					}
				}
			}

			// Token: 0x1700127A RID: 4730
			// (get) Token: 0x06006D29 RID: 27945 RVA: 0x00179C08 File Offset: 0x00177E08
			public virtual object SyncRoot
			{
				get
				{
					return this._root;
				}
			}

			// Token: 0x06006D2A RID: 27946 RVA: 0x00179C10 File Offset: 0x00177E10
			public virtual int Add(object value)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.Add(value);
				}
				return num;
			}

			// Token: 0x06006D2B RID: 27947 RVA: 0x00179C58 File Offset: 0x00177E58
			public virtual void Clear()
			{
				object root = this._root;
				lock (root)
				{
					this._list.Clear();
				}
			}

			// Token: 0x06006D2C RID: 27948 RVA: 0x00179CA0 File Offset: 0x00177EA0
			public virtual bool Contains(object item)
			{
				object root = this._root;
				bool flag2;
				lock (root)
				{
					flag2 = this._list.Contains(item);
				}
				return flag2;
			}

			// Token: 0x06006D2D RID: 27949 RVA: 0x00179CE8 File Offset: 0x00177EE8
			public virtual void CopyTo(Array array, int index)
			{
				object root = this._root;
				lock (root)
				{
					this._list.CopyTo(array, index);
				}
			}

			// Token: 0x06006D2E RID: 27950 RVA: 0x00179D30 File Offset: 0x00177F30
			public virtual IEnumerator GetEnumerator()
			{
				object root = this._root;
				IEnumerator enumerator;
				lock (root)
				{
					enumerator = this._list.GetEnumerator();
				}
				return enumerator;
			}

			// Token: 0x06006D2F RID: 27951 RVA: 0x00179D78 File Offset: 0x00177F78
			public virtual int IndexOf(object value)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.IndexOf(value);
				}
				return num;
			}

			// Token: 0x06006D30 RID: 27952 RVA: 0x00179DC0 File Offset: 0x00177FC0
			public virtual void Insert(int index, object value)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Insert(index, value);
				}
			}

			// Token: 0x06006D31 RID: 27953 RVA: 0x00179E08 File Offset: 0x00178008
			public virtual void Remove(object value)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Remove(value);
				}
			}

			// Token: 0x06006D32 RID: 27954 RVA: 0x00179E50 File Offset: 0x00178050
			public virtual void RemoveAt(int index)
			{
				object root = this._root;
				lock (root)
				{
					this._list.RemoveAt(index);
				}
			}

			// Token: 0x0400354F RID: 13647
			private IList _list;

			// Token: 0x04003550 RID: 13648
			private object _root;
		}

		// Token: 0x02000BA8 RID: 2984
		[Serializable]
		private class FixedSizeList : IList, ICollection, IEnumerable
		{
			// Token: 0x06006D33 RID: 27955 RVA: 0x00179E98 File Offset: 0x00178098
			internal FixedSizeList(IList l)
			{
				this._list = l;
			}

			// Token: 0x1700127B RID: 4731
			// (get) Token: 0x06006D34 RID: 27956 RVA: 0x00179EA7 File Offset: 0x001780A7
			public virtual int Count
			{
				get
				{
					return this._list.Count;
				}
			}

			// Token: 0x1700127C RID: 4732
			// (get) Token: 0x06006D35 RID: 27957 RVA: 0x00179EB4 File Offset: 0x001780B4
			public virtual bool IsReadOnly
			{
				get
				{
					return this._list.IsReadOnly;
				}
			}

			// Token: 0x1700127D RID: 4733
			// (get) Token: 0x06006D36 RID: 27958 RVA: 0x00179EC1 File Offset: 0x001780C1
			public virtual bool IsFixedSize
			{
				get
				{
					return true;
				}
			}

			// Token: 0x1700127E RID: 4734
			// (get) Token: 0x06006D37 RID: 27959 RVA: 0x00179EC4 File Offset: 0x001780C4
			public virtual bool IsSynchronized
			{
				get
				{
					return this._list.IsSynchronized;
				}
			}

			// Token: 0x1700127F RID: 4735
			public virtual object this[int index]
			{
				get
				{
					return this._list[index];
				}
				set
				{
					this._list[index] = value;
				}
			}

			// Token: 0x17001280 RID: 4736
			// (get) Token: 0x06006D3A RID: 27962 RVA: 0x00179EEE File Offset: 0x001780EE
			public virtual object SyncRoot
			{
				get
				{
					return this._list.SyncRoot;
				}
			}

			// Token: 0x06006D3B RID: 27963 RVA: 0x00179EFB File Offset: 0x001780FB
			public virtual int Add(object obj)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D3C RID: 27964 RVA: 0x00179F0C File Offset: 0x0017810C
			public virtual void Clear()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D3D RID: 27965 RVA: 0x00179F1D File Offset: 0x0017811D
			public virtual bool Contains(object obj)
			{
				return this._list.Contains(obj);
			}

			// Token: 0x06006D3E RID: 27966 RVA: 0x00179F2B File Offset: 0x0017812B
			public virtual void CopyTo(Array array, int index)
			{
				this._list.CopyTo(array, index);
			}

			// Token: 0x06006D3F RID: 27967 RVA: 0x00179F3A File Offset: 0x0017813A
			public virtual IEnumerator GetEnumerator()
			{
				return this._list.GetEnumerator();
			}

			// Token: 0x06006D40 RID: 27968 RVA: 0x00179F47 File Offset: 0x00178147
			public virtual int IndexOf(object value)
			{
				return this._list.IndexOf(value);
			}

			// Token: 0x06006D41 RID: 27969 RVA: 0x00179F55 File Offset: 0x00178155
			public virtual void Insert(int index, object obj)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D42 RID: 27970 RVA: 0x00179F66 File Offset: 0x00178166
			public virtual void Remove(object value)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D43 RID: 27971 RVA: 0x00179F77 File Offset: 0x00178177
			public virtual void RemoveAt(int index)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x04003551 RID: 13649
			private IList _list;
		}

		// Token: 0x02000BA9 RID: 2985
		[Serializable]
		private class FixedSizeArrayList : ArrayList
		{
			// Token: 0x06006D44 RID: 27972 RVA: 0x00179F88 File Offset: 0x00178188
			internal FixedSizeArrayList(ArrayList l)
			{
				this._list = l;
				this._version = this._list._version;
			}

			// Token: 0x17001281 RID: 4737
			// (get) Token: 0x06006D45 RID: 27973 RVA: 0x00179FA8 File Offset: 0x001781A8
			public override int Count
			{
				get
				{
					return this._list.Count;
				}
			}

			// Token: 0x17001282 RID: 4738
			// (get) Token: 0x06006D46 RID: 27974 RVA: 0x00179FB5 File Offset: 0x001781B5
			public override bool IsReadOnly
			{
				get
				{
					return this._list.IsReadOnly;
				}
			}

			// Token: 0x17001283 RID: 4739
			// (get) Token: 0x06006D47 RID: 27975 RVA: 0x00179FC2 File Offset: 0x001781C2
			public override bool IsFixedSize
			{
				get
				{
					return true;
				}
			}

			// Token: 0x17001284 RID: 4740
			// (get) Token: 0x06006D48 RID: 27976 RVA: 0x00179FC5 File Offset: 0x001781C5
			public override bool IsSynchronized
			{
				get
				{
					return this._list.IsSynchronized;
				}
			}

			// Token: 0x17001285 RID: 4741
			public override object this[int index]
			{
				get
				{
					return this._list[index];
				}
				set
				{
					this._list[index] = value;
					this._version = this._list._version;
				}
			}

			// Token: 0x17001286 RID: 4742
			// (get) Token: 0x06006D4B RID: 27979 RVA: 0x0017A000 File Offset: 0x00178200
			public override object SyncRoot
			{
				get
				{
					return this._list.SyncRoot;
				}
			}

			// Token: 0x06006D4C RID: 27980 RVA: 0x0017A00D File Offset: 0x0017820D
			public override int Add(object obj)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D4D RID: 27981 RVA: 0x0017A01E File Offset: 0x0017821E
			public override void AddRange(ICollection c)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D4E RID: 27982 RVA: 0x0017A02F File Offset: 0x0017822F
			public override int BinarySearch(int index, int count, object value, IComparer comparer)
			{
				return this._list.BinarySearch(index, count, value, comparer);
			}

			// Token: 0x17001287 RID: 4743
			// (get) Token: 0x06006D4F RID: 27983 RVA: 0x0017A041 File Offset: 0x00178241
			// (set) Token: 0x06006D50 RID: 27984 RVA: 0x0017A04E File Offset: 0x0017824E
			public override int Capacity
			{
				get
				{
					return this._list.Capacity;
				}
				set
				{
					throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
				}
			}

			// Token: 0x06006D51 RID: 27985 RVA: 0x0017A05F File Offset: 0x0017825F
			public override void Clear()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D52 RID: 27986 RVA: 0x0017A070 File Offset: 0x00178270
			public override object Clone()
			{
				return new ArrayList.FixedSizeArrayList(this._list)
				{
					_list = (ArrayList)this._list.Clone()
				};
			}

			// Token: 0x06006D53 RID: 27987 RVA: 0x0017A0A0 File Offset: 0x001782A0
			public override bool Contains(object obj)
			{
				return this._list.Contains(obj);
			}

			// Token: 0x06006D54 RID: 27988 RVA: 0x0017A0AE File Offset: 0x001782AE
			public override void CopyTo(Array array, int index)
			{
				this._list.CopyTo(array, index);
			}

			// Token: 0x06006D55 RID: 27989 RVA: 0x0017A0BD File Offset: 0x001782BD
			public override void CopyTo(int index, Array array, int arrayIndex, int count)
			{
				this._list.CopyTo(index, array, arrayIndex, count);
			}

			// Token: 0x06006D56 RID: 27990 RVA: 0x0017A0CF File Offset: 0x001782CF
			public override IEnumerator GetEnumerator()
			{
				return this._list.GetEnumerator();
			}

			// Token: 0x06006D57 RID: 27991 RVA: 0x0017A0DC File Offset: 0x001782DC
			public override IEnumerator GetEnumerator(int index, int count)
			{
				return this._list.GetEnumerator(index, count);
			}

			// Token: 0x06006D58 RID: 27992 RVA: 0x0017A0EB File Offset: 0x001782EB
			public override int IndexOf(object value)
			{
				return this._list.IndexOf(value);
			}

			// Token: 0x06006D59 RID: 27993 RVA: 0x0017A0F9 File Offset: 0x001782F9
			public override int IndexOf(object value, int startIndex)
			{
				return this._list.IndexOf(value, startIndex);
			}

			// Token: 0x06006D5A RID: 27994 RVA: 0x0017A108 File Offset: 0x00178308
			public override int IndexOf(object value, int startIndex, int count)
			{
				return this._list.IndexOf(value, startIndex, count);
			}

			// Token: 0x06006D5B RID: 27995 RVA: 0x0017A118 File Offset: 0x00178318
			public override void Insert(int index, object obj)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D5C RID: 27996 RVA: 0x0017A129 File Offset: 0x00178329
			public override void InsertRange(int index, ICollection c)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D5D RID: 27997 RVA: 0x0017A13A File Offset: 0x0017833A
			public override int LastIndexOf(object value)
			{
				return this._list.LastIndexOf(value);
			}

			// Token: 0x06006D5E RID: 27998 RVA: 0x0017A148 File Offset: 0x00178348
			public override int LastIndexOf(object value, int startIndex)
			{
				return this._list.LastIndexOf(value, startIndex);
			}

			// Token: 0x06006D5F RID: 27999 RVA: 0x0017A157 File Offset: 0x00178357
			public override int LastIndexOf(object value, int startIndex, int count)
			{
				return this._list.LastIndexOf(value, startIndex, count);
			}

			// Token: 0x06006D60 RID: 28000 RVA: 0x0017A167 File Offset: 0x00178367
			public override void Remove(object value)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D61 RID: 28001 RVA: 0x0017A178 File Offset: 0x00178378
			public override void RemoveAt(int index)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D62 RID: 28002 RVA: 0x0017A189 File Offset: 0x00178389
			public override void RemoveRange(int index, int count)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x06006D63 RID: 28003 RVA: 0x0017A19A File Offset: 0x0017839A
			public override void SetRange(int index, ICollection c)
			{
				this._list.SetRange(index, c);
				this._version = this._list._version;
			}

			// Token: 0x06006D64 RID: 28004 RVA: 0x0017A1BC File Offset: 0x001783BC
			public override ArrayList GetRange(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				return new ArrayList.Range(this, index, count);
			}

			// Token: 0x06006D65 RID: 28005 RVA: 0x0017A214 File Offset: 0x00178414
			public override void Reverse(int index, int count)
			{
				this._list.Reverse(index, count);
				this._version = this._list._version;
			}

			// Token: 0x06006D66 RID: 28006 RVA: 0x0017A234 File Offset: 0x00178434
			public override void Sort(int index, int count, IComparer comparer)
			{
				this._list.Sort(index, count, comparer);
				this._version = this._list._version;
			}

			// Token: 0x06006D67 RID: 28007 RVA: 0x0017A255 File Offset: 0x00178455
			public override object[] ToArray()
			{
				return this._list.ToArray();
			}

			// Token: 0x06006D68 RID: 28008 RVA: 0x0017A262 File Offset: 0x00178462
			public override Array ToArray(Type type)
			{
				return this._list.ToArray(type);
			}

			// Token: 0x06006D69 RID: 28009 RVA: 0x0017A270 File Offset: 0x00178470
			public override void TrimToSize()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_FixedSizeCollection"));
			}

			// Token: 0x04003552 RID: 13650
			private ArrayList _list;
		}

		// Token: 0x02000BAA RID: 2986
		[Serializable]
		private class ReadOnlyList : IList, ICollection, IEnumerable
		{
			// Token: 0x06006D6A RID: 28010 RVA: 0x0017A281 File Offset: 0x00178481
			internal ReadOnlyList(IList l)
			{
				this._list = l;
			}

			// Token: 0x17001288 RID: 4744
			// (get) Token: 0x06006D6B RID: 28011 RVA: 0x0017A290 File Offset: 0x00178490
			public virtual int Count
			{
				get
				{
					return this._list.Count;
				}
			}

			// Token: 0x17001289 RID: 4745
			// (get) Token: 0x06006D6C RID: 28012 RVA: 0x0017A29D File Offset: 0x0017849D
			public virtual bool IsReadOnly
			{
				get
				{
					return true;
				}
			}

			// Token: 0x1700128A RID: 4746
			// (get) Token: 0x06006D6D RID: 28013 RVA: 0x0017A2A0 File Offset: 0x001784A0
			public virtual bool IsFixedSize
			{
				get
				{
					return true;
				}
			}

			// Token: 0x1700128B RID: 4747
			// (get) Token: 0x06006D6E RID: 28014 RVA: 0x0017A2A3 File Offset: 0x001784A3
			public virtual bool IsSynchronized
			{
				get
				{
					return this._list.IsSynchronized;
				}
			}

			// Token: 0x1700128C RID: 4748
			public virtual object this[int index]
			{
				get
				{
					return this._list[index];
				}
				set
				{
					throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
				}
			}

			// Token: 0x1700128D RID: 4749
			// (get) Token: 0x06006D71 RID: 28017 RVA: 0x0017A2CF File Offset: 0x001784CF
			public virtual object SyncRoot
			{
				get
				{
					return this._list.SyncRoot;
				}
			}

			// Token: 0x06006D72 RID: 28018 RVA: 0x0017A2DC File Offset: 0x001784DC
			public virtual int Add(object obj)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D73 RID: 28019 RVA: 0x0017A2ED File Offset: 0x001784ED
			public virtual void Clear()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D74 RID: 28020 RVA: 0x0017A2FE File Offset: 0x001784FE
			public virtual bool Contains(object obj)
			{
				return this._list.Contains(obj);
			}

			// Token: 0x06006D75 RID: 28021 RVA: 0x0017A30C File Offset: 0x0017850C
			public virtual void CopyTo(Array array, int index)
			{
				this._list.CopyTo(array, index);
			}

			// Token: 0x06006D76 RID: 28022 RVA: 0x0017A31B File Offset: 0x0017851B
			public virtual IEnumerator GetEnumerator()
			{
				return this._list.GetEnumerator();
			}

			// Token: 0x06006D77 RID: 28023 RVA: 0x0017A328 File Offset: 0x00178528
			public virtual int IndexOf(object value)
			{
				return this._list.IndexOf(value);
			}

			// Token: 0x06006D78 RID: 28024 RVA: 0x0017A336 File Offset: 0x00178536
			public virtual void Insert(int index, object obj)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D79 RID: 28025 RVA: 0x0017A347 File Offset: 0x00178547
			public virtual void Remove(object value)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D7A RID: 28026 RVA: 0x0017A358 File Offset: 0x00178558
			public virtual void RemoveAt(int index)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x04003553 RID: 13651
			private IList _list;
		}

		// Token: 0x02000BAB RID: 2987
		[Serializable]
		private class ReadOnlyArrayList : ArrayList
		{
			// Token: 0x06006D7B RID: 28027 RVA: 0x0017A369 File Offset: 0x00178569
			internal ReadOnlyArrayList(ArrayList l)
			{
				this._list = l;
			}

			// Token: 0x1700128E RID: 4750
			// (get) Token: 0x06006D7C RID: 28028 RVA: 0x0017A378 File Offset: 0x00178578
			public override int Count
			{
				get
				{
					return this._list.Count;
				}
			}

			// Token: 0x1700128F RID: 4751
			// (get) Token: 0x06006D7D RID: 28029 RVA: 0x0017A385 File Offset: 0x00178585
			public override bool IsReadOnly
			{
				get
				{
					return true;
				}
			}

			// Token: 0x17001290 RID: 4752
			// (get) Token: 0x06006D7E RID: 28030 RVA: 0x0017A388 File Offset: 0x00178588
			public override bool IsFixedSize
			{
				get
				{
					return true;
				}
			}

			// Token: 0x17001291 RID: 4753
			// (get) Token: 0x06006D7F RID: 28031 RVA: 0x0017A38B File Offset: 0x0017858B
			public override bool IsSynchronized
			{
				get
				{
					return this._list.IsSynchronized;
				}
			}

			// Token: 0x17001292 RID: 4754
			public override object this[int index]
			{
				get
				{
					return this._list[index];
				}
				set
				{
					throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
				}
			}

			// Token: 0x17001293 RID: 4755
			// (get) Token: 0x06006D82 RID: 28034 RVA: 0x0017A3B7 File Offset: 0x001785B7
			public override object SyncRoot
			{
				get
				{
					return this._list.SyncRoot;
				}
			}

			// Token: 0x06006D83 RID: 28035 RVA: 0x0017A3C4 File Offset: 0x001785C4
			public override int Add(object obj)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D84 RID: 28036 RVA: 0x0017A3D5 File Offset: 0x001785D5
			public override void AddRange(ICollection c)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D85 RID: 28037 RVA: 0x0017A3E6 File Offset: 0x001785E6
			public override int BinarySearch(int index, int count, object value, IComparer comparer)
			{
				return this._list.BinarySearch(index, count, value, comparer);
			}

			// Token: 0x17001294 RID: 4756
			// (get) Token: 0x06006D86 RID: 28038 RVA: 0x0017A3F8 File Offset: 0x001785F8
			// (set) Token: 0x06006D87 RID: 28039 RVA: 0x0017A405 File Offset: 0x00178605
			public override int Capacity
			{
				get
				{
					return this._list.Capacity;
				}
				set
				{
					throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
				}
			}

			// Token: 0x06006D88 RID: 28040 RVA: 0x0017A416 File Offset: 0x00178616
			public override void Clear()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D89 RID: 28041 RVA: 0x0017A428 File Offset: 0x00178628
			public override object Clone()
			{
				return new ArrayList.ReadOnlyArrayList(this._list)
				{
					_list = (ArrayList)this._list.Clone()
				};
			}

			// Token: 0x06006D8A RID: 28042 RVA: 0x0017A458 File Offset: 0x00178658
			public override bool Contains(object obj)
			{
				return this._list.Contains(obj);
			}

			// Token: 0x06006D8B RID: 28043 RVA: 0x0017A466 File Offset: 0x00178666
			public override void CopyTo(Array array, int index)
			{
				this._list.CopyTo(array, index);
			}

			// Token: 0x06006D8C RID: 28044 RVA: 0x0017A475 File Offset: 0x00178675
			public override void CopyTo(int index, Array array, int arrayIndex, int count)
			{
				this._list.CopyTo(index, array, arrayIndex, count);
			}

			// Token: 0x06006D8D RID: 28045 RVA: 0x0017A487 File Offset: 0x00178687
			public override IEnumerator GetEnumerator()
			{
				return this._list.GetEnumerator();
			}

			// Token: 0x06006D8E RID: 28046 RVA: 0x0017A494 File Offset: 0x00178694
			public override IEnumerator GetEnumerator(int index, int count)
			{
				return this._list.GetEnumerator(index, count);
			}

			// Token: 0x06006D8F RID: 28047 RVA: 0x0017A4A3 File Offset: 0x001786A3
			public override int IndexOf(object value)
			{
				return this._list.IndexOf(value);
			}

			// Token: 0x06006D90 RID: 28048 RVA: 0x0017A4B1 File Offset: 0x001786B1
			public override int IndexOf(object value, int startIndex)
			{
				return this._list.IndexOf(value, startIndex);
			}

			// Token: 0x06006D91 RID: 28049 RVA: 0x0017A4C0 File Offset: 0x001786C0
			public override int IndexOf(object value, int startIndex, int count)
			{
				return this._list.IndexOf(value, startIndex, count);
			}

			// Token: 0x06006D92 RID: 28050 RVA: 0x0017A4D0 File Offset: 0x001786D0
			public override void Insert(int index, object obj)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D93 RID: 28051 RVA: 0x0017A4E1 File Offset: 0x001786E1
			public override void InsertRange(int index, ICollection c)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D94 RID: 28052 RVA: 0x0017A4F2 File Offset: 0x001786F2
			public override int LastIndexOf(object value)
			{
				return this._list.LastIndexOf(value);
			}

			// Token: 0x06006D95 RID: 28053 RVA: 0x0017A500 File Offset: 0x00178700
			public override int LastIndexOf(object value, int startIndex)
			{
				return this._list.LastIndexOf(value, startIndex);
			}

			// Token: 0x06006D96 RID: 28054 RVA: 0x0017A50F File Offset: 0x0017870F
			public override int LastIndexOf(object value, int startIndex, int count)
			{
				return this._list.LastIndexOf(value, startIndex, count);
			}

			// Token: 0x06006D97 RID: 28055 RVA: 0x0017A51F File Offset: 0x0017871F
			public override void Remove(object value)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D98 RID: 28056 RVA: 0x0017A530 File Offset: 0x00178730
			public override void RemoveAt(int index)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D99 RID: 28057 RVA: 0x0017A541 File Offset: 0x00178741
			public override void RemoveRange(int index, int count)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D9A RID: 28058 RVA: 0x0017A552 File Offset: 0x00178752
			public override void SetRange(int index, ICollection c)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D9B RID: 28059 RVA: 0x0017A564 File Offset: 0x00178764
			public override ArrayList GetRange(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this.Count - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				return new ArrayList.Range(this, index, count);
			}

			// Token: 0x06006D9C RID: 28060 RVA: 0x0017A5BC File Offset: 0x001787BC
			public override void Reverse(int index, int count)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D9D RID: 28061 RVA: 0x0017A5CD File Offset: 0x001787CD
			public override void Sort(int index, int count, IComparer comparer)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x06006D9E RID: 28062 RVA: 0x0017A5DE File Offset: 0x001787DE
			public override object[] ToArray()
			{
				return this._list.ToArray();
			}

			// Token: 0x06006D9F RID: 28063 RVA: 0x0017A5EB File Offset: 0x001787EB
			public override Array ToArray(Type type)
			{
				return this._list.ToArray(type);
			}

			// Token: 0x06006DA0 RID: 28064 RVA: 0x0017A5F9 File Offset: 0x001787F9
			public override void TrimToSize()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_ReadOnlyCollection"));
			}

			// Token: 0x04003554 RID: 13652
			private ArrayList _list;
		}

		// Token: 0x02000BAC RID: 2988
		[Serializable]
		private sealed class ArrayListEnumerator : IEnumerator, ICloneable
		{
			// Token: 0x06006DA1 RID: 28065 RVA: 0x0017A60A File Offset: 0x0017880A
			internal ArrayListEnumerator(ArrayList list, int index, int count)
			{
				this.list = list;
				this.startIndex = index;
				this.index = index - 1;
				this.endIndex = this.index + count;
				this.version = list._version;
				this.currentElement = null;
			}

			// Token: 0x06006DA2 RID: 28066 RVA: 0x0017A64A File Offset: 0x0017884A
			public object Clone()
			{
				return base.MemberwiseClone();
			}

			// Token: 0x06006DA3 RID: 28067 RVA: 0x0017A654 File Offset: 0x00178854
			public bool MoveNext()
			{
				if (this.version != this.list._version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
				}
				if (this.index < this.endIndex)
				{
					ArrayList arrayList = this.list;
					int num = this.index + 1;
					this.index = num;
					this.currentElement = arrayList[num];
					return true;
				}
				this.index = this.endIndex + 1;
				return false;
			}

			// Token: 0x17001295 RID: 4757
			// (get) Token: 0x06006DA4 RID: 28068 RVA: 0x0017A6C8 File Offset: 0x001788C8
			public object Current
			{
				get
				{
					if (this.index < this.startIndex)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumNotStarted"));
					}
					if (this.index > this.endIndex)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumEnded"));
					}
					return this.currentElement;
				}
			}

			// Token: 0x06006DA5 RID: 28069 RVA: 0x0017A717 File Offset: 0x00178917
			public void Reset()
			{
				if (this.version != this.list._version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
				}
				this.index = this.startIndex - 1;
			}

			// Token: 0x04003555 RID: 13653
			private ArrayList list;

			// Token: 0x04003556 RID: 13654
			private int index;

			// Token: 0x04003557 RID: 13655
			private int endIndex;

			// Token: 0x04003558 RID: 13656
			private int version;

			// Token: 0x04003559 RID: 13657
			private object currentElement;

			// Token: 0x0400355A RID: 13658
			private int startIndex;
		}

		// Token: 0x02000BAD RID: 2989
		[Serializable]
		private class Range : ArrayList
		{
			// Token: 0x06006DA6 RID: 28070 RVA: 0x0017A74A File Offset: 0x0017894A
			internal Range(ArrayList list, int index, int count)
				: base(false)
			{
				this._baseList = list;
				this._baseIndex = index;
				this._baseSize = count;
				this._baseVersion = list._version;
				this._version = list._version;
			}

			// Token: 0x06006DA7 RID: 28071 RVA: 0x0017A780 File Offset: 0x00178980
			private void InternalUpdateRange()
			{
				if (this._baseVersion != this._baseList._version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_UnderlyingArrayListChanged"));
				}
			}

			// Token: 0x06006DA8 RID: 28072 RVA: 0x0017A7A5 File Offset: 0x001789A5
			private void InternalUpdateVersion()
			{
				this._baseVersion++;
				this._version++;
			}

			// Token: 0x06006DA9 RID: 28073 RVA: 0x0017A7C4 File Offset: 0x001789C4
			public override int Add(object value)
			{
				this.InternalUpdateRange();
				this._baseList.Insert(this._baseIndex + this._baseSize, value);
				this.InternalUpdateVersion();
				int baseSize = this._baseSize;
				this._baseSize = baseSize + 1;
				return baseSize;
			}

			// Token: 0x06006DAA RID: 28074 RVA: 0x0017A808 File Offset: 0x00178A08
			public override void AddRange(ICollection c)
			{
				if (c == null)
				{
					throw new ArgumentNullException("c");
				}
				this.InternalUpdateRange();
				int count = c.Count;
				if (count > 0)
				{
					this._baseList.InsertRange(this._baseIndex + this._baseSize, c);
					this.InternalUpdateVersion();
					this._baseSize += count;
				}
			}

			// Token: 0x06006DAB RID: 28075 RVA: 0x0017A864 File Offset: 0x00178A64
			public override int BinarySearch(int index, int count, object value, IComparer comparer)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._baseSize - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				this.InternalUpdateRange();
				int num = this._baseList.BinarySearch(this._baseIndex + index, count, value, comparer);
				if (num >= 0)
				{
					return num - this._baseIndex;
				}
				return num + this._baseIndex;
			}

			// Token: 0x17001296 RID: 4758
			// (get) Token: 0x06006DAC RID: 28076 RVA: 0x0017A8E7 File Offset: 0x00178AE7
			// (set) Token: 0x06006DAD RID: 28077 RVA: 0x0017A8F4 File Offset: 0x00178AF4
			public override int Capacity
			{
				get
				{
					return this._baseList.Capacity;
				}
				set
				{
					if (value < this.Count)
					{
						throw new ArgumentOutOfRangeException("value", Environment.GetResourceString("ArgumentOutOfRange_SmallCapacity"));
					}
				}
			}

			// Token: 0x06006DAE RID: 28078 RVA: 0x0017A914 File Offset: 0x00178B14
			public override void Clear()
			{
				this.InternalUpdateRange();
				if (this._baseSize != 0)
				{
					this._baseList.RemoveRange(this._baseIndex, this._baseSize);
					this.InternalUpdateVersion();
					this._baseSize = 0;
				}
			}

			// Token: 0x06006DAF RID: 28079 RVA: 0x0017A948 File Offset: 0x00178B48
			public override object Clone()
			{
				this.InternalUpdateRange();
				return new ArrayList.Range(this._baseList, this._baseIndex, this._baseSize)
				{
					_baseList = (ArrayList)this._baseList.Clone()
				};
			}

			// Token: 0x06006DB0 RID: 28080 RVA: 0x0017A98C File Offset: 0x00178B8C
			public override bool Contains(object item)
			{
				this.InternalUpdateRange();
				if (item == null)
				{
					for (int i = 0; i < this._baseSize; i++)
					{
						if (this._baseList[this._baseIndex + i] == null)
						{
							return true;
						}
					}
					return false;
				}
				for (int j = 0; j < this._baseSize; j++)
				{
					if (this._baseList[this._baseIndex + j] != null && this._baseList[this._baseIndex + j].Equals(item))
					{
						return true;
					}
				}
				return false;
			}

			// Token: 0x06006DB1 RID: 28081 RVA: 0x0017AA10 File Offset: 0x00178C10
			public override void CopyTo(Array array, int index)
			{
				if (array == null)
				{
					throw new ArgumentNullException("array");
				}
				if (array.Rank != 1)
				{
					throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
				}
				if (index < 0)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (array.Length - index < this._baseSize)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				this.InternalUpdateRange();
				this._baseList.CopyTo(this._baseIndex, array, index, this._baseSize);
			}

			// Token: 0x06006DB2 RID: 28082 RVA: 0x0017AA9C File Offset: 0x00178C9C
			public override void CopyTo(int index, Array array, int arrayIndex, int count)
			{
				if (array == null)
				{
					throw new ArgumentNullException("array");
				}
				if (array.Rank != 1)
				{
					throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
				}
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (array.Length - arrayIndex < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				if (this._baseSize - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				this.InternalUpdateRange();
				this._baseList.CopyTo(this._baseIndex + index, array, arrayIndex, count);
			}

			// Token: 0x17001297 RID: 4759
			// (get) Token: 0x06006DB3 RID: 28083 RVA: 0x0017AB4E File Offset: 0x00178D4E
			public override int Count
			{
				get
				{
					this.InternalUpdateRange();
					return this._baseSize;
				}
			}

			// Token: 0x17001298 RID: 4760
			// (get) Token: 0x06006DB4 RID: 28084 RVA: 0x0017AB5C File Offset: 0x00178D5C
			public override bool IsReadOnly
			{
				get
				{
					return this._baseList.IsReadOnly;
				}
			}

			// Token: 0x17001299 RID: 4761
			// (get) Token: 0x06006DB5 RID: 28085 RVA: 0x0017AB69 File Offset: 0x00178D69
			public override bool IsFixedSize
			{
				get
				{
					return this._baseList.IsFixedSize;
				}
			}

			// Token: 0x1700129A RID: 4762
			// (get) Token: 0x06006DB6 RID: 28086 RVA: 0x0017AB76 File Offset: 0x00178D76
			public override bool IsSynchronized
			{
				get
				{
					return this._baseList.IsSynchronized;
				}
			}

			// Token: 0x06006DB7 RID: 28087 RVA: 0x0017AB83 File Offset: 0x00178D83
			public override IEnumerator GetEnumerator()
			{
				return this.GetEnumerator(0, this._baseSize);
			}

			// Token: 0x06006DB8 RID: 28088 RVA: 0x0017AB94 File Offset: 0x00178D94
			public override IEnumerator GetEnumerator(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._baseSize - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				this.InternalUpdateRange();
				return this._baseList.GetEnumerator(this._baseIndex + index, count);
			}

			// Token: 0x06006DB9 RID: 28089 RVA: 0x0017AC00 File Offset: 0x00178E00
			public override ArrayList GetRange(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._baseSize - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				this.InternalUpdateRange();
				return new ArrayList.Range(this, index, count);
			}

			// Token: 0x1700129B RID: 4763
			// (get) Token: 0x06006DBA RID: 28090 RVA: 0x0017AC5E File Offset: 0x00178E5E
			public override object SyncRoot
			{
				get
				{
					return this._baseList.SyncRoot;
				}
			}

			// Token: 0x06006DBB RID: 28091 RVA: 0x0017AC6C File Offset: 0x00178E6C
			public override int IndexOf(object value)
			{
				this.InternalUpdateRange();
				int num = this._baseList.IndexOf(value, this._baseIndex, this._baseSize);
				if (num >= 0)
				{
					return num - this._baseIndex;
				}
				return -1;
			}

			// Token: 0x06006DBC RID: 28092 RVA: 0x0017ACA8 File Offset: 0x00178EA8
			public override int IndexOf(object value, int startIndex)
			{
				if (startIndex < 0)
				{
					throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (startIndex > this._baseSize)
				{
					throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				this.InternalUpdateRange();
				int num = this._baseList.IndexOf(value, this._baseIndex + startIndex, this._baseSize - startIndex);
				if (num >= 0)
				{
					return num - this._baseIndex;
				}
				return -1;
			}

			// Token: 0x06006DBD RID: 28093 RVA: 0x0017AD20 File Offset: 0x00178F20
			public override int IndexOf(object value, int startIndex, int count)
			{
				if (startIndex < 0 || startIndex > this._baseSize)
				{
					throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				if (count < 0 || startIndex > this._baseSize - count)
				{
					throw new ArgumentOutOfRangeException("count", Environment.GetResourceString("ArgumentOutOfRange_Count"));
				}
				this.InternalUpdateRange();
				int num = this._baseList.IndexOf(value, this._baseIndex + startIndex, count);
				if (num >= 0)
				{
					return num - this._baseIndex;
				}
				return -1;
			}

			// Token: 0x06006DBE RID: 28094 RVA: 0x0017ADA0 File Offset: 0x00178FA0
			public override void Insert(int index, object value)
			{
				if (index < 0 || index > this._baseSize)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				this.InternalUpdateRange();
				this._baseList.Insert(this._baseIndex + index, value);
				this.InternalUpdateVersion();
				this._baseSize++;
			}

			// Token: 0x06006DBF RID: 28095 RVA: 0x0017AE00 File Offset: 0x00179000
			public override void InsertRange(int index, ICollection c)
			{
				if (index < 0 || index > this._baseSize)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				if (c == null)
				{
					throw new ArgumentNullException("c");
				}
				this.InternalUpdateRange();
				int count = c.Count;
				if (count > 0)
				{
					this._baseList.InsertRange(this._baseIndex + index, c);
					this._baseSize += count;
					this.InternalUpdateVersion();
				}
			}

			// Token: 0x06006DC0 RID: 28096 RVA: 0x0017AE78 File Offset: 0x00179078
			public override int LastIndexOf(object value)
			{
				this.InternalUpdateRange();
				int num = this._baseList.LastIndexOf(value, this._baseIndex + this._baseSize - 1, this._baseSize);
				if (num >= 0)
				{
					return num - this._baseIndex;
				}
				return -1;
			}

			// Token: 0x06006DC1 RID: 28097 RVA: 0x0017AEBB File Offset: 0x001790BB
			public override int LastIndexOf(object value, int startIndex)
			{
				return this.LastIndexOf(value, startIndex, startIndex + 1);
			}

			// Token: 0x06006DC2 RID: 28098 RVA: 0x0017AEC8 File Offset: 0x001790C8
			public override int LastIndexOf(object value, int startIndex, int count)
			{
				this.InternalUpdateRange();
				if (this._baseSize == 0)
				{
					return -1;
				}
				if (startIndex >= this._baseSize)
				{
					throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				if (startIndex < 0)
				{
					throw new ArgumentOutOfRangeException("startIndex", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				int num = this._baseList.LastIndexOf(value, this._baseIndex + startIndex, count);
				if (num >= 0)
				{
					return num - this._baseIndex;
				}
				return -1;
			}

			// Token: 0x06006DC3 RID: 28099 RVA: 0x0017AF40 File Offset: 0x00179140
			public override void RemoveAt(int index)
			{
				if (index < 0 || index >= this._baseSize)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				this.InternalUpdateRange();
				this._baseList.RemoveAt(this._baseIndex + index);
				this.InternalUpdateVersion();
				this._baseSize--;
			}

			// Token: 0x06006DC4 RID: 28100 RVA: 0x0017AF9C File Offset: 0x0017919C
			public override void RemoveRange(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._baseSize - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				this.InternalUpdateRange();
				if (count > 0)
				{
					this._baseList.RemoveRange(this._baseIndex + index, count);
					this.InternalUpdateVersion();
					this._baseSize -= count;
				}
			}

			// Token: 0x06006DC5 RID: 28101 RVA: 0x0017B020 File Offset: 0x00179220
			public override void Reverse(int index, int count)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._baseSize - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				this.InternalUpdateRange();
				this._baseList.Reverse(this._baseIndex + index, count);
				this.InternalUpdateVersion();
			}

			// Token: 0x06006DC6 RID: 28102 RVA: 0x0017B090 File Offset: 0x00179290
			public override void SetRange(int index, ICollection c)
			{
				this.InternalUpdateRange();
				if (index < 0 || index >= this._baseSize)
				{
					throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
				}
				this._baseList.SetRange(this._baseIndex + index, c);
				if (c.Count > 0)
				{
					this.InternalUpdateVersion();
				}
			}

			// Token: 0x06006DC7 RID: 28103 RVA: 0x0017B0E8 File Offset: 0x001792E8
			public override void Sort(int index, int count, IComparer comparer)
			{
				if (index < 0 || count < 0)
				{
					throw new ArgumentOutOfRangeException((index < 0) ? "index" : "count", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
				}
				if (this._baseSize - index < count)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_InvalidOffLen"));
				}
				this.InternalUpdateRange();
				this._baseList.Sort(this._baseIndex + index, count, comparer);
				this.InternalUpdateVersion();
			}

			// Token: 0x1700129C RID: 4764
			public override object this[int index]
			{
				get
				{
					this.InternalUpdateRange();
					if (index < 0 || index >= this._baseSize)
					{
						throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
					}
					return this._baseList[this._baseIndex + index];
				}
				set
				{
					this.InternalUpdateRange();
					if (index < 0 || index >= this._baseSize)
					{
						throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
					}
					this._baseList[this._baseIndex + index] = value;
					this.InternalUpdateVersion();
				}
			}

			// Token: 0x06006DCA RID: 28106 RVA: 0x0017B1E8 File Offset: 0x001793E8
			public override object[] ToArray()
			{
				this.InternalUpdateRange();
				object[] array = new object[this._baseSize];
				Array.Copy(this._baseList._items, this._baseIndex, array, 0, this._baseSize);
				return array;
			}

			// Token: 0x06006DCB RID: 28107 RVA: 0x0017B228 File Offset: 0x00179428
			[SecuritySafeCritical]
			public override Array ToArray(Type type)
			{
				if (type == null)
				{
					throw new ArgumentNullException("type");
				}
				this.InternalUpdateRange();
				Array array = Array.UnsafeCreateInstance(type, this._baseSize);
				this._baseList.CopyTo(this._baseIndex, array, 0, this._baseSize);
				return array;
			}

			// Token: 0x06006DCC RID: 28108 RVA: 0x0017B276 File Offset: 0x00179476
			public override void TrimToSize()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_RangeCollection"));
			}

			// Token: 0x0400355B RID: 13659
			private ArrayList _baseList;

			// Token: 0x0400355C RID: 13660
			private int _baseIndex;

			// Token: 0x0400355D RID: 13661
			private int _baseSize;

			// Token: 0x0400355E RID: 13662
			private int _baseVersion;
		}

		// Token: 0x02000BAE RID: 2990
		[Serializable]
		private sealed class ArrayListEnumeratorSimple : IEnumerator, ICloneable
		{
			// Token: 0x06006DCD RID: 28109 RVA: 0x0017B288 File Offset: 0x00179488
			internal ArrayListEnumeratorSimple(ArrayList list)
			{
				this.list = list;
				this.index = -1;
				this.version = list._version;
				this.isArrayList = list.GetType() == typeof(ArrayList);
				this.currentElement = ArrayList.ArrayListEnumeratorSimple.dummyObject;
			}

			// Token: 0x06006DCE RID: 28110 RVA: 0x0017B2DB File Offset: 0x001794DB
			public object Clone()
			{
				return base.MemberwiseClone();
			}

			// Token: 0x06006DCF RID: 28111 RVA: 0x0017B2E4 File Offset: 0x001794E4
			public bool MoveNext()
			{
				if (this.version != this.list._version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
				}
				if (this.isArrayList)
				{
					if (this.index < this.list._size - 1)
					{
						object[] items = this.list._items;
						int num = this.index + 1;
						this.index = num;
						this.currentElement = items[num];
						return true;
					}
					this.currentElement = ArrayList.ArrayListEnumeratorSimple.dummyObject;
					this.index = this.list._size;
					return false;
				}
				else
				{
					if (this.index < this.list.Count - 1)
					{
						ArrayList arrayList = this.list;
						int num = this.index + 1;
						this.index = num;
						this.currentElement = arrayList[num];
						return true;
					}
					this.index = this.list.Count;
					this.currentElement = ArrayList.ArrayListEnumeratorSimple.dummyObject;
					return false;
				}
			}

			// Token: 0x1700129D RID: 4765
			// (get) Token: 0x06006DD0 RID: 28112 RVA: 0x0017B3CC File Offset: 0x001795CC
			public object Current
			{
				get
				{
					object obj = this.currentElement;
					if (ArrayList.ArrayListEnumeratorSimple.dummyObject != obj)
					{
						return obj;
					}
					if (this.index == -1)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumNotStarted"));
					}
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumEnded"));
				}
			}

			// Token: 0x06006DD1 RID: 28113 RVA: 0x0017B412 File Offset: 0x00179612
			public void Reset()
			{
				if (this.version != this.list._version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
				}
				this.currentElement = ArrayList.ArrayListEnumeratorSimple.dummyObject;
				this.index = -1;
			}

			// Token: 0x0400355F RID: 13663
			private ArrayList list;

			// Token: 0x04003560 RID: 13664
			private int index;

			// Token: 0x04003561 RID: 13665
			private int version;

			// Token: 0x04003562 RID: 13666
			private object currentElement;

			// Token: 0x04003563 RID: 13667
			[NonSerialized]
			private bool isArrayList;

			// Token: 0x04003564 RID: 13668
			private static object dummyObject = new object();
		}

		// Token: 0x02000BAF RID: 2991
		internal class ArrayListDebugView
		{
			// Token: 0x06006DD3 RID: 28115 RVA: 0x0017B455 File Offset: 0x00179655
			public ArrayListDebugView(ArrayList arrayList)
			{
				if (arrayList == null)
				{
					throw new ArgumentNullException("arrayList");
				}
				this.arrayList = arrayList;
			}

			// Token: 0x1700129E RID: 4766
			// (get) Token: 0x06006DD4 RID: 28116 RVA: 0x0017B472 File Offset: 0x00179672
			[DebuggerBrowsable(DebuggerBrowsableState.RootHidden)]
			public object[] Items
			{
				get
				{
					return this.arrayList.ToArray();
				}
			}

			// Token: 0x04003565 RID: 13669
			private ArrayList arrayList;
		}
	}
}
