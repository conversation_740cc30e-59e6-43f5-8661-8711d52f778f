﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004C3 RID: 1219
	[Serializable]
	internal class ObjectEqualityComparer<T> : EqualityComparer<T>
	{
		// Token: 0x06003A91 RID: 14993 RVA: 0x000DF42E File Offset: 0x000DD62E
		public override bool Equals(T x, T y)
		{
			if (x != null)
			{
				return y != null && x.Equals(y);
			}
			return y == null;
		}

		// Token: 0x06003A92 RID: 14994 RVA: 0x000DF461 File Offset: 0x000DD661
		public override int GetHashCode(T obj)
		{
			if (obj == null)
			{
				return 0;
			}
			return obj.GetHashCode();
		}

		// Token: 0x06003A93 RID: 14995 RVA: 0x000DF47C File Offset: 0x000DD67C
		internal override int IndexOf(T[] array, T value, int startIndex, int count)
		{
			int num = startIndex + count;
			if (value == null)
			{
				for (int i = startIndex; i < num; i++)
				{
					if (array[i] == null)
					{
						return i;
					}
				}
			}
			else
			{
				for (int j = startIndex; j < num; j++)
				{
					if (array[j] != null && array[j].Equals(value))
					{
						return j;
					}
				}
			}
			return -1;
		}

		// Token: 0x06003A94 RID: 14996 RVA: 0x000DF4F0 File Offset: 0x000DD6F0
		internal override int LastIndexOf(T[] array, T value, int startIndex, int count)
		{
			int num = startIndex - count + 1;
			if (value == null)
			{
				for (int i = startIndex; i >= num; i--)
				{
					if (array[i] == null)
					{
						return i;
					}
				}
			}
			else
			{
				for (int j = startIndex; j >= num; j--)
				{
					if (array[j] != null && array[j].Equals(value))
					{
						return j;
					}
				}
			}
			return -1;
		}

		// Token: 0x06003A95 RID: 14997 RVA: 0x000DF564 File Offset: 0x000DD764
		public override bool Equals(object obj)
		{
			ObjectEqualityComparer<T> objectEqualityComparer = obj as ObjectEqualityComparer<T>;
			return objectEqualityComparer != null;
		}

		// Token: 0x06003A96 RID: 14998 RVA: 0x000DF57C File Offset: 0x000DD77C
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}
	}
}
