﻿using System;
using System.Runtime.CompilerServices;

namespace System.Collections.Generic
{
	// Token: 0x020004D6 RID: 1238
	[TypeDependency("System.SZArrayHelper")]
	[__DynamicallyInvokable]
	public interface IList<T> : ICollection<T>, IEnumerable<T>, IEnumerable
	{
		// Token: 0x170008EC RID: 2284
		[__DynamicallyInvokable]
		T this[int index]
		{
			[__DynamicallyInvokable]
			get;
			[__DynamicallyInvokable]
			set;
		}

		// Token: 0x06003AE3 RID: 15075
		[__DynamicallyInvokable]
		int IndexOf(T item);

		// Token: 0x06003AE4 RID: 15076
		[__DynamicallyInvokable]
		void Insert(int index, T item);

		// Token: 0x06003AE5 RID: 15077
		[__DynamicallyInvokable]
		void RemoveAt(int index);
	}
}
