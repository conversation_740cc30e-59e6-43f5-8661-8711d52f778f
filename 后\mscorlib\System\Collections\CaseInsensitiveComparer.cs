﻿using System;
using System.Globalization;
using System.Runtime.InteropServices;

namespace System.Collections
{
	// Token: 0x0200048A RID: 1162
	[ComVisible(true)]
	[Serializable]
	public class CaseInsensitiveComparer : IComparer
	{
		// Token: 0x06003776 RID: 14198 RVA: 0x000D5636 File Offset: 0x000D3836
		public CaseInsensitiveComparer()
		{
			this.m_compareInfo = CultureInfo.CurrentCulture.CompareInfo;
		}

		// Token: 0x06003777 RID: 14199 RVA: 0x000D564E File Offset: 0x000D384E
		public CaseInsensitiveComparer(CultureInfo culture)
		{
			if (culture == null)
			{
				throw new ArgumentNullException("culture");
			}
			this.m_compareInfo = culture.CompareInfo;
		}

		// Token: 0x1700081F RID: 2079
		// (get) Token: 0x06003778 RID: 14200 RVA: 0x000D5670 File Offset: 0x000D3870
		public static CaseInsensitiveComparer Default
		{
			get
			{
				return new CaseInsensitiveComparer(CultureInfo.CurrentCulture);
			}
		}

		// Token: 0x17000820 RID: 2080
		// (get) Token: 0x06003779 RID: 14201 RVA: 0x000D567C File Offset: 0x000D387C
		public static CaseInsensitiveComparer DefaultInvariant
		{
			get
			{
				if (CaseInsensitiveComparer.m_InvariantCaseInsensitiveComparer == null)
				{
					CaseInsensitiveComparer.m_InvariantCaseInsensitiveComparer = new CaseInsensitiveComparer(CultureInfo.InvariantCulture);
				}
				return CaseInsensitiveComparer.m_InvariantCaseInsensitiveComparer;
			}
		}

		// Token: 0x0600377A RID: 14202 RVA: 0x000D56A0 File Offset: 0x000D38A0
		public int Compare(object a, object b)
		{
			string text = a as string;
			string text2 = b as string;
			if (text != null && text2 != null)
			{
				return this.m_compareInfo.Compare(text, text2, CompareOptions.IgnoreCase);
			}
			return Comparer.Default.Compare(a, b);
		}

		// Token: 0x040018B5 RID: 6325
		private CompareInfo m_compareInfo;

		// Token: 0x040018B6 RID: 6326
		private static volatile CaseInsensitiveComparer m_InvariantCaseInsensitiveComparer;
	}
}
