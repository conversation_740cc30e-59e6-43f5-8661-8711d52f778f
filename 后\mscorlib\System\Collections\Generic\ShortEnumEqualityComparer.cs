﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;

namespace System.Collections.Generic
{
	// Token: 0x020004C7 RID: 1223
	[Serializable]
	internal sealed class ShortEnumEqualityComparer<T> : EnumEqualityComparer<T>, ISerializable where T : struct
	{
		// Token: 0x06003AA9 RID: 15017 RVA: 0x000DF76A File Offset: 0x000DD96A
		public ShortEnumEqualityComparer()
		{
		}

		// Token: 0x06003AAA RID: 15018 RVA: 0x000DF772 File Offset: 0x000DD972
		public ShortEnumEqualityComparer(SerializationInfo information, StreamingContext context)
		{
		}

		// Token: 0x06003AAB RID: 15019 RVA: 0x000DF77C File Offset: 0x000DD97C
		public override int GetHashCode(T obj)
		{
			int num = JitHelpers.UnsafeEnumCast<T>(obj);
			return ((short)num).GetHashCode();
		}
	}
}
