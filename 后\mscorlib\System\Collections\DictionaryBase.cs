﻿using System;
using System.Runtime.InteropServices;

namespace System.Collections
{
	// Token: 0x0200048D RID: 1165
	[ComVisible(true)]
	[Serializable]
	public abstract class DictionaryBase : IDictionary, ICollection, IEnumerable
	{
		// Token: 0x1700082C RID: 2092
		// (get) Token: 0x060037A0 RID: 14240 RVA: 0x000D5B25 File Offset: 0x000D3D25
		protected Hashtable InnerHashtable
		{
			get
			{
				if (this.hashtable == null)
				{
					this.hashtable = new Hashtable();
				}
				return this.hashtable;
			}
		}

		// Token: 0x1700082D RID: 2093
		// (get) Token: 0x060037A1 RID: 14241 RVA: 0x000D5B40 File Offset: 0x000D3D40
		protected IDictionary Dictionary
		{
			get
			{
				return this;
			}
		}

		// Token: 0x1700082E RID: 2094
		// (get) Token: 0x060037A2 RID: 14242 RVA: 0x000D5B43 File Offset: 0x000D3D43
		public int Count
		{
			get
			{
				if (this.hashtable != null)
				{
					return this.hashtable.Count;
				}
				return 0;
			}
		}

		// Token: 0x1700082F RID: 2095
		// (get) Token: 0x060037A3 RID: 14243 RVA: 0x000D5B5A File Offset: 0x000D3D5A
		bool IDictionary.IsReadOnly
		{
			get
			{
				return this.InnerHashtable.IsReadOnly;
			}
		}

		// Token: 0x17000830 RID: 2096
		// (get) Token: 0x060037A4 RID: 14244 RVA: 0x000D5B67 File Offset: 0x000D3D67
		bool IDictionary.IsFixedSize
		{
			get
			{
				return this.InnerHashtable.IsFixedSize;
			}
		}

		// Token: 0x17000831 RID: 2097
		// (get) Token: 0x060037A5 RID: 14245 RVA: 0x000D5B74 File Offset: 0x000D3D74
		bool ICollection.IsSynchronized
		{
			get
			{
				return this.InnerHashtable.IsSynchronized;
			}
		}

		// Token: 0x17000832 RID: 2098
		// (get) Token: 0x060037A6 RID: 14246 RVA: 0x000D5B81 File Offset: 0x000D3D81
		ICollection IDictionary.Keys
		{
			get
			{
				return this.InnerHashtable.Keys;
			}
		}

		// Token: 0x17000833 RID: 2099
		// (get) Token: 0x060037A7 RID: 14247 RVA: 0x000D5B8E File Offset: 0x000D3D8E
		object ICollection.SyncRoot
		{
			get
			{
				return this.InnerHashtable.SyncRoot;
			}
		}

		// Token: 0x17000834 RID: 2100
		// (get) Token: 0x060037A8 RID: 14248 RVA: 0x000D5B9B File Offset: 0x000D3D9B
		ICollection IDictionary.Values
		{
			get
			{
				return this.InnerHashtable.Values;
			}
		}

		// Token: 0x060037A9 RID: 14249 RVA: 0x000D5BA8 File Offset: 0x000D3DA8
		public void CopyTo(Array array, int index)
		{
			this.InnerHashtable.CopyTo(array, index);
		}

		// Token: 0x17000835 RID: 2101
		object IDictionary.this[object key]
		{
			get
			{
				object obj = this.InnerHashtable[key];
				this.OnGet(key, obj);
				return obj;
			}
			set
			{
				this.OnValidate(key, value);
				bool flag = true;
				object obj = this.InnerHashtable[key];
				if (obj == null)
				{
					flag = this.InnerHashtable.Contains(key);
				}
				this.OnSet(key, obj, value);
				this.InnerHashtable[key] = value;
				try
				{
					this.OnSetComplete(key, obj, value);
				}
				catch
				{
					if (flag)
					{
						this.InnerHashtable[key] = obj;
					}
					else
					{
						this.InnerHashtable.Remove(key);
					}
					throw;
				}
			}
		}

		// Token: 0x060037AC RID: 14252 RVA: 0x000D5C64 File Offset: 0x000D3E64
		bool IDictionary.Contains(object key)
		{
			return this.InnerHashtable.Contains(key);
		}

		// Token: 0x060037AD RID: 14253 RVA: 0x000D5C74 File Offset: 0x000D3E74
		void IDictionary.Add(object key, object value)
		{
			this.OnValidate(key, value);
			this.OnInsert(key, value);
			this.InnerHashtable.Add(key, value);
			try
			{
				this.OnInsertComplete(key, value);
			}
			catch
			{
				this.InnerHashtable.Remove(key);
				throw;
			}
		}

		// Token: 0x060037AE RID: 14254 RVA: 0x000D5CC8 File Offset: 0x000D3EC8
		public void Clear()
		{
			this.OnClear();
			this.InnerHashtable.Clear();
			this.OnClearComplete();
		}

		// Token: 0x060037AF RID: 14255 RVA: 0x000D5CE4 File Offset: 0x000D3EE4
		void IDictionary.Remove(object key)
		{
			if (this.InnerHashtable.Contains(key))
			{
				object obj = this.InnerHashtable[key];
				this.OnValidate(key, obj);
				this.OnRemove(key, obj);
				this.InnerHashtable.Remove(key);
				try
				{
					this.OnRemoveComplete(key, obj);
				}
				catch
				{
					this.InnerHashtable.Add(key, obj);
					throw;
				}
			}
		}

		// Token: 0x060037B0 RID: 14256 RVA: 0x000D5D54 File Offset: 0x000D3F54
		public IDictionaryEnumerator GetEnumerator()
		{
			return this.InnerHashtable.GetEnumerator();
		}

		// Token: 0x060037B1 RID: 14257 RVA: 0x000D5D61 File Offset: 0x000D3F61
		IEnumerator IEnumerable.GetEnumerator()
		{
			return this.InnerHashtable.GetEnumerator();
		}

		// Token: 0x060037B2 RID: 14258 RVA: 0x000D5D6E File Offset: 0x000D3F6E
		protected virtual object OnGet(object key, object currentValue)
		{
			return currentValue;
		}

		// Token: 0x060037B3 RID: 14259 RVA: 0x000D5D71 File Offset: 0x000D3F71
		protected virtual void OnSet(object key, object oldValue, object newValue)
		{
		}

		// Token: 0x060037B4 RID: 14260 RVA: 0x000D5D73 File Offset: 0x000D3F73
		protected virtual void OnInsert(object key, object value)
		{
		}

		// Token: 0x060037B5 RID: 14261 RVA: 0x000D5D75 File Offset: 0x000D3F75
		protected virtual void OnClear()
		{
		}

		// Token: 0x060037B6 RID: 14262 RVA: 0x000D5D77 File Offset: 0x000D3F77
		protected virtual void OnRemove(object key, object value)
		{
		}

		// Token: 0x060037B7 RID: 14263 RVA: 0x000D5D79 File Offset: 0x000D3F79
		protected virtual void OnValidate(object key, object value)
		{
		}

		// Token: 0x060037B8 RID: 14264 RVA: 0x000D5D7B File Offset: 0x000D3F7B
		protected virtual void OnSetComplete(object key, object oldValue, object newValue)
		{
		}

		// Token: 0x060037B9 RID: 14265 RVA: 0x000D5D7D File Offset: 0x000D3F7D
		protected virtual void OnInsertComplete(object key, object value)
		{
		}

		// Token: 0x060037BA RID: 14266 RVA: 0x000D5D7F File Offset: 0x000D3F7F
		protected virtual void OnClearComplete()
		{
		}

		// Token: 0x060037BB RID: 14267 RVA: 0x000D5D81 File Offset: 0x000D3F81
		protected virtual void OnRemoveComplete(object key, object value)
		{
		}

		// Token: 0x040018BA RID: 6330
		private Hashtable hashtable;
	}
}
