using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Reflection;
using Game.Base.Packets;
using Game.Logic.Actions;
using Game.Logic.Phy.Maps;
using Game.Logic.Phy.Object;
using log4net;
using SqlDataProvider.Data;

namespace Game.Logic
{
	// Token: 0x02000499 RID: 1177
	public class BaseGame : AbstractGame
	{
		// Token: 0x17000E23 RID: 3619
		// (get) Token: 0x06003183 RID: 12675 RVA: 0x00017F3B File Offset: 0x0001613B
		public int RoomId
		{
			get
			{
				return this.m_roomId;
			}
		}

		// Token: 0x17000E24 RID: 3620
		// (get) Token: 0x06003184 RID: 12676 RVA: 0x00017F43 File Offset: 0x00016143
		public Dictionary<int, Player> Players
		{
			get
			{
				return this.m_players;
			}
		}

		// Token: 0x06003185 RID: 12677 RVA: 0x000BBDC4 File Offset: 0x000B9FC4
		internal void sendShowPicSkil(Living player, PetSkillElementInfo info, bool isActive)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(145);
			gspacketIn.WriteInt(info.ID);
			gspacketIn.WriteString(info.Name);
			gspacketIn.WriteString(info.Description);
			gspacketIn.WriteString(info.Pic.ToString());
			gspacketIn.WriteString(info.EffectPic);
			gspacketIn.WriteBoolean(isActive);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003186 RID: 12678 RVA: 0x0000E6BD File Offset: 0x0000C8BD
		internal void SendlivingBoltmove(Player player, int x, int y)
		{
			throw new NotImplementedException();
		}

		// Token: 0x06003187 RID: 12679 RVA: 0x000BBE54 File Offset: 0x000BA054
		public void AddPlayerSadowHandler(Player living_0, int X, int Y, int IdObj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living_0.Id);
			gspacketIn.Parameter1 = living_0.Id;
			gspacketIn.WriteByte(157);
			gspacketIn.WriteByte(18);
			gspacketIn.WriteInt(IdObj);
			gspacketIn.WriteInt(X);
			gspacketIn.WriteInt(living_0.Y);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteInt(1);
			gspacketIn.WriteByte(1);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteBoolean(false);
			gspacketIn.WriteInt(living_0.Id);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x17000E25 RID: 3621
		// (get) Token: 0x06003188 RID: 12680 RVA: 0x000BBEF8 File Offset: 0x000BA0F8
		public int PlayerCount
		{
			get
			{
				Dictionary<int, Player> players = this.m_players;
				int count;
				lock (players)
				{
					count = this.m_players.Count;
				}
				return count;
			}
		}

		// Token: 0x17000E26 RID: 3622
		// (get) Token: 0x06003189 RID: 12681 RVA: 0x000BBF44 File Offset: 0x000BA144
		// (set) Token: 0x0600318A RID: 12682 RVA: 0x00017F4B File Offset: 0x0001614B
		public int TurnIndex
		{
			get
			{
				return this.m_turnIndex;
			}
			set
			{
				this.m_turnIndex = value;
			}
		}

		// Token: 0x17000E27 RID: 3623
		// (get) Token: 0x0600318B RID: 12683 RVA: 0x000BBF5C File Offset: 0x000BA15C
		// (set) Token: 0x0600318C RID: 12684 RVA: 0x00017F56 File Offset: 0x00016156
		protected int m_turnIndex
		{
			get
			{
				return this.turnIndex;
			}
			set
			{
				this.turnIndex = value;
			}
		}

		// Token: 0x17000E28 RID: 3624
		// (get) Token: 0x0600318D RID: 12685 RVA: 0x00017F60 File Offset: 0x00016160
		public eGameState GameState
		{
			get
			{
				return this.m_gameState;
			}
		}

		// Token: 0x17000E29 RID: 3625
		// (get) Token: 0x0600318E RID: 12686 RVA: 0x00017F68 File Offset: 0x00016168
		public eGameState GameSecondState
		{
			get
			{
				return this.m_gameSecondState;
			}
		}

		// Token: 0x17000E2A RID: 3626
		// (get) Token: 0x0600318F RID: 12687 RVA: 0x00017F70 File Offset: 0x00016170
		public float Wind
		{
			get
			{
				return this.m_map.wind;
			}
		}

		// Token: 0x17000E2B RID: 3627
		// (get) Token: 0x06003190 RID: 12688 RVA: 0x00017F7D File Offset: 0x0001617D
		public Map Map
		{
			get
			{
				return this.m_map;
			}
		}

		// Token: 0x17000E2C RID: 3628
		// (get) Token: 0x06003191 RID: 12689 RVA: 0x00017F85 File Offset: 0x00016185
		public List<TurnedLiving> TurnQueue
		{
			get
			{
				return this.m_turnQueue;
			}
		}

		// Token: 0x17000E2D RID: 3629
		// (get) Token: 0x06003192 RID: 12690 RVA: 0x00017F8D File Offset: 0x0001618D
		public bool HasPlayer
		{
			get
			{
				return this.m_players.Count > 0;
			}
		}

		// Token: 0x17000E2E RID: 3630
		// (get) Token: 0x06003193 RID: 12691 RVA: 0x00017F9D File Offset: 0x0001619D
		public Random Random
		{
			get
			{
				return this.m_random;
			}
		}

		// Token: 0x17000E2F RID: 3631
		// (get) Token: 0x06003194 RID: 12692 RVA: 0x00017FA5 File Offset: 0x000161A5
		public TurnedLiving CurrentLiving
		{
			get
			{
				return this.m_currentLiving;
			}
		}

		// Token: 0x17000E30 RID: 3632
		// (get) Token: 0x06003195 RID: 12693 RVA: 0x00017FAD File Offset: 0x000161AD
		public int LifeTime
		{
			get
			{
				return this.m_lifeTime;
			}
		}

		// Token: 0x1400001F RID: 31
		// (add) Token: 0x06003196 RID: 12694 RVA: 0x000BBF74 File Offset: 0x000BA174
		// (remove) Token: 0x06003197 RID: 12695 RVA: 0x000BBFAC File Offset: 0x000BA1AC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event GameEventHandle GameOverred;

		// Token: 0x14000020 RID: 32
		// (add) Token: 0x06003198 RID: 12696 RVA: 0x000BBFE4 File Offset: 0x000BA1E4
		// (remove) Token: 0x06003199 RID: 12697 RVA: 0x000BC01C File Offset: 0x000BA21C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event GameEventHandle BeginNewTurn;

		// Token: 0x14000021 RID: 33
		// (add) Token: 0x0600319A RID: 12698 RVA: 0x000BC054 File Offset: 0x000BA254
		// (remove) Token: 0x0600319B RID: 12699 RVA: 0x000BC08C File Offset: 0x000BA28C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event BaseGame.GameOverLogEventHandle GameOverLog;

		// Token: 0x14000022 RID: 34
		// (add) Token: 0x0600319C RID: 12700 RVA: 0x000BC0C4 File Offset: 0x000BA2C4
		// (remove) Token: 0x0600319D RID: 12701 RVA: 0x000BC0FC File Offset: 0x000BA2FC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event BaseGame.GameNpcDieEventHandle GameNpcDie;

		// Token: 0x0600319E RID: 12702 RVA: 0x000BC134 File Offset: 0x000BA334
		public BaseGame(int id, int roomId, Map map, eRoomType roomType, eGameType gameType, int timeType)
			: base(id, roomType, gameType, timeType)
		{
			this.m_roomId = roomId;
			this.m_players = new Dictionary<int, Player>();
			this.m_turnQueue = new List<TurnedLiving>();
			this.m_livings = new List<Living>();
			this.m_decklivings = new List<Living>();
			this.m_random = new Random();
			this.m_logStartIps = new Dictionary<int, string>();
			this.m_map = map;
			this.m_actions = new ArrayList();
			this.PhysicalId = 0;
			this.BossWarField = "";
			this.FrozenWind = false;
			this.m_tempBox = new List<SimpleBox>();
			this.m_tempPoints = new List<Point>();
			this.m_tempGhostPoints = new List<Point>();
			bool flag = roomType == eRoomType.Dungeon || roomType == eRoomType.Boss;
			if (flag)
			{
				this.Cards = new int[21];
			}
			else
			{
				this.Cards = new int[8];
			}
			this.VortexBombKillCount = 0;
			this.m_gameState = eGameState.Inited;
		}

		// Token: 0x0600319F RID: 12703 RVA: 0x00017FB5 File Offset: 0x000161B5
		public void SetWind(int wind)
		{
			this.m_map.wind = (float)wind;
		}

		// Token: 0x060031A0 RID: 12704 RVA: 0x000BC27C File Offset: 0x000BA47C
		public bool SetMap(int mapId)
		{
			bool flag = this.GameState == eGameState.Playing;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				Map map = MapMgr.CloneMap(mapId);
				bool flag3 = map != null;
				if (flag3)
				{
					this.m_map = map;
					flag2 = true;
				}
				else
				{
					flag2 = false;
				}
			}
			return flag2;
		}

		// Token: 0x060031A1 RID: 12705 RVA: 0x000BC2BC File Offset: 0x000BA4BC
		public int GetTurnWaitTime()
		{
			return this.m_timeType;
		}

		// Token: 0x060031A2 RID: 12706 RVA: 0x000BC2D4 File Offset: 0x000BA4D4
		protected void AddLogIp(int id, string ip)
		{
			Dictionary<int, string> logStartIps = this.m_logStartIps;
			lock (logStartIps)
			{
				bool flag2 = !this.m_logStartIps.ContainsKey(id);
				if (flag2)
				{
					this.m_logStartIps.Add(id, ip);
				}
			}
		}

		// Token: 0x060031A3 RID: 12707 RVA: 0x000BC338 File Offset: 0x000BA538
		protected void AddPlayer(Player fp)
		{
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				this.m_players.Add(fp.Id, fp);
				bool flag2 = fp.Weapon != null;
				if (flag2)
				{
					this.m_turnQueue.Add(fp);
				}
			}
		}

		// Token: 0x060031A4 RID: 12708 RVA: 0x000BC3A8 File Offset: 0x000BA5A8
		public bool IsPVE()
		{
			eRoomType roomType = base.RoomType;
			eRoomType eRoomType = roomType;
			return eRoomType - eRoomType.Boss <= 1 || eRoomType == eRoomType.Academy;
		}

		// Token: 0x060031A5 RID: 12709 RVA: 0x000BC3DC File Offset: 0x000BA5DC
		public virtual void AddLiving(Living living)
		{
			this.m_map.AddPhysical(living);
			bool flag = living is Player;
			if (flag)
			{
				Player player = living as Player;
				bool flag2 = player.Weapon == null;
				if (flag2)
				{
					return;
				}
			}
			bool flag3 = living is TurnedLiving;
			if (flag3)
			{
				this.m_turnQueue.Add(living as TurnedLiving);
			}
			else
			{
				bool flag4 = ((SimpleNpc)living).Type != eLivingType.SimpleNpcDeck;
				if (flag4)
				{
					this.m_livings.Add(living);
				}
				else
				{
					this.m_decklivings.Add(living);
				}
			}
			bool flag5 = !(living is Player);
			if (flag5)
			{
				this.SendAddLiving(living);
			}
		}

		// Token: 0x060031A6 RID: 12710 RVA: 0x00017FC6 File Offset: 0x000161C6
		public virtual void AddGhostBoxObj(PhysicalObj phy)
		{
			this.m_map.AddPhysical(phy);
			phy.SetGame(this);
		}

		// Token: 0x060031A7 RID: 12711 RVA: 0x000BC494 File Offset: 0x000BA694
		public virtual void AddPhysicalObj(PhysicalObj phy, bool sendToClient)
		{
			this.m_map.AddPhysical(phy);
			phy.SetGame(this);
			if (sendToClient)
			{
				this.SendAddPhysicalObj(phy);
			}
		}

		// Token: 0x060031A8 RID: 12712 RVA: 0x000BC4C8 File Offset: 0x000BA6C8
		public virtual void AddPhysicalTip(PhysicalObj phy, bool sendToClient)
		{
			this.m_map.AddPhysical(phy);
			phy.SetGame(this);
			if (sendToClient)
			{
				this.SendAddPhysicalTip(phy);
			}
		}

		// Token: 0x060031A9 RID: 12713 RVA: 0x000BC4FC File Offset: 0x000BA6FC
		public override Player RemovePlayer(IGamePlayer gp, bool IsKick)
		{
			Player player = null;
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player2 in this.m_players.Values)
				{
					bool flag2 = player2.PlayerDetail == gp;
					if (flag2)
					{
						player = player2;
						this.m_players.Remove(player2.Id);
						break;
					}
				}
			}
			bool flag3 = player != null;
			if (flag3)
			{
				this.AddAction(new RemovePlayerAction(player));
			}
			return player;
		}

		// Token: 0x060031AA RID: 12714 RVA: 0x000BC5CC File Offset: 0x000BA7CC
		public void RemovePhysicalObj(PhysicalObj phy, bool sendToClient)
		{
			this.m_map.RemovePhysical(phy);
			phy.SetGame(null);
			if (sendToClient)
			{
				this.SendRemovePhysicalObj(phy);
			}
		}

		// Token: 0x060031AB RID: 12715 RVA: 0x000BC600 File Offset: 0x000BA800
		public void RemoveLiving(int id)
		{
			foreach (Living living in this.m_livings)
			{
				bool flag = living.Id == id;
				if (flag)
				{
					this.m_map.RemovePhysical(living);
					bool flag2 = living is TurnedLiving;
					if (flag2)
					{
						this.m_turnQueue.Remove(living as TurnedLiving);
					}
					else
					{
						this.m_livings.Remove(living);
					}
				}
			}
			this.SendRemoveLiving(id);
		}

		// Token: 0x060031AC RID: 12716 RVA: 0x00017FDE File Offset: 0x000161DE
		public void RemoveLivings(int id)
		{
			this.SendRemoveLiving(id);
		}

		// Token: 0x060031AD RID: 12717 RVA: 0x000BC6A8 File Offset: 0x000BA8A8
		public void RemoveLiving(Living living, bool sendToClient)
		{
			this.m_map.RemovePhysical(living);
			if (sendToClient)
			{
				this.SendRemoveLiving(living.Id);
			}
		}

		// Token: 0x060031AE RID: 12718 RVA: 0x000BC6D8 File Offset: 0x000BA8D8
		public List<Living> GetLivedLivings()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool isLiving = living.IsLiving;
				if (isLiving)
				{
					list.Add(living);
				}
			}
			return list;
		}

		// Token: 0x060031AF RID: 12719 RVA: 0x000BC74C File Offset: 0x000BA94C
		public List<Living> GetLivedLivingsHadTurn()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living.IsLiving && living is SimpleNpc && living.Config.HasTurn;
				if (flag)
				{
					list.Add(living);
				}
			}
			return list;
		}

		// Token: 0x060031B0 RID: 12720 RVA: 0x000BC7D8 File Offset: 0x000BA9D8
		public void ClearAllNpc()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc;
				if (flag)
				{
					list.Add(living);
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
				this.SendRemoveLiving(living2.Id);
			}
			List<Physics> allPhysicalSafe = this.m_map.GetAllPhysicalSafe();
			foreach (Physics physics in allPhysicalSafe)
			{
				bool flag2 = physics is SimpleNpc;
				if (flag2)
				{
					this.m_map.RemovePhysical(physics);
				}
			}
		}

		// Token: 0x060031B1 RID: 12721 RVA: 0x000BC910 File Offset: 0x000BAB10
		public void ClearDiedPhysicals()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = !living.IsLiving;
				if (flag)
				{
					list.Add(living);
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
			}
			List<Living> list2 = new List<Living>();
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag2 = !turnedLiving.IsLiving;
				if (flag2)
				{
					list2.Add(turnedLiving);
				}
			}
			foreach (Living living3 in list2)
			{
				TurnedLiving turnedLiving2 = (TurnedLiving)living3;
				this.m_turnQueue.Remove(turnedLiving2);
			}
			List<Physics> allPhysicalSafe = this.m_map.GetAllPhysicalSafe();
			foreach (Physics physics in allPhysicalSafe)
			{
				bool flag3 = !physics.IsLiving && !(physics is Player);
				if (flag3)
				{
					this.m_map.RemovePhysical(physics);
				}
			}
		}

		// Token: 0x060031B2 RID: 12722 RVA: 0x000BCB00 File Offset: 0x000BAD00
		public int CountPlayersTeam(int team)
		{
			int num = 0;
			Player[] allPlayers = this.GetAllPlayers();
			for (int i = 0; i < allPlayers.Length; i++)
			{
				bool flag = allPlayers[i].Team == team;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x060031B3 RID: 12723 RVA: 0x000BCB4C File Offset: 0x000BAD4C
		public bool IsAllComplete()
		{
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			foreach (Player player in allFightPlayers)
			{
				bool flag = player.LoadingProcess < 100;
				if (flag)
				{
					return false;
				}
			}
			return true;
		}

		// Token: 0x060031B4 RID: 12724 RVA: 0x000BCBBC File Offset: 0x000BADBC
		public bool IsSpecialPVE()
		{
			eRoomType roomType = base.RoomType;
			eRoomType eRoomType = roomType;
			return eRoomType == eRoomType.FightLib || eRoomType == eRoomType.Freshman;
		}

		// Token: 0x060031B5 RID: 12725 RVA: 0x000BCBF0 File Offset: 0x000BADF0
		public Player FindPlayer(int id)
		{
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				bool flag2 = this.m_players.ContainsKey(id);
				if (flag2)
				{
					return this.m_players[id];
				}
			}
			return null;
		}

		// Token: 0x060031B6 RID: 12726 RVA: 0x000BCC54 File Offset: 0x000BAE54
		public TurnedLiving FindNextTurnedLiving(bool isPvP)
		{
			bool flag = this.m_turnQueue.Count == 0;
			TurnedLiving turnedLiving;
			if (flag)
			{
				turnedLiving = null;
			}
			else
			{
				TurnedLiving turnedLiving2 = null;
				bool flag2 = this.TurnIndex == 1 && isPvP;
				if (flag2)
				{
					double num = -1.0;
					for (int i = 0; i < this.m_turnQueue.Count; i++)
					{
						bool flag3 = (!(this.m_turnQueue[i] is Player) || !(this.m_turnQueue[i] as Player).PlayerDetail.PlayerCharacter.isViewer) && this.m_turnQueue[i].IsLiving && this.m_turnQueue[i] is Player && (this.m_turnQueue[i] as Player).Agility > num;
						if (flag3)
						{
							turnedLiving2 = this.m_turnQueue[i];
							num = (this.m_turnQueue[i] as Player).Agility;
						}
					}
				}
				bool flag4 = turnedLiving2 == null;
				if (flag4)
				{
					int num2 = this.m_random.Next(this.m_turnQueue.Count - 1);
					turnedLiving2 = this.m_turnQueue[num2];
					int num3 = turnedLiving2.Delay;
					for (int j = 0; j < this.m_turnQueue.Count; j++)
					{
						bool flag5 = (!(this.m_turnQueue[j] is Player) || !(this.m_turnQueue[j] as Player).PlayerDetail.PlayerCharacter.isViewer) && (this.m_turnQueue[j].Config.HasTurn || this.m_turnQueue[j] is Player) && this.m_turnQueue[j].IsLiving && this.m_turnQueue[j].Delay < num3;
						if (flag5)
						{
							num3 = this.m_turnQueue[j].Delay;
							turnedLiving2 = this.m_turnQueue[j];
						}
					}
				}
				turnedLiving2.TurnNum++;
				turnedLiving = turnedLiving2;
			}
			return turnedLiving;
		}

		// Token: 0x060031B7 RID: 12727 RVA: 0x000BCEA8 File Offset: 0x000BB0A8
		public TurnedLiving[] GetNextAllTurnedLiving()
		{
			bool flag = this.m_turnQueue.Count == 0;
			TurnedLiving[] array;
			if (flag)
			{
				array = null;
			}
			else
			{
				List<TurnedLiving> list = new List<TurnedLiving>();
				for (int i = 0; i < this.m_turnQueue.Count; i++)
				{
					bool flag2 = this.m_turnQueue[i].IsLiving && !this.m_turnQueue[i].IsFrost && !this.m_turnQueue[i].IsAttacking && this.m_turnQueue[i] is Player;
					if (flag2)
					{
						this.m_turnQueue[i].TurnNum++;
						list.Add(this.m_turnQueue[i]);
					}
				}
				array = list.ToArray();
			}
			return array;
		}

		// Token: 0x060031B8 RID: 12728 RVA: 0x000BCF88 File Offset: 0x000BB188
		public virtual void MinusDelays(int lowestDelay)
		{
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				turnedLiving.Delay -= lowestDelay;
			}
		}

		// Token: 0x060031B9 RID: 12729 RVA: 0x000BCFE8 File Offset: 0x000BB1E8
		public SimpleNpc FindNpc(int npcId)
		{
			SimpleNpc simpleNpc = null;
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc && (living as SimpleNpc).NpcInfo.ID == npcId && living.IsLiving;
				if (flag)
				{
					simpleNpc = living as SimpleNpc;
					return simpleNpc;
				}
			}
			return simpleNpc;
		}

		// Token: 0x060031BA RID: 12730 RVA: 0x000BD078 File Offset: 0x000BB278
		public SimpleNpc FindRandomNpc()
		{
			SimpleNpc[] array = this.FindAllNpcLiving();
			int num = this.Random.Next(0, array.Length - 1);
			return array[num];
		}

		// Token: 0x060031BB RID: 12731 RVA: 0x000BD0A8 File Offset: 0x000BB2A8
		public SimpleBoss[] FindAllBoss()
		{
			List<SimpleBoss> list = new List<SimpleBoss>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleBoss;
				if (flag)
				{
					list.Add(living as SimpleBoss);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060031BC RID: 12732 RVA: 0x000BD128 File Offset: 0x000BB328
		public SimpleBoss FindBoss(int bossID)
		{
			SimpleBoss simpleBoss = null;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && (turnedLiving as SimpleBoss).NpcInfo.ID == bossID;
				if (flag)
				{
					simpleBoss = turnedLiving as SimpleBoss;
					return simpleBoss;
				}
			}
			return simpleBoss;
		}

		// Token: 0x060031BD RID: 12733 RVA: 0x000BD1B4 File Offset: 0x000BB3B4
		public SimpleBoss FindBossWithID(int id)
		{
			SimpleBoss simpleBoss = null;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && turnedLiving.IsLiving && (turnedLiving as SimpleBoss).NpcInfo.ID == id;
				if (flag)
				{
					simpleBoss = turnedLiving as SimpleBoss;
				}
			}
			return simpleBoss;
		}

		// Token: 0x060031BE RID: 12734 RVA: 0x000BD244 File Offset: 0x000BB444
		public List<Living> FindAllTurnBossLiving()
		{
			List<Living> list = new List<Living>();
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && turnedLiving.IsLiving;
				if (flag)
				{
					list.Add(turnedLiving);
				}
			}
			return list;
		}

		// Token: 0x060031BF RID: 12735 RVA: 0x000BD2C4 File Offset: 0x000BB4C4
		public List<SimpleBoss> FindAllTurnBoss()
		{
			List<SimpleBoss> list = new List<SimpleBoss>();
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss;
				if (flag)
				{
					list.Add(turnedLiving as SimpleBoss);
				}
			}
			return list;
		}

		// Token: 0x060031C0 RID: 12736 RVA: 0x000BD340 File Offset: 0x000BB540
		public SimpleBoss[] FindLivingTurnBossWithID(int id)
		{
			List<SimpleBoss> list = new List<SimpleBoss>();
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && turnedLiving.IsLiving && (turnedLiving as SimpleBoss).NpcInfo.ID == id;
				if (flag)
				{
					list.Add(turnedLiving as SimpleBoss);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060031C1 RID: 12737 RVA: 0x000BD3DC File Offset: 0x000BB5DC
		public SimpleBoss FindSingleSimpleBossID(int id)
		{
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving is SimpleBoss && (turnedLiving as SimpleBoss).NpcInfo.ID == id;
				if (flag)
				{
					return turnedLiving as SimpleBoss;
				}
			}
			return null;
		}

		// Token: 0x060031C2 RID: 12738 RVA: 0x000BD460 File Offset: 0x000BB660
		public SimpleNpc[] FindAllNpc()
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc;
				if (flag)
				{
					list.Add(living as SimpleNpc);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060031C3 RID: 12739 RVA: 0x000BD4E0 File Offset: 0x000BB6E0
		public SimpleNpc[] FindAllNpcLiving()
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc && living.IsLiving;
				if (flag)
				{
					list.Add(living as SimpleNpc);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060031C4 RID: 12740 RVA: 0x000BD568 File Offset: 0x000BB768
		public SimpleNpc[] GetNPCLivingWithID(int id)
		{
			List<SimpleNpc> list = new List<SimpleNpc>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living is SimpleNpc && living.IsLiving && (living as SimpleNpc).NpcInfo.ID == id;
				if (flag)
				{
					list.Add(living as SimpleNpc);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060031C5 RID: 12741 RVA: 0x000BD604 File Offset: 0x000BB804
		public List<Living> GetLivedNpcs(int npcId)
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living.IsLiving && living is SimpleNpc && (living as SimpleNpc).NpcInfo.ID == npcId;
				if (flag)
				{
					list.Add(living);
				}
			}
			return list;
		}

		// Token: 0x060031C6 RID: 12742 RVA: 0x000BD698 File Offset: 0x000BB898
		public int GetHighDelayTurn()
		{
			new List<Living>();
			int num = int.MinValue;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving != null && turnedLiving.Delay > num;
				if (flag)
				{
					num = turnedLiving.Delay;
				}
			}
			return num;
		}

		// Token: 0x060031C7 RID: 12743 RVA: 0x000BD718 File Offset: 0x000BB918
		public int GetLowDelayTurn()
		{
			new List<Living>();
			int num = int.MaxValue;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving != null && turnedLiving.Delay < num;
				if (flag)
				{
					num = turnedLiving.Delay;
				}
			}
			return num;
		}

		// Token: 0x060031C8 RID: 12744 RVA: 0x000BD798 File Offset: 0x000BB998
		public void ClearAllChild()
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living.IsLiving && living is SimpleNpc;
				if (flag)
				{
					list.Add(living);
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
				this.RemoveLiving(living2.Id);
			}
		}

		// Token: 0x060031C9 RID: 12745 RVA: 0x000BD874 File Offset: 0x000BBA74
		public void ClearAllChildByID(int ID)
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				SimpleNpc simpleNpc = (SimpleNpc)living;
				bool flag = simpleNpc.IsLiving && simpleNpc != null && simpleNpc.NpcInfo.ID == ID;
				if (flag)
				{
					list.Add(simpleNpc);
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
				this.RemoveLiving(living2.Id);
			}
		}

		// Token: 0x060031CA RID: 12746 RVA: 0x000BD95C File Offset: 0x000BBB5C
		public void ClearAllChildByIDs(int[] ID)
		{
			List<Living> list = new List<Living>();
			for (int i = 0; i < ID.Length; i++)
			{
				foreach (Living living in this.m_livings)
				{
					SimpleNpc simpleNpc = (SimpleNpc)living;
					bool flag = simpleNpc.IsLiving && simpleNpc != null && simpleNpc.NpcInfo.ID == ID[i];
					if (flag)
					{
						list.Add(simpleNpc);
					}
				}
			}
			foreach (Living living2 in list)
			{
				this.m_livings.Remove(living2);
				living2.Dispose();
				this.RemoveLiving(living2.Id);
			}
		}

		// Token: 0x060031CB RID: 12747 RVA: 0x000BDA5C File Offset: 0x000BBC5C
		public List<Player> GetAllLivingPlayersByProperties(int prop)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.IsLiving && (int)player.Properties1 == 2;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060031CC RID: 12748 RVA: 0x000BDB1C File Offset: 0x000BBD1C
		public float GetNextWind()
		{
			bool frozenWind = this.FrozenWind;
			float num;
			if (frozenWind)
			{
				num = 0f;
			}
			else
			{
				int num2 = (int)(this.Wind * 10f);
				bool flag = num2 > this.m_nextWind;
				int num3;
				if (flag)
				{
					num3 = num2 - this.m_random.Next(11);
					bool flag2 = num2 <= this.m_nextWind;
					if (flag2)
					{
						this.m_nextWind = this.m_random.Next(-40, 40);
					}
				}
				else
				{
					num3 = num2 + this.m_random.Next(11);
					bool flag3 = num2 >= this.m_nextWind;
					if (flag3)
					{
						this.m_nextWind = this.m_random.Next(-40, 40);
					}
				}
				num = (float)num3 / 10f;
			}
			return num;
		}

		// Token: 0x060031CD RID: 12749 RVA: 0x000BDBE4 File Offset: 0x000BBDE4
		public void UpdateWind(float wind, bool sendToClient)
		{
			bool flag = this.m_map.wind != wind;
			if (flag)
			{
				this.m_map.wind = wind;
				if (sendToClient)
				{
					this.SendGameUpdateWind(wind);
				}
			}
		}

		// Token: 0x060031CE RID: 12750 RVA: 0x000BDC28 File Offset: 0x000BBE28
		public int GetDiedPlayerCount()
		{
			int num = 0;
			foreach (Player player in this.m_players.Values)
			{
				bool flag = player.IsActive && !player.IsLiving;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x060031CF RID: 12751 RVA: 0x000BDCA8 File Offset: 0x000BBEA8
		public int GetDiedNPCCount()
		{
			int num = 0;
			SimpleNpc[] array = this.FindAllNpc();
			foreach (SimpleNpc simpleNpc in array)
			{
				bool flag = !simpleNpc.IsLiving;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x060031D0 RID: 12752 RVA: 0x000BDCF4 File Offset: 0x000BBEF4
		public int GetDiedBossCount()
		{
			int num = 0;
			SimpleBoss[] array = this.FindAllBoss();
			foreach (SimpleBoss simpleBoss in array)
			{
				bool flag = !simpleBoss.IsLiving;
				if (flag)
				{
					num++;
				}
			}
			return num;
		}

		// Token: 0x060031D1 RID: 12753 RVA: 0x000BDD40 File Offset: 0x000BBF40
		public int GetDiedCount()
		{
			return this.GetDiedNPCCount() + this.GetDiedBossCount();
		}

		// Token: 0x060031D2 RID: 12754 RVA: 0x000BDD60 File Offset: 0x000BBF60
		public Point GetPlayerPoint(int team)
		{
			MapPoint pvemapRandomPos = MapMgr.GetPVEMapRandomPos(this.m_map.Info.ID);
			return this.GetPlayerPoint(pvemapRandomPos, team);
		}

		// Token: 0x060031D3 RID: 12755 RVA: 0x000BDD90 File Offset: 0x000BBF90
		protected Point GetPlayerPoint(MapPoint mapPos, int team)
		{
			List<Point> list = mapPos.PosX;
			switch (team)
			{
			case 2:
			case 4:
			case 6:
				list = mapPos.PosX1;
				break;
			}
			int num = this.m_random.Next(list.Count);
			Point point = list[num];
			list.Remove(point);
			return point;
		}

		// Token: 0x060031D4 RID: 12756 RVA: 0x00004D85 File Offset: 0x00002F85
		public virtual void CheckState(int delay)
		{
		}

		// Token: 0x060031D5 RID: 12757 RVA: 0x000BDDFC File Offset: 0x000BBFFC
		public override void ProcessData(GSPacketIn packet)
		{
			bool flag = this.m_players.ContainsKey(packet.Parameter1);
			if (flag)
			{
				Player player = this.m_players[packet.Parameter1];
				this.AddAction(new ProcessPacketAction(player, packet));
			}
		}

		// Token: 0x060031D6 RID: 12758 RVA: 0x000BDE44 File Offset: 0x000BC044
		public Player FindPlayerWithId(int id)
		{
			Dictionary<int, Player> players = this.m_players;
			Player player2;
			lock (players)
			{
				bool flag2 = this.m_players.Count > 0;
				if (flag2)
				{
					foreach (Player player in this.m_players.Values)
					{
						bool flag3 = player.IsLiving && player.Id == id;
						if (flag3)
						{
							return player;
						}
					}
				}
				player2 = null;
			}
			return player2;
		}

		// Token: 0x060031D7 RID: 12759 RVA: 0x000BDF04 File Offset: 0x000BC104
		public List<Player> FindRangePlayers(int minX, int maxX)
		{
			Dictionary<int, Player> players = this.m_players;
			List<Player> list2;
			lock (players)
			{
				List<Player> list = new List<Player>();
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.IsLiving && player.X >= minX && player.X <= maxX;
					if (flag2)
					{
						list.Add(player);
					}
				}
				list2 = list;
			}
			return list2;
		}

		// Token: 0x060031D8 RID: 12760 RVA: 0x000BDFC8 File Offset: 0x000BC1C8
		public string ListPlayersName()
		{
			List<string> list = new List<string>();
			List<Player> allLivingPlayers = this.GetAllLivingPlayers();
			foreach (Player player in allLivingPlayers)
			{
				list.Add(player.PlayerDetail.PlayerCharacter.NickName);
			}
			return string.Join(",", list);
		}

		// Token: 0x060031D9 RID: 12761 RVA: 0x000BE04C File Offset: 0x000BC24C
		public List<Player> GetAllEnemyPlayers(Living living)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			List<Player> list2;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.Team != living.Team;
					if (flag2)
					{
						list.Add(player);
					}
				}
				list2 = list;
			}
			return list2;
		}

		// Token: 0x060031DA RID: 12762 RVA: 0x000BE100 File Offset: 0x000BC300
		public Player FindNearestPlayer(int x, int y)
		{
			double num = double.MaxValue;
			Player player = null;
			foreach (Player player2 in this.m_players.Values)
			{
				bool isLiving = player2.IsLiving;
				if (isLiving)
				{
					double num2 = player2.Distance(x, y);
					bool flag = num2 < num;
					if (flag)
					{
						num = num2;
						player = player2;
					}
				}
			}
			return player;
		}

		// Token: 0x060031DB RID: 12763 RVA: 0x000BE194 File Offset: 0x000BC394
		public Player FindFarPlayer(int x, int y)
		{
			Dictionary<int, Player> players = this.m_players;
			Player player3;
			lock (players)
			{
				double num = double.MinValue;
				Player player = null;
				foreach (Player player2 in this.m_players.Values)
				{
					bool isLiving = player2.IsLiving;
					if (isLiving)
					{
						double num2 = player2.Distance(x, y);
						bool flag2 = num2 > num;
						if (flag2)
						{
							num = num2;
							player = player2;
						}
					}
				}
				player3 = player;
			}
			return player3;
		}

		// Token: 0x060031DC RID: 12764 RVA: 0x000BE258 File Offset: 0x000BC458
		public int FindBombPlayerX(int blowArea)
		{
			Dictionary<int, int> dictionary = new Dictionary<int, int>();
			Dictionary<int, int> dictionary2 = new Dictionary<int, int>();
			List<int> list = new List<int>();
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			int num = 0;
			foreach (Player player in allFightPlayers)
			{
				bool flag = !player.IsLiving;
				if (!flag)
				{
					for (int i = 0; i < 10; i++)
					{
						int num2;
						do
						{
							num2 = this.Random.Next(player.X - blowArea, player.X + blowArea);
						}
						while (dictionary.ContainsKey(num2));
						dictionary.Add(num2, 0);
					}
				}
			}
			foreach (int num3 in dictionary.Keys)
			{
				foreach (Player player2 in allFightPlayers)
				{
					bool flag2 = player2.X > num3 - blowArea && player2.X < num3 + blowArea;
					if (flag2)
					{
						bool flag3 = dictionary2.ContainsKey(num3);
						if (flag3)
						{
							Dictionary<int, int> dictionary3 = dictionary2;
							int num4 = num3;
							int num5 = dictionary3[num4];
							dictionary3[num4] = num5 + 1;
						}
						else
						{
							dictionary2.Add(num3, 1);
						}
					}
				}
			}
			foreach (int num6 in dictionary2.Values)
			{
				bool flag4 = num6 > num;
				if (flag4)
				{
					num = num6;
				}
			}
			foreach (int num7 in dictionary2.Keys)
			{
				bool flag5 = dictionary2[num7] == num;
				if (flag5)
				{
					list.Add(num7);
				}
			}
			int num8 = this.Random.Next(0, list.Count);
			return list[num8];
		}

		// Token: 0x060031DD RID: 12765 RVA: 0x000BE4DC File Offset: 0x000BC6DC
		public Living FindNearestHelper(int x, int y)
		{
			double num = double.MaxValue;
			Living living = null;
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag = turnedLiving.IsLiving && (turnedLiving is Player || turnedLiving.Config.IsHelper);
				if (flag)
				{
					double num2 = turnedLiving.Distance(x, y);
					bool flag2 = num2 < num;
					if (flag2)
					{
						num = num2;
						living = turnedLiving;
					}
				}
			}
			return living;
		}

		// Token: 0x060031DE RID: 12766 RVA: 0x000BE588 File Offset: 0x000BC788
		public SimpleNpc FindHealthyHelper()
		{
			SimpleNpc simpleNpc = null;
			foreach (Living living in this.m_livings)
			{
				SimpleNpc simpleNpc2 = (SimpleNpc)living;
				bool flag = simpleNpc2.Config.IsHelper && !simpleNpc2.Config.CanHeal;
				if (flag)
				{
					simpleNpc = simpleNpc2;
				}
			}
			return simpleNpc;
		}

		// Token: 0x060031DF RID: 12767 RVA: 0x000BE610 File Offset: 0x000BC810
		public SimpleNpc FindNearestAdverseNpc(int x, int y, int camp)
		{
			double num = double.MaxValue;
			SimpleNpc simpleNpc = null;
			foreach (Living living in this.m_livings)
			{
				SimpleNpc simpleNpc2 = (SimpleNpc)living;
				bool flag = simpleNpc2.IsLiving && simpleNpc2.NpcInfo.Camp != camp;
				if (flag)
				{
					double num2 = simpleNpc2.Distance(x, y);
					bool flag2 = num2 < num;
					if (flag2)
					{
						num = num2;
						simpleNpc = simpleNpc2;
					}
				}
			}
			foreach (Living living2 in this.m_decklivings)
			{
				SimpleNpc simpleNpc3 = (SimpleNpc)living2;
				bool flag3 = simpleNpc3.IsLiving && simpleNpc3.NpcInfo.Camp != camp;
				if (flag3)
				{
					double num3 = simpleNpc3.Distance(x, y);
					bool flag4 = num3 < num;
					if (flag4)
					{
						num = num3;
						simpleNpc = simpleNpc3;
					}
				}
			}
			return simpleNpc;
		}

		// Token: 0x060031E0 RID: 12768 RVA: 0x000BE750 File Offset: 0x000BC950
		public Player FindRandomPlayer(Living living)
		{
			List<Player> list = new List<Player>();
			Player player = null;
			string text = "这里一个组合";
			List<Player> list2 = list;
			Console.WriteLine(text + ((list2 != null) ? list2.ToString() : null));
			foreach (Player player2 in this.m_players.Values)
			{
				Console.WriteLine("这里进入foreach");
				bool flag = player2.IsLiving && player2.Team == living.Team;
				if (flag)
				{
					Console.WriteLine("这里进入if");
					Console.WriteLine("value.IsLiving：" + player2.IsLiving.ToString());
					Console.WriteLine("value.Team：" + player2.Team.ToString());
					Console.WriteLine("living.Team：" + living.Team.ToString());
					list.Add(player2);
				}
			}
			bool flag2 = list.Count > 0;
			if (flag2)
			{
				Console.WriteLine("这里进入if 》 0");
				int num = this.Random.Next(0, list.Count);
				Console.WriteLine("index概率取0-Count" + list.Count.ToString());
				player = list[num];
				string text2 = "result";
				Player player3 = player;
				Console.WriteLine(text2 + ((player3 != null) ? player3.ToString() : null));
			}
			return player;
		}

		// Token: 0x060031E1 RID: 12769 RVA: 0x000BE8EC File Offset: 0x000BCAEC
		public void UpdateBlood(Living living, int type, int value, int embleType = 0)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id)
			{
				Parameter1 = living.Id
			};
			gspacketIn.WriteByte(11);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteLong((long)living.Blood);
			gspacketIn.WriteInt(value);
			gspacketIn.WriteInt(embleType);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x060031E2 RID: 12770 RVA: 0x000BE950 File Offset: 0x000BCB50
		public void UpdateWind2(float wind, bool sendToClient)
		{
			this.m_map.wind = wind;
			if (sendToClient)
			{
				this.SendGameUpdateWind(wind);
			}
		}

		// Token: 0x060031E3 RID: 12771 RVA: 0x000BE97C File Offset: 0x000BCB7C
		internal void UpdateEnergy(Player player, int energycost)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(191);
			gspacketIn.WriteInt(energycost);
			player.PlayerDetail.SendTCP(gspacketIn);
		}

		// Token: 0x060031E4 RID: 12772 RVA: 0x000BE9BC File Offset: 0x000BCBBC
		public void removePhysicObject(Player living_0)
		{
			foreach (int num in living_0.ListObject)
			{
				GSPacketIn gspacketIn = new GSPacketIn(91, living_0.Id)
				{
					Parameter1 = living_0.Id
				};
				gspacketIn.WriteByte(53);
				gspacketIn.WriteInt(num);
				this.SendToAll(gspacketIn);
			}
			living_0.ListObject = new List<int>();
		}

		// Token: 0x060031E5 RID: 12773 RVA: 0x000BEA4C File Offset: 0x000BCC4C
		public List<Player> GetAllPlayersSameTeam(Living living)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.Team == living.Team;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060031E6 RID: 12774 RVA: 0x000BEB00 File Offset: 0x000BCD00
		public Player FindRandomPlayer()
		{
			List<Player> list = new List<Player>();
			Player player = null;
			foreach (Player player2 in this.m_players.Values)
			{
				bool flag = player2.IsLiving && player2.IsActive;
				if (flag)
				{
					list.Add(player2);
				}
			}
			bool flag2 = list.Count > 0;
			if (flag2)
			{
				int num = this.Random.Next(0, list.Count);
				player = list[num];
			}
			return player;
		}

		// Token: 0x060031E7 RID: 12775 RVA: 0x000BEBB4 File Offset: 0x000BCDB4
		public Player[] FindRandomPlayer(int max)
		{
			List<Player> list = new List<Player>();
			bool flag = this.m_players.Count > 0;
			if (flag)
			{
				List<Player> list2 = new List<Player>();
				foreach (Player player in this.m_players.Values)
				{
					bool isLiving = player.IsLiving;
					if (isLiving)
					{
						list2.Add(player);
					}
				}
				for (int i = 0; i < max; i++)
				{
					int num = this.Random.Next(0, list2.Count);
					list.Add(list2[num]);
					list2.RemoveAt(num);
					bool flag2 = list2.Count <= 0;
					if (flag2)
					{
						break;
					}
				}
			}
			return list.ToArray();
		}

		// Token: 0x060031E8 RID: 12776 RVA: 0x000BECAC File Offset: 0x000BCEAC
		public int FindlivingbyDir(Living npc)
		{
			int num = 0;
			int num2 = 0;
			foreach (Player player in this.m_players.Values)
			{
				bool isLiving = player.IsLiving;
				if (isLiving)
				{
					bool flag = player.X > npc.X;
					if (flag)
					{
						num2++;
					}
					else
					{
						num++;
					}
				}
			}
			bool flag2 = num2 > num;
			int num3;
			if (flag2)
			{
				num3 = 1;
			}
			else
			{
				bool flag3 = num2 < num;
				if (flag3)
				{
					num3 = -1;
				}
				else
				{
					num3 = -npc.Direction;
				}
			}
			return num3;
		}

		// Token: 0x060031E9 RID: 12777 RVA: 0x000BED64 File Offset: 0x000BCF64
		public PhysicalObj[] FindPhysicalObjByName(string name)
		{
			List<PhysicalObj> list = new List<PhysicalObj>();
			foreach (PhysicalObj physicalObj in this.m_map.GetAllPhysicalObjSafe())
			{
				bool flag = physicalObj.Name == name;
				if (flag)
				{
					list.Add(physicalObj);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060031EA RID: 12778 RVA: 0x000BEDE8 File Offset: 0x000BCFE8
		public PhysicalObj[] FindPhysicalObjByName(string name, bool CanPenetrate)
		{
			List<PhysicalObj> list = new List<PhysicalObj>();
			foreach (PhysicalObj physicalObj in this.m_map.GetAllPhysicalObjSafe())
			{
				bool flag = physicalObj.Name == name && physicalObj.CanPenetrate == CanPenetrate;
				if (flag)
				{
					list.Add(physicalObj);
				}
			}
			return list.ToArray();
		}

		// Token: 0x060031EB RID: 12779 RVA: 0x000BEE78 File Offset: 0x000BD078
		public Player GetFrostPlayerRadom()
		{
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			List<Player> list = new List<Player>();
			foreach (Player player in allFightPlayers)
			{
				bool isFrost = player.IsFrost;
				if (isFrost)
				{
					list.Add(player);
				}
			}
			bool flag = list.Count > 0;
			Player player2;
			if (flag)
			{
				int num = this.Random.Next(0, list.Count);
				player2 = list.ElementAt(num);
			}
			else
			{
				player2 = null;
			}
			return player2;
		}

		// Token: 0x060031EC RID: 12780 RVA: 0x000BEA4C File Offset: 0x000BCC4C
		public List<Player> GetAllTeamPlayers(Living living)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.Team == living.Team;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060031ED RID: 12781 RVA: 0x000BEF20 File Offset: 0x000BD120
		public List<Living> FindAppointDeGreeNpc(int degree)
		{
			List<Living> list = new List<Living>();
			foreach (Living living in this.m_livings)
			{
				bool flag = living.IsLiving && living.Degree == degree;
				if (flag)
				{
					list.Add(living);
				}
			}
			foreach (Living living2 in this.m_decklivings)
			{
				bool flag2 = living2.IsLiving && living2.Degree == degree;
				if (flag2)
				{
					list.Add(living2);
				}
			}
			foreach (TurnedLiving turnedLiving in this.m_turnQueue)
			{
				bool flag3 = turnedLiving.IsLiving && turnedLiving.Degree == degree;
				if (flag3)
				{
					list.Add(turnedLiving);
				}
			}
			return list;
		}

		// Token: 0x060031EE RID: 12782 RVA: 0x00042B50 File Offset: 0x00040D50
		public virtual bool TakeCard(Player player, bool isSysTake)
		{
			return false;
		}

		// Token: 0x060031EF RID: 12783 RVA: 0x00042B50 File Offset: 0x00040D50
		public virtual bool TakeCard(Player player, int index, bool isSysTake)
		{
			return false;
		}

		// Token: 0x060031F0 RID: 12784 RVA: 0x00017FE9 File Offset: 0x000161E9
		public override void Pause(int time)
		{
			this.m_passTick = Math.Max(this.m_passTick, TickHelper.GetTickCount() + (long)time);
		}

		// Token: 0x060031F1 RID: 12785 RVA: 0x00018005 File Offset: 0x00016205
		public override void Resume()
		{
			this.m_passTick = 0L;
		}

		// Token: 0x060031F2 RID: 12786 RVA: 0x000BF070 File Offset: 0x000BD270
		public void AddAction(IAction action)
		{
			ArrayList actions = this.m_actions;
			lock (actions)
			{
				this.m_actions.Add(action);
			}
		}

		// Token: 0x060031F3 RID: 12787 RVA: 0x000BF0BC File Offset: 0x000BD2BC
		public void AddAction(ArrayList actions)
		{
			ArrayList actions2 = this.m_actions;
			lock (actions2)
			{
				this.m_actions.AddRange(actions);
			}
		}

		// Token: 0x060031F4 RID: 12788 RVA: 0x00018010 File Offset: 0x00016210
		public void ClearWaitTimer()
		{
			this.m_waitTimer = 0L;
		}

		// Token: 0x060031F5 RID: 12789 RVA: 0x0001801B File Offset: 0x0001621B
		public void WaitTime(int delay)
		{
			this.m_waitTimer = Math.Max(this.m_waitTimer, TickHelper.GetTickCount() + (long)delay);
			this.m_lastWaitTimer = this.m_waitTimer;
		}

		// Token: 0x060031F6 RID: 12790 RVA: 0x000BF108 File Offset: 0x000BD308
		public long GetWaitTimer()
		{
			return this.m_waitTimer;
		}

		// Token: 0x060031F7 RID: 12791 RVA: 0x000BF120 File Offset: 0x000BD320
		public int GetWaitTimerLeft()
		{
			bool flag = this.m_lastWaitTimer <= 0L;
			int num;
			if (flag)
			{
				num = 0;
			}
			else
			{
				long num2 = ((TickHelper.GetTickCount() > this.m_lastWaitTimer) ? (TickHelper.GetTickCount() - this.m_lastWaitTimer) : (this.m_lastWaitTimer - TickHelper.GetTickCount()));
				bool flag2 = num2 > 10000L;
				if (flag2)
				{
					num = 1000;
				}
				else
				{
					num = (int)num2;
				}
			}
			return num;
		}

		// Token: 0x060031F8 RID: 12792 RVA: 0x000BF188 File Offset: 0x000BD388
		public void Update(long tick)
		{
			bool flag = this.m_passTick >= tick;
			if (!flag)
			{
				this.m_lifeTime++;
				ArrayList actions = this.m_actions;
				ArrayList arrayList;
				lock (actions)
				{
					arrayList = (ArrayList)this.m_actions.Clone();
					this.m_actions.Clear();
				}
				bool flag3 = arrayList == null || this.GameState == eGameState.Stopped;
				if (!flag3)
				{
					this.CurrentActionCount = arrayList.Count;
					bool flag4 = arrayList.Count > 0;
					if (flag4)
					{
						ArrayList arrayList2 = new ArrayList();
						foreach (object obj in arrayList)
						{
							IAction action = (IAction)obj;
							try
							{
								action.Execute(this, tick);
								bool flag5 = !action.IsFinished(this, tick);
								if (flag5)
								{
									arrayList2.Add(action);
								}
							}
							catch (Exception ex)
							{
								BaseGame.log.Error("Map update error:", ex);
							}
						}
						this.AddAction(arrayList2);
					}
					else
					{
						bool flag6 = this.m_waitTimer < tick;
						if (flag6)
						{
							this.CheckState(0);
						}
					}
				}
			}
		}

		// Token: 0x060031F9 RID: 12793 RVA: 0x000BF308 File Offset: 0x000BD508
		public List<Player> GetAllTarget(Living living)
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = player.IsLiving && player.Id != living.Id;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060031FA RID: 12794 RVA: 0x000BF3C8 File Offset: 0x000BD5C8
		public List<Player> GetAllFightPlayers()
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				list.AddRange(this.m_players.Values);
			}
			return list;
		}

		// Token: 0x060031FB RID: 12795 RVA: 0x000BF424 File Offset: 0x000BD624
		public List<Player> GetAllFightingPlayers()
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool flag2 = !player.PlayerDetail.PlayerCharacter.isViewer;
					if (flag2)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060031FC RID: 12796 RVA: 0x000BF4DC File Offset: 0x000BD6DC
		public List<Player> GetAllLivingPlayers()
		{
			List<Player> list = new List<Player>();
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player in this.m_players.Values)
				{
					bool isLiving = player.IsLiving;
					if (isLiving)
					{
						list.Add(player);
					}
				}
			}
			return list;
		}

		// Token: 0x060031FD RID: 12797 RVA: 0x000BF588 File Offset: 0x000BD788
		public List<Player> GetAllLivingPlayerOnRange(int minX, int maxX)
		{
			List<Player> list = new List<Player>();
			foreach (Player player in this.GetAllLivingPlayers())
			{
				bool flag = player.X >= minX && player.X <= maxX;
				if (flag)
				{
					list.Add(player);
				}
			}
			return list;
		}

		// Token: 0x060031FE RID: 12798 RVA: 0x000BF60C File Offset: 0x000BD80C
		public bool GetSameTeam()
		{
			bool flag = false;
			Player[] allPlayers = this.GetAllPlayers();
			Player[] array = allPlayers;
			foreach (Player player in array)
			{
				bool flag2 = player.Team == allPlayers[0].Team;
				if (!flag2)
				{
					flag = false;
					break;
				}
				flag = true;
			}
			return flag;
		}

		// Token: 0x060031FF RID: 12799 RVA: 0x000BF668 File Offset: 0x000BD868
		public Player[] GetAllPlayers()
		{
			return this.GetAllFightPlayers().ToArray();
		}

		// Token: 0x06003200 RID: 12800 RVA: 0x000BF688 File Offset: 0x000BD888
		public Player GetPlayer(IGamePlayer gp)
		{
			Player player = null;
			Dictionary<int, Player> players = this.m_players;
			lock (players)
			{
				foreach (Player player2 in this.m_players.Values)
				{
					bool flag2 = player2.PlayerDetail == gp;
					if (flag2)
					{
						player = player2;
						break;
					}
				}
			}
			return player;
		}

		// Token: 0x06003201 RID: 12801 RVA: 0x000BF72C File Offset: 0x000BD92C
		public int GetPlayerCount()
		{
			return this.GetAllFightPlayers().Count;
		}

		// Token: 0x06003202 RID: 12802 RVA: 0x00018043 File Offset: 0x00016243
		public virtual void SendToAll(GSPacketIn pkg)
		{
			this.SendToAll(pkg, null);
		}

		// Token: 0x06003203 RID: 12803 RVA: 0x000BF74C File Offset: 0x000BD94C
		public virtual void SendToAll(GSPacketIn pkg, IGamePlayer except)
		{
			bool flag = pkg.Parameter2 == 0;
			if (flag)
			{
				pkg.Parameter2 = this.LifeTime;
			}
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			foreach (Player player in allFightPlayers)
			{
				bool flag2 = player.IsActive && player.PlayerDetail != except;
				if (flag2)
				{
					player.PlayerDetail.SendTCP(pkg);
				}
			}
		}

		// Token: 0x06003204 RID: 12804 RVA: 0x0001804F File Offset: 0x0001624F
		public virtual void SendToTeam(GSPacketIn pkg, int team)
		{
			this.SendToTeam(pkg, team, null);
		}

		// Token: 0x06003205 RID: 12805 RVA: 0x000BF7E8 File Offset: 0x000BD9E8
		public virtual void SendToTeam(GSPacketIn pkg, int team, IGamePlayer except)
		{
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			foreach (Player player in allFightPlayers)
			{
				bool flag = player.IsActive && player.PlayerDetail != except && player.Team == team;
				if (flag)
				{
					player.PlayerDetail.SendTCP(pkg);
				}
			}
		}

		// Token: 0x06003206 RID: 12806 RVA: 0x000BF86C File Offset: 0x000BDA6C
		public int getTurnTime()
		{
			int timeType = this.m_timeType;
			if (!true)
			{
			}
			int num;
			switch (timeType)
			{
			case 1:
				num = 8;
				break;
			case 2:
				num = 10;
				break;
			case 3:
				num = 12;
				break;
			case 4:
				num = 16;
				break;
			case 5:
				num = 21;
				break;
			case 6:
				num = 31;
				break;
			default:
				num = -1;
				break;
			}
			if (!true)
			{
			}
			return num;
		}

		// Token: 0x06003207 RID: 12807 RVA: 0x0001805C File Offset: 0x0001625C
		public void AddTempPoint(int x, int y)
		{
			this.m_tempPoints.Add(new Point(x, y));
		}

		// Token: 0x06003208 RID: 12808 RVA: 0x00018072 File Offset: 0x00016272
		public void AddTempGhostPoint(int x, int y)
		{
			this.m_tempGhostPoints.Add(new Point(x, y));
		}

		// Token: 0x06003209 RID: 12809 RVA: 0x000BF8D4 File Offset: 0x000BDAD4
		public SimpleBox AddBox(ItemInfo item, Point pos, bool sendToClient)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			SimpleBox simpleBox = new SimpleBox(physicalId, "1", item, 1);
			simpleBox.SetXY(pos);
			this.AddPhysicalObj(simpleBox, sendToClient);
			return this.AddBox(simpleBox, sendToClient);
		}

		// Token: 0x0600320A RID: 12810 RVA: 0x000BF920 File Offset: 0x000BDB20
		public SimpleBox AddBox(SimpleBox box, bool sendToClient)
		{
			this.m_tempBox.Add(box);
			this.AddPhysicalObj(box, sendToClient);
			return box;
		}

		// Token: 0x0600320B RID: 12811 RVA: 0x000BF94C File Offset: 0x000BDB4C
		public SimpleBox AddGhostBox(Point pos, int type)
		{
			int physicalId = this.PhysicalId;
			this.PhysicalId = physicalId + 1;
			SimpleBox simpleBox = new SimpleBox(physicalId, "1", null, type);
			simpleBox.SetXY(pos);
			return this.AddGhostBox(simpleBox);
		}

		// Token: 0x0600320C RID: 12812 RVA: 0x000BF98C File Offset: 0x000BDB8C
		public SimpleBox AddGhostBox(SimpleBox box)
		{
			this.m_tempBox.Add(box);
			this.AddGhostBoxObj(box);
			return box;
		}

		// Token: 0x0600320D RID: 12813 RVA: 0x000BF9B4 File Offset: 0x000BDBB4
		public void CheckBox()
		{
			List<SimpleBox> list = new List<SimpleBox>();
			foreach (SimpleBox simpleBox in this.m_tempBox)
			{
				bool flag = !simpleBox.IsLiving;
				if (flag)
				{
					list.Add(simpleBox);
				}
			}
			foreach (SimpleBox simpleBox2 in list)
			{
				this.m_tempBox.Remove(simpleBox2);
				this.RemovePhysicalObj(simpleBox2, true);
			}
		}

		// Token: 0x0600320E RID: 12814 RVA: 0x000BFA78 File Offset: 0x000BDC78
		public int CheckGhostBox()
		{
			List<SimpleBox> list = new List<SimpleBox>();
			foreach (SimpleBox simpleBox in this.m_tempBox)
			{
				bool flag = simpleBox.Type > 1;
				if (flag)
				{
					list.Add(simpleBox);
				}
			}
			return list.Count<SimpleBox>();
		}

		// Token: 0x0600320F RID: 12815 RVA: 0x000BFAF4 File Offset: 0x000BDCF4
		public void CreateGhostPoints()
		{
			int backroundHeight = this.m_map.Info.BackroundHeight;
			int backroundWidht = this.m_map.Info.BackroundWidht;
			Point point = new Point(backroundWidht / 2, backroundHeight / 2);
			int num = 180;
			this.m_tempGhostPoints = this.DrawCirclePoints(backroundHeight, 30, (double)(backroundHeight - num), point);
		}

		// Token: 0x06003210 RID: 12816 RVA: 0x000BFB4C File Offset: 0x000BDD4C
		public List<Point> DrawCirclePoints(int points, int dis, double radius, Point center)
		{
			List<Point> list = new List<Point>();
			double num = 6.283185307179586 / (double)points;
			for (double num2 = radius; num2 > (double)dis; num2 -= (double)dis)
			{
				for (int i = 0; i < points; i++)
				{
					double num3 = num * (double)i;
					int num4 = (int)((double)center.X + num2 * Math.Cos(num3));
					int num5 = (int)((double)center.Y + num2 * Math.Sin(num3));
					Point point = new Point(num4, num5);
					list.Add(point);
				}
			}
			return list;
		}

		// Token: 0x06003211 RID: 12817 RVA: 0x000BFBE4 File Offset: 0x000BDDE4
		public List<SimpleBox> CreateBox()
		{
			int num = this.m_players.Count + 2;
			int num2 = 0;
			List<ItemInfo> list = null;
			List<SimpleBox> list2 = new List<SimpleBox>();
			bool flag = this.CurrentTurnTotalDamage > 0;
			if (flag)
			{
				num2 = this.m_random.Next(1, 3);
				bool flag2 = this.m_tempBox.Count + num2 > num;
				if (flag2)
				{
					num2 = num - this.m_tempBox.Count;
				}
				bool flag3 = num2 > 0;
				if (flag3)
				{
					DropInventory.BoxDrop(this.m_roomType, ref list);
				}
			}
			int diedPlayerCount = this.GetDiedPlayerCount();
			int num3 = 0;
			bool flag4 = diedPlayerCount > 0;
			if (flag4)
			{
				num3 = this.m_random.Next(diedPlayerCount);
				bool flag5 = this.m_tempGhostPoints.Count < num;
				if (flag5)
				{
					this.CreateGhostPoints();
				}
				for (int i = 0; i < this.m_tempGhostPoints.Count; i++)
				{
					int num4 = this.m_random.Next(this.m_tempGhostPoints.Count);
					Point point = this.m_tempGhostPoints[num4];
					this.m_tempGhostPoints[num4] = this.m_tempGhostPoints[i];
					this.m_tempGhostPoints[i] = point;
				}
				int num5 = diedPlayerCount + num - this.CheckGhostBox();
				bool flag6 = this.m_tempGhostPoints.Count > num5;
				if (flag6)
				{
					int[] array = new int[] { 2, 3 };
					for (int j = 0; j < num5; j++)
					{
						int num6 = this.m_random.Next(array.Length);
						int num7 = this.m_random.Next(this.m_tempGhostPoints.Count);
						list2.Add(this.AddGhostBox(this.m_tempGhostPoints[num7], array[num6]));
					}
				}
			}
			bool flag7 = this.m_tempBox.Count + num2 + num3 > num;
			if (flag7)
			{
				num3 = num - this.m_tempBox.Count - num2;
			}
			bool flag8 = num3 > 0;
			if (flag8)
			{
			}
			bool flag9 = list != null;
			if (flag9)
			{
				for (int k = 0; k < this.m_tempPoints.Count; k++)
				{
					int num8 = this.m_random.Next(this.m_tempPoints.Count);
					Point point2 = this.m_tempPoints[num8];
					this.m_tempPoints[num8] = this.m_tempPoints[k];
					this.m_tempPoints[k] = point2;
				}
				int num9 = Math.Min(list.Count, this.m_tempPoints.Count);
				for (int l = 0; l < num9; l++)
				{
					list2.Add(this.AddBox(list[l], this.m_tempPoints[l], false));
				}
			}
			this.m_tempPoints.Clear();
			this.m_tempGhostPoints.Clear();
			return list2;
		}

		// Token: 0x06003212 RID: 12818 RVA: 0x000BFEE8 File Offset: 0x000BE0E8
		public void AddLoadingFile(int type, string file, string className)
		{
			bool flag = file != null && className != null;
			if (flag)
			{
				this.m_loadingFiles.Add(new LoadingFileInfo(type, file, className));
			}
		}

		// Token: 0x06003213 RID: 12819 RVA: 0x00018088 File Offset: 0x00016288
		public void ClearLoadingFiles()
		{
			this.m_loadingFiles.Clear();
		}

		// Token: 0x06003214 RID: 12820 RVA: 0x00004D85 File Offset: 0x00002F85
		public void AfterUseItem(ItemInfo item)
		{
		}

		// Token: 0x06003215 RID: 12821 RVA: 0x000BFF1C File Offset: 0x000BE11C
		public byte GetVane(int Wind, int param)
		{
			int num = Math.Abs(Wind);
			if (!true)
			{
			}
			byte b;
			if (param != 1)
			{
				if (param != 3)
				{
					b = 0;
				}
				else
				{
					b = WindMgr.GetWindID(num, 3);
				}
			}
			else
			{
				b = WindMgr.GetWindID(num, 1);
			}
			if (!true)
			{
			}
			return b;
		}

		// Token: 0x06003216 RID: 12822 RVA: 0x000BFF64 File Offset: 0x000BE164
		public void VaneLoading(Player p)
		{
			List<WindInfo> wind = WindMgr.GetWind();
			foreach (WindInfo windInfo in wind)
			{
				this.SendGameWindPic(p, (byte)windInfo.WindID, windInfo.WindPic);
			}
		}

		// Token: 0x06003217 RID: 12823 RVA: 0x00018097 File Offset: 0x00016297
		internal void SendCreateGame()
		{
			this.SendCreateGame(null);
		}

		// Token: 0x06003218 RID: 12824 RVA: 0x000BFFCC File Offset: 0x000BE1CC
		internal void SendCreateGame(Player player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(101);
			gspacketIn.WriteInt((int)((byte)this.m_roomType));
			gspacketIn.WriteInt((int)((byte)this.m_gameType));
			gspacketIn.WriteInt(this.m_timeType);
			List<Player> allFightPlayers = this.GetAllFightPlayers();
			gspacketIn.WriteInt(allFightPlayers.Count);
			foreach (Player player2 in allFightPlayers)
			{
				IGamePlayer playerDetail = player2.PlayerDetail;
				gspacketIn.WriteInt(playerDetail.AreaID);
				gspacketIn.WriteString(playerDetail.AreaName);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ID);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.NickName);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.isViewer);
				gspacketIn.WriteByte(playerDetail.PlayerCharacter.TypeVIP);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.VIPLevel);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.Sex);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Hide);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Style);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Colors);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Skin);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Grade);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Repute);
				bool flag = playerDetail.MainWeapon.GoldValidDate();
				if (flag)
				{
					gspacketIn.WriteInt(playerDetail.MainWeapon.GoldEquip.TemplateID);
				}
				else
				{
					gspacketIn.WriteInt(playerDetail.MainWeapon.TemplateID);
				}
				gspacketIn.WriteInt(playerDetail.MainWeapon.RefineryLevel);
				gspacketIn.WriteString(playerDetail.MainWeapon.Template.Pic);
				gspacketIn.WriteDateTime(DateTime.Now);
				gspacketIn.WriteInt((playerDetail.SecondWeapon != null) ? playerDetail.SecondWeapon.TemplateID : 0);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Nimbus);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaID);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.ConsortiaName);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.badgeID);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaLevel);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ConsortiaRepute);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Win);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Total);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.FightPower);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.ApprenticeshipState);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.MasterID);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.MasterOrApprentices);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.AchievementPoint);
				gspacketIn.WriteString(playerDetail.PlayerCharacter.Honor);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.Offer);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.DailyLeagueFirst);
				gspacketIn.WriteInt(playerDetail.PlayerCharacter.DailyLeagueLastScore);
				gspacketIn.WriteBoolean(playerDetail.PlayerCharacter.IsMarried);
				bool isMarried = playerDetail.PlayerCharacter.IsMarried;
				if (isMarried)
				{
					gspacketIn.WriteInt(playerDetail.PlayerCharacter.SpouseID);
					gspacketIn.WriteString(playerDetail.PlayerCharacter.SpouseName);
				}
				gspacketIn.WriteInt((int)playerDetail.GMExperienceRate);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt((int)playerDetail.GMOfferRate);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt((int)playerDetail.GMRichesRate);
				gspacketIn.WriteInt(0);
				gspacketIn.WriteInt(player2.Team);
				gspacketIn.WriteInt(player2.Id);
				gspacketIn.WriteInt(player2.MaxBlood);
				bool flag2 = player2.UserPet == null;
				if (flag2)
				{
					gspacketIn.WriteInt(0);
				}
				else
				{
					gspacketIn.WriteInt(1);
					gspacketIn.WriteInt(player2.UserPet.Place);
					gspacketIn.WriteInt(player2.UserPet.TemplateID);
					gspacketIn.WriteInt(player2.UserPet.ID);
					gspacketIn.WriteString(player2.UserPet.Name);
					gspacketIn.WriteInt(player2.UserPet.UserID);
					gspacketIn.WriteInt(player2.UserPet.Level);
					string[] array = player2.UserPet.SkillEquip.Split(new char[] { '|' });
					gspacketIn.WriteInt(array.Length);
					string[] array2 = array;
					foreach (string text in array2)
					{
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[1]));
						gspacketIn.WriteInt(int.Parse(text.Split(new char[] { ',' })[0]));
					}
				}
			}
			bool flag3 = player == null;
			if (flag3)
			{
				this.SendToAll(gspacketIn);
			}
			else
			{
				player.PlayerDetail.SendTCP(gspacketIn);
			}
		}

		// Token: 0x06003219 RID: 12825 RVA: 0x000C0558 File Offset: 0x000BE758
		internal void SendOpenSelectLeaderWindow(int maxTime)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(102);
			gspacketIn.WriteInt(maxTime);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600321A RID: 12826 RVA: 0x000C0588 File Offset: 0x000BE788
		internal void SendSkipNext(Player player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(12);
			player.PlayerDetail.SendTCP(gspacketIn);
		}

		// Token: 0x0600321B RID: 12827 RVA: 0x000C05B4 File Offset: 0x000BE7B4
		internal void SendStartLoading(int maxTime)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(103);
			gspacketIn.WriteInt(maxTime);
			gspacketIn.WriteInt(this.m_map.Info.ID);
			gspacketIn.WriteInt(this.m_loadingFiles.Count);
			foreach (LoadingFileInfo loadingFileInfo in this.m_loadingFiles)
			{
				gspacketIn.WriteInt(loadingFileInfo.Type);
				gspacketIn.WriteString(loadingFileInfo.Path);
				gspacketIn.WriteString(loadingFileInfo.ClassName);
			}
			bool flag = this.IsSpecialPVE();
			if (flag)
			{
				gspacketIn.WriteInt(0);
			}
			else
			{
				GameNeedPetSkillInfo[] gameNeedPetSkill = PetMgr.GetGameNeedPetSkill();
				gspacketIn.WriteInt(gameNeedPetSkill.Length);
				GameNeedPetSkillInfo[] array = gameNeedPetSkill;
				foreach (GameNeedPetSkillInfo gameNeedPetSkillInfo in array)
				{
					gspacketIn.WriteString(gameNeedPetSkillInfo.Pic.ToString());
					gspacketIn.WriteString(gameNeedPetSkillInfo.EffectPic);
				}
			}
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600321C RID: 12828 RVA: 0x000C06EC File Offset: 0x000BE8EC
		internal void SendAddPhysicalObj(PhysicalObj obj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(48);
			gspacketIn.WriteInt(obj.Id);
			gspacketIn.WriteInt(obj.Type);
			gspacketIn.WriteInt(obj.X);
			gspacketIn.WriteInt(obj.Y);
			gspacketIn.WriteString(obj.Model);
			gspacketIn.WriteString(obj.CurrentAction);
			gspacketIn.WriteInt(obj.Scale);
			gspacketIn.WriteInt(obj.Scale);
			gspacketIn.WriteInt(obj.Rotation);
			gspacketIn.WriteInt(obj.phyBringToFront);
			gspacketIn.WriteInt(obj.typeEffect);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600321D RID: 12829 RVA: 0x000C07A4 File Offset: 0x000BE9A4
		internal void SendAddPhysicalTip(PhysicalObj obj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(68);
			gspacketIn.WriteInt(obj.Id);
			gspacketIn.WriteInt(obj.Type);
			gspacketIn.WriteInt(obj.X);
			gspacketIn.WriteInt(obj.Y);
			gspacketIn.WriteString(obj.Model);
			gspacketIn.WriteString(obj.CurrentAction);
			gspacketIn.WriteInt(obj.Scale);
			gspacketIn.WriteInt(obj.Rotation);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600321E RID: 12830 RVA: 0x000180A2 File Offset: 0x000162A2
		internal void SendPhysicalObjFocus(Physics obj, int type)
		{
			this.SendPhysicalObjFocus(obj.X, obj.Y, type);
		}

		// Token: 0x0600321F RID: 12831 RVA: 0x000C0834 File Offset: 0x000BEA34
		internal void SendPhysicalObjFocus(int x, int y, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(62);
			gspacketIn.WriteInt(type);
			gspacketIn.WriteInt(x);
			gspacketIn.WriteInt(y);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003220 RID: 12832 RVA: 0x000C0874 File Offset: 0x000BEA74
		internal void SendPhysicalObjPlayAction(PhysicalObj obj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(66);
			gspacketIn.WriteInt(obj.Id);
			gspacketIn.WriteString(obj.CurrentAction);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003221 RID: 12833 RVA: 0x000C08B8 File Offset: 0x000BEAB8
		internal void SendRemovePhysicalObj(PhysicalObj obj)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(53);
			gspacketIn.WriteInt(obj.Id);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003222 RID: 12834 RVA: 0x000C08EC File Offset: 0x000BEAEC
		internal void SendRemoveLiving(int id)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(53);
			gspacketIn.WriteInt(id);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003223 RID: 12835 RVA: 0x000C091C File Offset: 0x000BEB1C
		internal void SendLivingBoltMove(Living living, int toX, int toY, string action)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(72);
			gspacketIn.WriteInt(toX);
			gspacketIn.WriteInt(toY);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003224 RID: 12836 RVA: 0x000C0980 File Offset: 0x000BEB80
		internal void SendAddLiving(Living living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(64);
			gspacketIn.WriteByte((byte)living.Type);
			gspacketIn.WriteInt(living.Id);
			gspacketIn.WriteString(living.Name);
			gspacketIn.WriteString(living.ModelId);
			gspacketIn.WriteString(living.ActionStr);
			gspacketIn.WriteInt(living.X);
			gspacketIn.WriteInt(living.Y);
			gspacketIn.WriteInt(living.Blood);
			gspacketIn.WriteInt(living.MaxBlood);
			gspacketIn.WriteInt(living.Team);
			gspacketIn.WriteByte((byte)living.Direction);
			gspacketIn.WriteByte(living.Config.isBotom);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003225 RID: 12837 RVA: 0x000C0A58 File Offset: 0x000BEC58
		internal void SendPlayerMove(Player player, int type, int x, int y, byte dir, bool isLiving)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(9);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteInt(x);
			gspacketIn.WriteInt(y);
			gspacketIn.WriteByte(dir);
			gspacketIn.WriteBoolean(isLiving);
			bool flag = type == 2;
			if (flag)
			{
				gspacketIn.WriteInt(this.m_tempBox.Count);
				foreach (SimpleBox simpleBox in this.m_tempBox)
				{
					gspacketIn.WriteInt(simpleBox.X);
					gspacketIn.WriteInt(simpleBox.Y);
				}
			}
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003226 RID: 12838 RVA: 0x000C0B38 File Offset: 0x000BED38
		internal void SendLivingMoveTo(Living living, int fromX, int fromY, int toX, int toY, string action, int speed, string sAction)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(55);
			gspacketIn.WriteInt(fromX);
			gspacketIn.WriteInt(fromY);
			gspacketIn.WriteInt(toX);
			gspacketIn.WriteInt(toY);
			gspacketIn.WriteInt(speed);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			gspacketIn.WriteString(sAction);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003227 RID: 12839 RVA: 0x000C0BC0 File Offset: 0x000BEDC0
		internal void SendLivingSay(Living living, string msg, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(59);
			gspacketIn.WriteString(msg);
			gspacketIn.WriteInt(type);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003228 RID: 12840 RVA: 0x000C0C0C File Offset: 0x000BEE0C
		internal void SendLivingFall(Living living, int toX, int toY, int speed, string action, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(56);
			gspacketIn.WriteInt(toX);
			gspacketIn.WriteInt(toY);
			gspacketIn.WriteInt(speed);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			gspacketIn.WriteInt(type);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003229 RID: 12841 RVA: 0x000C0C84 File Offset: 0x000BEE84
		internal void SendLivingJump(Living living, int toX, int toY, int speed, string action, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(57);
			gspacketIn.WriteInt(toX);
			gspacketIn.WriteInt(toY);
			gspacketIn.WriteInt(speed);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			gspacketIn.WriteInt(type);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600322A RID: 12842 RVA: 0x000C0CFC File Offset: 0x000BEEFC
		internal void SendLivingBeat(Living living, Living target, int totalDemageAmount, string action, int livingCount, int attackEffect)
		{
			int num = 0;
			bool flag = target is Player;
			if (flag)
			{
				Player player = target as Player;
				num = player.Dander;
			}
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(58);
			gspacketIn.WriteString((!string.IsNullOrEmpty(action)) ? action : "");
			gspacketIn.WriteInt(livingCount);
			for (int i = 1; i <= livingCount; i++)
			{
				gspacketIn.WriteInt(target.Id);
				gspacketIn.WriteInt(totalDemageAmount);
				gspacketIn.WriteInt(target.Blood);
				gspacketIn.WriteInt(num);
				gspacketIn.WriteInt(attackEffect);
			}
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600322B RID: 12843 RVA: 0x000C0DC8 File Offset: 0x000BEFC8
		internal void SendLivingPlayMovie(Living living, string action)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(60);
			gspacketIn.WriteString(action);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600322C RID: 12844 RVA: 0x000C0E0C File Offset: 0x000BF00C
		internal void SendPlayerInfoInGame(Player living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter2 = this.LifeTime;
			gspacketIn.WriteByte(120);
			gspacketIn.WriteInt(living.PlayerDetail.AreaID);
			gspacketIn.WriteInt(living.PlayerDetail.PlayerCharacter.ID);
			gspacketIn.WriteInt(living.Team);
			gspacketIn.WriteInt(living.Id);
			gspacketIn.WriteInt(living.MaxBlood);
			gspacketIn.WriteBoolean(living.Ready);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600322D RID: 12845 RVA: 0x000C0EA0 File Offset: 0x000BF0A0
		internal void SendGameUpdateHealth(Living player, int type, int value)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(11);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteInt(player.Blood);
			gspacketIn.WriteInt(value);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600322E RID: 12846 RVA: 0x000C0EF8 File Offset: 0x000BF0F8
		internal void SendGameUpdateDander(TurnedLiving player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(14);
			gspacketIn.WriteInt(player.Dander);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600322F RID: 12847 RVA: 0x000C0F40 File Offset: 0x000BF140
		internal void SendGameUpdateFrozenState(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(33);
			gspacketIn.WriteBoolean(player.IsFrost);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003230 RID: 12848 RVA: 0x000C0F88 File Offset: 0x000BF188
		internal void SendGameUpdateNoHoleState(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(82);
			gspacketIn.WriteBoolean(player.IsNoHole);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003231 RID: 12849 RVA: 0x000C0FD0 File Offset: 0x000BF1D0
		internal void SendGameUpdateHideState(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(35);
			gspacketIn.WriteBoolean(player.IsHide);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003232 RID: 12850 RVA: 0x000C1018 File Offset: 0x000BF218
		internal void SendGameUpdateSealState(Living player, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(18);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteBoolean(player.GetSealState());
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003233 RID: 12851 RVA: 0x000C1068 File Offset: 0x000BF268
		internal void SendGameUpdateShootCount(Player player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(46);
			gspacketIn.WriteByte((byte)player.ShootCount);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003234 RID: 12852 RVA: 0x000C10A4 File Offset: 0x000BF2A4
		internal void SendGameUpdateBall(Player player, bool Special)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(20);
			gspacketIn.WriteBoolean(Special);
			gspacketIn.WriteInt(player.CurrentBall.ID);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003235 RID: 12853 RVA: 0x000C10F8 File Offset: 0x000BF2F8
		internal void SendGamePickBox(Living player, int index, int arkType, string goods)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(49);
			gspacketIn.WriteByte((byte)index);
			gspacketIn.WriteByte((byte)arkType);
			gspacketIn.WriteString(goods);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003236 RID: 12854 RVA: 0x000C1140 File Offset: 0x000BF340
		internal void SendGameUpdateWind(float wind)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(38);
			int num = (int)(wind * 10f);
			gspacketIn.WriteInt(num);
			gspacketIn.WriteBoolean(num > 0);
			gspacketIn.WriteByte(this.GetVane(num, 1));
			gspacketIn.WriteByte(this.GetVane(num, 2));
			gspacketIn.WriteByte(this.GetVane(num, 3));
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003237 RID: 12855 RVA: 0x000C11B0 File Offset: 0x000BF3B0
		internal void SendGameWindPic(Player p, byte windId, byte[] windpic)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(241);
			gspacketIn.WriteByte(windId);
			gspacketIn.Write(windpic);
			bool flag = p != null;
			if (flag)
			{
				p.PlayerDetail.SendTCP(gspacketIn);
			}
			else
			{
				this.SendToAll(gspacketIn);
			}
		}

		// Token: 0x06003238 RID: 12856 RVA: 0x000C1208 File Offset: 0x000BF408
		internal void SendUseDeputyWeapon(Player player, int ResCount)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(84);
			gspacketIn.WriteInt(ResCount);
			player.PlayerDetail.SendTCP(gspacketIn);
		}

		// Token: 0x06003239 RID: 12857 RVA: 0x000180B9 File Offset: 0x000162B9
		internal void SendPlayerUseProp(Player player, int type, int place, int templateID)
		{
			this.SendPlayerUseProp(player, type, place, templateID, player);
		}

		// Token: 0x0600323A RID: 12858 RVA: 0x000C1250 File Offset: 0x000BF450
		internal void SendPlayerUseProp(Living player, int type, int place, int templateID, Player p)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(32);
			gspacketIn.WriteByte((byte)type);
			gspacketIn.WriteInt(place);
			gspacketIn.WriteInt(templateID);
			gspacketIn.WriteInt(p.Id);
			gspacketIn.WriteBoolean(templateID == 10017);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600323B RID: 12859 RVA: 0x000C12C4 File Offset: 0x000BF4C4
		internal void SendGamePlayerTakeCard(Player player, int index, int templateID, int count, bool isSysTake)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(98);
			gspacketIn.WriteBoolean(isSysTake);
			gspacketIn.WriteByte((byte)index);
			gspacketIn.WriteInt(templateID);
			gspacketIn.WriteInt(count);
			gspacketIn.WriteBoolean(player.PlayerDetail.PlayerCharacter.TypeVIP > 0 && player.HasPaymentTakeCard);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600323C RID: 12860 RVA: 0x000C1344 File Offset: 0x000BF544
		internal void SendGameNextTurn(Living living, BaseGame game, List<SimpleBox> newBoxes)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(6);
			int num = (int)(this.m_map.wind * 10f);
			gspacketIn.WriteBoolean(num > 0);
			gspacketIn.WriteByte(this.GetVane(num, 1));
			gspacketIn.WriteByte(this.GetVane(num, 2));
			gspacketIn.WriteByte(this.GetVane(num, 3));
			gspacketIn.WriteBoolean(living.IsHide);
			gspacketIn.WriteInt(this.getTurnTime());
			gspacketIn.WriteInt(newBoxes.Count);
			foreach (SimpleBox simpleBox in newBoxes)
			{
				gspacketIn.WriteInt(simpleBox.Id);
				gspacketIn.WriteInt(simpleBox.X);
				gspacketIn.WriteInt(simpleBox.Y);
				gspacketIn.WriteInt(simpleBox.Type);
			}
			bool flag = living is TurnedLiving;
			if (flag)
			{
				List<Player> allFightPlayers = game.GetAllFightPlayers();
				gspacketIn.WriteInt(allFightPlayers.Count);
				foreach (Player player in allFightPlayers)
				{
					gspacketIn.WriteInt(player.Id);
					gspacketIn.WriteBoolean(player.IsLiving);
					gspacketIn.WriteInt(player.X);
					gspacketIn.WriteInt(player.Y);
					gspacketIn.WriteInt(player.Blood);
					gspacketIn.WriteBoolean(player.IsNoHole);
					gspacketIn.WriteInt(player.Energy);
					gspacketIn.WriteInt(player.psychic);
					gspacketIn.WriteInt(player.Dander);
					bool flag2 = player.UserPet == null;
					if (flag2)
					{
						gspacketIn.WriteInt(0);
						gspacketIn.WriteInt(0);
						gspacketIn.WriteInt(0);
					}
					else
					{
						gspacketIn.WriteInt(player.PetMaxMP);
						gspacketIn.WriteInt(player.PetMP);
						gspacketIn.WriteInt(player.PetFlag);
					}
					gspacketIn.WriteInt(player.ShootCount);
				}
				gspacketIn.WriteInt(game.TurnIndex);
			}
			gspacketIn.WriteBoolean(false);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600323D RID: 12861 RVA: 0x000C15E0 File Offset: 0x000BF7E0
		internal void SendLivingUpdateDirection(Living living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(7);
			gspacketIn.WriteInt(living.Direction);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600323E RID: 12862 RVA: 0x000C1620 File Offset: 0x000BF820
		internal void SendLivingUpdateAngryState(Living living)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(118);
			gspacketIn.WriteInt(living.State);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600323F RID: 12863 RVA: 0x000C1664 File Offset: 0x000BF864
		internal void SendEquipEffect(Living player, string buffer)
		{
			GSPacketIn gspacketIn = new GSPacketIn(3);
			gspacketIn.WriteInt(0);
			gspacketIn.WriteString(buffer);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003240 RID: 12864 RVA: 0x000C1694 File Offset: 0x000BF894
		internal void SendMessage(IGamePlayer player, string msg, string msg1, int type)
		{
			bool flag = msg != null;
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(3);
				gspacketIn.WriteInt(type);
				gspacketIn.WriteString(msg);
				player.SendTCP(gspacketIn);
			}
			bool flag2 = msg1 != null;
			if (flag2)
			{
				GSPacketIn gspacketIn2 = new GSPacketIn(3);
				gspacketIn2.WriteInt(type);
				gspacketIn2.WriteString(msg1);
				this.SendToAll(gspacketIn2, player);
			}
		}

		// Token: 0x06003241 RID: 12865 RVA: 0x000C16F8 File Offset: 0x000BF8F8
		internal void SendFightAchievement(Living living, int achievID, int dis, int delay)
		{
			bool flag = living.Game.RoomType == eRoomType.Match || living.Game.RoomType == eRoomType.Freedom || living.Game.RoomType == eRoomType.Score || living.Game.RoomType == eRoomType.Rank;
			if (flag)
			{
				GSPacketIn gspacketIn = new GSPacketIn(91);
				gspacketIn.WriteByte(238);
				gspacketIn.WriteInt(achievID);
				gspacketIn.WriteInt(dis);
				gspacketIn.WriteInt(delay);
				this.SendToAll(gspacketIn);
			}
		}

		// Token: 0x06003242 RID: 12866 RVA: 0x000C1780 File Offset: 0x000BF980
		internal void SendPlayerPicture(Living living, int type, bool state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(128);
			gspacketIn.WriteInt(type);
			gspacketIn.WriteBoolean(state);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003243 RID: 12867 RVA: 0x000C17C8 File Offset: 0x000BF9C8
		internal void SendPlayerRemove(Player player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(94, player.PlayerDetail.PlayerCharacter.ID);
			gspacketIn.WriteByte(5);
			gspacketIn.WriteInt(player.PlayerDetail.AreaID);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003244 RID: 12868 RVA: 0x000C1810 File Offset: 0x000BFA10
		internal void SendAttackEffect(Living player, int type)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(129);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(type);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003245 RID: 12869 RVA: 0x000C1858 File Offset: 0x000BFA58
		internal void SendSyncLifeTime()
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(131);
			gspacketIn.WriteInt(this.m_lifeTime);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003246 RID: 12870 RVA: 0x000C1890 File Offset: 0x000BFA90
		internal void SendGamePlayerProperty(Living living, string type, string state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(41);
			gspacketIn.WriteString(type);
			gspacketIn.WriteString(state);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003247 RID: 12871 RVA: 0x000C18DC File Offset: 0x000BFADC
		internal void SendCurrentPlayerProperty(Living living, string type, string state)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, living.Id);
			gspacketIn.Parameter1 = living.Id;
			gspacketIn.WriteByte(41);
			gspacketIn.WriteString(type);
			gspacketIn.WriteString(state);
			bool flag = living is Player;
			if (flag)
			{
				((Player)living).PlayerDetail.SendTCP(gspacketIn);
			}
		}

		// Token: 0x06003248 RID: 12872 RVA: 0x000C1940 File Offset: 0x000BFB40
		public void SendLivingShowBlood(Living player, int isShow)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(80);
			gspacketIn.WriteInt(player.Id);
			gspacketIn.WriteInt(isShow);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003249 RID: 12873 RVA: 0x000C1984 File Offset: 0x000BFB84
		internal void SendLivingTurnRotation(Living player, int rotation, int speed, string endPlay)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(85);
			gspacketIn.WriteInt(rotation);
			gspacketIn.WriteInt(speed);
			gspacketIn.WriteString(endPlay);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600324A RID: 12874 RVA: 0x000C19D8 File Offset: 0x000BFBD8
		internal void SendLivingActionMapping(int id, string source, string value)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, id);
			gspacketIn.Parameter1 = id;
			gspacketIn.WriteByte(223);
			gspacketIn.WriteInt(id);
			gspacketIn.WriteString(source);
			gspacketIn.WriteString(value);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600324B RID: 12875 RVA: 0x000C1A24 File Offset: 0x000BFC24
		internal void SendRoundOneEnd(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(4);
			((Player)player).PlayerDetail.SendTCP(gspacketIn);
		}

		// Token: 0x0600324C RID: 12876 RVA: 0x000C1A54 File Offset: 0x000BFC54
		internal void UpdateMaxBlood(Living player)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(164);
			gspacketIn.WriteInt(player.Id);
			gspacketIn.WriteInt(player.MaxBlood);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600324D RID: 12877 RVA: 0x000C1AA0 File Offset: 0x000BFCA0
		internal void SendTempStyle(Player living, string style, bool isTemp)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(134);
			gspacketIn.WriteInt(1);
			bool flag = !isTemp;
			if (flag)
			{
				gspacketIn.WriteString(living.PlayerDetail.PlayerCharacter.Style);
				gspacketIn.WriteInt(living.PlayerDetail.PlayerCharacter.Hide);
			}
			else
			{
				gspacketIn.WriteString(style);
				gspacketIn.WriteInt(1111111111);
			}
			gspacketIn.WriteBoolean(living.PlayerDetail.PlayerCharacter.Sex);
			gspacketIn.WriteString(living.PlayerDetail.PlayerCharacter.Skin);
			gspacketIn.WriteString(living.PlayerDetail.PlayerCharacter.Colors);
			gspacketIn.WriteInt(living.PlayerDetail.PlayerCharacter.ID);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x0600324E RID: 12878 RVA: 0x000180C9 File Offset: 0x000162C9
		internal void SendPetUseKill(Player player)
		{
			this.SendPetUseKill(player, player.PetEffects.CurrentUseSkill, player.PetEffects.CurrentUseSkill != 0);
		}

		// Token: 0x0600324F RID: 12879 RVA: 0x000C1B80 File Offset: 0x000BFD80
		internal void SendPetUseKill(Player player, int skillId, bool isUse)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteByte(144);
			gspacketIn.WriteInt(skillId);
			gspacketIn.WriteBoolean(isUse);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003250 RID: 12880 RVA: 0x000C1BD0 File Offset: 0x000BFDD0
		internal void SendPetBuff(Living player, PetSkillElementInfo info, bool isActive)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91, player.Id);
			gspacketIn.WriteByte(145);
			gspacketIn.Parameter1 = player.Id;
			gspacketIn.WriteInt(info.ID);
			gspacketIn.WriteString(info.Name);
			gspacketIn.WriteString(info.Description);
			gspacketIn.WriteString(info.Pic.ToString());
			gspacketIn.WriteString(info.EffectPic);
			gspacketIn.WriteBoolean(isActive);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003251 RID: 12881 RVA: 0x000C1C60 File Offset: 0x000BFE60
		public void SendQuizWindow(int QuizID, int ArightResult, int NeedArightResult, int MaxQuizSize, int TimeOut, string Caption, string QuizStr, string ResultStrFirst, string ResultStrSecond, string ResultStrThird)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(24);
			gspacketIn.WriteBoolean(true);
			gspacketIn.WriteInt(QuizID);
			gspacketIn.WriteInt(ArightResult);
			gspacketIn.WriteInt(NeedArightResult);
			gspacketIn.WriteInt(MaxQuizSize);
			gspacketIn.WriteInt(TimeOut);
			gspacketIn.WriteString(Caption);
			gspacketIn.WriteString(QuizStr);
			gspacketIn.WriteString(ResultStrFirst);
			gspacketIn.WriteString(ResultStrSecond);
			gspacketIn.WriteString(ResultStrThird);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003252 RID: 12882 RVA: 0x000C1CE8 File Offset: 0x000BFEE8
		public void SendCloseQuizWindow()
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(24);
			gspacketIn.WriteBoolean(false);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003253 RID: 12883 RVA: 0x000C1D18 File Offset: 0x000BFF18
		internal void SendLockFocus(bool IsLock)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(69);
			gspacketIn.WriteBoolean(IsLock);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003254 RID: 12884 RVA: 0x000C1D48 File Offset: 0x000BFF48
		public void ShowBloodItem(int id)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(69);
			gspacketIn.WriteInt(id);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003255 RID: 12885 RVA: 0x000C1D78 File Offset: 0x000BFF78
		public void SendShowBloodItem(int livingId)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91);
			gspacketIn.WriteByte(73);
			gspacketIn.WriteInt(livingId);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003256 RID: 12886 RVA: 0x000C1DA8 File Offset: 0x000BFFA8
		public void LivingChangeAngle(Living living, int Speed, int Angle, string endPlay)
		{
			GSPacketIn gspacketIn = new GSPacketIn(91)
			{
				Parameter1 = living.Id
			};
			gspacketIn.WriteByte(85);
			gspacketIn.WriteInt(Angle);
			gspacketIn.WriteInt(Speed);
			gspacketIn.WriteString(endPlay);
			this.SendToAll(gspacketIn);
		}

		// Token: 0x06003257 RID: 12887 RVA: 0x000C1DF8 File Offset: 0x000BFFF8
		protected void OnGameOverred()
		{
			bool flag = this.GameOverred != null;
			if (flag)
			{
				this.GameOverred(this);
			}
		}

		// Token: 0x06003258 RID: 12888 RVA: 0x000C1E24 File Offset: 0x000C0024
		protected void OnBeginNewTurn()
		{
			bool flag = this.BeginNewTurn != null;
			if (flag)
			{
				this.BeginNewTurn(this);
			}
		}

		// Token: 0x06003259 RID: 12889 RVA: 0x000C1E50 File Offset: 0x000C0050
		public void OnGameOverLog(int _roomId, eRoomType _roomType, eGameType _fightType, int _changeTeam, DateTime _playBegin, DateTime _playEnd, int _userCount, int _mapId, string _teamA, string _teamB, string _playResult, int _winTeam, string BossWar)
		{
			bool flag = this.GameOverLog != null;
			if (flag)
			{
				this.GameOverLog(_roomId, _roomType, _fightType, _changeTeam, _playBegin, _playEnd, _userCount, _mapId, _teamA, _teamB, _playResult, _winTeam, this.BossWarField);
			}
		}

		// Token: 0x0600325A RID: 12890 RVA: 0x000C1E94 File Offset: 0x000C0094
		public void OnGameNpcDie(int Id)
		{
			bool flag = this.GameNpcDie != null;
			if (flag)
			{
				this.GameNpcDie(Id);
			}
		}

		// Token: 0x0600325B RID: 12891 RVA: 0x000C1EC0 File Offset: 0x000C00C0
		public override string ToString()
		{
			return string.Format("Id:{0},player:{1},state:{2},current:{3},turnIndex:{4},actions:{5}", new object[]
			{
				base.Id,
				this.PlayerCount,
				this.GameState,
				this.CurrentLiving,
				this.m_turnIndex,
				this.m_actions.Count
			});
		}

		// Token: 0x04001961 RID: 6497
		public static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

		// Token: 0x04001962 RID: 6498
		public readonly int[] EquipPlace = new int[]
		{
			1, 2, 3, 4, 5, 6, 11, 13, 14, 15,
			16, 17, 18, 19, 20
		};

		// Token: 0x04001963 RID: 6499
		protected int turnIndex;

		// Token: 0x04001964 RID: 6500
		protected int m_nextPlayerId;

		// Token: 0x04001965 RID: 6501
		protected int m_nextWind;

		// Token: 0x04001966 RID: 6502
		protected int m_nextWindRate;

		// Token: 0x04001967 RID: 6503
		protected eGameState m_gameState;

		// Token: 0x04001968 RID: 6504
		protected eGameState m_gameSecondState;

		// Token: 0x04001969 RID: 6505
		protected Map m_map;

		// Token: 0x0400196A RID: 6506
		protected Dictionary<int, Player> m_players;

		// Token: 0x0400196B RID: 6507
		protected List<Living> m_livings;

		// Token: 0x0400196C RID: 6508
		protected List<Living> m_decklivings;

		// Token: 0x0400196D RID: 6509
		protected Random m_random;

		// Token: 0x0400196E RID: 6510
		protected TurnedLiving m_currentLiving;

		// Token: 0x0400196F RID: 6511
		public TurnedLiving LastTurnLiving;

		// Token: 0x04001970 RID: 6512
		public int PhysicalId;

		// Token: 0x04001971 RID: 6513
		public int CurrentTurnTotalDamage;

		// Token: 0x04001972 RID: 6514
		public int TotalHurt;

		// Token: 0x04001973 RID: 6515
		public int ConsortiaAlly;

		// Token: 0x04001974 RID: 6516
		public int RichesRate;

		// Token: 0x04001975 RID: 6517
		public string BossWarField;

		// Token: 0x04001976 RID: 6518
		private ArrayList m_actions;

		// Token: 0x04001977 RID: 6519
		private List<TurnedLiving> m_turnQueue;

		// Token: 0x04001978 RID: 6520
		protected Dictionary<int, string> m_logStartIps;

		// Token: 0x04001979 RID: 6521
		private int m_roomId;

		// Token: 0x0400197A RID: 6522
		public bool FrozenWind;

		// Token: 0x0400197B RID: 6523
		public int VortexBombKillCount = 0;

		// Token: 0x0400197C RID: 6524
		public int[] Cards;

		// Token: 0x0400197D RID: 6525
		private int m_lifeTime = 0;

		// Token: 0x0400197E RID: 6526
		private long m_waitTimer = 0L;

		// Token: 0x0400197F RID: 6527
		private long m_lastWaitTimer = 0L;

		// Token: 0x04001980 RID: 6528
		private long m_passTick = 0L;

		// Token: 0x04001981 RID: 6529
		public int CurrentActionCount = 0;

		// Token: 0x04001982 RID: 6530
		public int loadBossID = 0;

		// Token: 0x04001983 RID: 6531
		private List<SimpleBox> m_tempBox;

		// Token: 0x04001984 RID: 6532
		private List<Point> m_tempPoints;

		// Token: 0x04001985 RID: 6533
		private List<Point> m_tempGhostPoints;

		// Token: 0x04001986 RID: 6534
		private List<LoadingFileInfo> m_loadingFiles = new List<LoadingFileInfo>();

		// Token: 0x04001987 RID: 6535
		public int TotalCostMoney;

		// Token: 0x04001988 RID: 6536
		public int TotalCostGold;

		// Token: 0x0200049A RID: 1178
		// (Invoke) Token: 0x0600325E RID: 12894
		public delegate void GameOverLogEventHandle(int roomId, eRoomType roomType, eGameType fightType, int changeTeam, DateTime playBegin, DateTime playEnd, int userCount, int mapId, string teamA, string teamB, string playResult, int winTeam, string BossWar);

		// Token: 0x0200049B RID: 1179
		// (Invoke) Token: 0x06003262 RID: 12898
		public delegate void GameNpcDieEventHandle(int NpcId);
	}
}
