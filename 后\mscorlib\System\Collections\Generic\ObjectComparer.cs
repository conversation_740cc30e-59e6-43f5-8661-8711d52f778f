﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004BD RID: 1213
	[Serializable]
	internal class ObjectComparer<T> : Comparer<T>
	{
		// Token: 0x06003A3D RID: 14909 RVA: 0x000DDF92 File Offset: 0x000DC192
		public override int Compare(T x, T y)
		{
			return Comparer.Default.Compare(x, y);
		}

		// Token: 0x06003A3E RID: 14910 RVA: 0x000DDFAC File Offset: 0x000DC1AC
		public override bool Equals(object obj)
		{
			ObjectComparer<T> objectComparer = obj as ObjectComparer<T>;
			return objectComparer != null;
		}

		// Token: 0x06003A3F RID: 14911 RVA: 0x000DDFC4 File Offset: 0x000DC1C4
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}
	}
}
