﻿using System;
using System.Security;

namespace System.Collections.Generic
{
	// Token: 0x020004CA RID: 1226
	internal sealed class RandomizedObjectEqualityComparer : IEqualityComparer, IWellKnownStringEqualityComparer
	{
		// Token: 0x06003ABC RID: 15036 RVA: 0x000DF932 File Offset: 0x000DDB32
		public RandomizedObjectEqualityComparer()
		{
			this._entropy = HashHelpers.GetEntropy();
		}

		// Token: 0x06003ABD RID: 15037 RVA: 0x000DF945 File Offset: 0x000DDB45
		public bool Equals(object x, object y)
		{
			if (x != null)
			{
				return y != null && x.Equals(y);
			}
			return y == null;
		}

		// Token: 0x06003ABE RID: 15038 RVA: 0x000DF960 File Offset: 0x000DDB60
		[SecuritySafeCritical]
		public int GetHashCode(object obj)
		{
			if (obj == null)
			{
				return 0;
			}
			string text = obj as string;
			if (text != null)
			{
				return string.InternalMarvin32HashString(text, text.Length, this._entropy);
			}
			return obj.GetHashCode();
		}

		// Token: 0x06003ABF RID: 15039 RVA: 0x000DF998 File Offset: 0x000DDB98
		public override bool Equals(object obj)
		{
			RandomizedObjectEqualityComparer randomizedObjectEqualityComparer = obj as RandomizedObjectEqualityComparer;
			return randomizedObjectEqualityComparer != null && this._entropy == randomizedObjectEqualityComparer._entropy;
		}

		// Token: 0x06003AC0 RID: 15040 RVA: 0x000DF9BF File Offset: 0x000DDBBF
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode() ^ (int)(this._entropy & 2147483647L);
		}

		// Token: 0x06003AC1 RID: 15041 RVA: 0x000DF9E0 File Offset: 0x000DDBE0
		IEqualityComparer IWellKnownStringEqualityComparer.GetRandomizedEqualityComparer()
		{
			return new RandomizedObjectEqualityComparer();
		}

		// Token: 0x06003AC2 RID: 15042 RVA: 0x000DF9E7 File Offset: 0x000DDBE7
		IEqualityComparer IWellKnownStringEqualityComparer.GetEqualityComparerForSerialization()
		{
			return null;
		}

		// Token: 0x04001953 RID: 6483
		private long _entropy;
	}
}
