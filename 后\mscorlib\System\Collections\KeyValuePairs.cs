﻿using System;
using System.Diagnostics;

namespace System.Collections
{
	// Token: 0x020004A3 RID: 1187
	[DebuggerDisplay("{value}", Name = "[{key}]", Type = "")]
	internal class KeyValuePairs
	{
		// Token: 0x060038D6 RID: 14550 RVA: 0x000D9D8D File Offset: 0x000D7F8D
		public KeyValuePairs(object key, object value)
		{
			this.value = value;
			this.key = key;
		}

		// Token: 0x1700087C RID: 2172
		// (get) Token: 0x060038D7 RID: 14551 RVA: 0x000D9DA3 File Offset: 0x000D7FA3
		public object Key
		{
			get
			{
				return this.key;
			}
		}

		// Token: 0x1700087D RID: 2173
		// (get) Token: 0x060038D8 RID: 14552 RVA: 0x000D9DAB File Offset: 0x000D7FAB
		public object Value
		{
			get
			{
				return this.value;
			}
		}

		// Token: 0x04001903 RID: 6403
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private object key;

		// Token: 0x04001904 RID: 6404
		[DebuggerBrowsable(DebuggerBrowsableState.Never)]
		private object value;
	}
}
