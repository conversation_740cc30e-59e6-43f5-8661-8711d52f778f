﻿using System;
using System.Runtime.InteropServices;

namespace System.Collections
{
	// Token: 0x020004A2 RID: 1186
	[ComVisible(true)]
	[__DynamicallyInvokable]
	public interface IList : ICollection, IEnumerable
	{
		// Token: 0x17000879 RID: 2169
		[__DynamicallyInvokable]
		object this[int index]
		{
			[__DynamicallyInvokable]
			get;
			[__DynamicallyInvokable]
			set;
		}

		// Token: 0x060038CD RID: 14541
		[__DynamicallyInvokable]
		int Add(object value);

		// Token: 0x060038CE RID: 14542
		[__DynamicallyInvokable]
		bool Contains(object value);

		// Token: 0x060038CF RID: 14543
		[__DynamicallyInvokable]
		void Clear();

		// Token: 0x1700087A RID: 2170
		// (get) Token: 0x060038D0 RID: 14544
		[__DynamicallyInvokable]
		bool IsReadOnly
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x1700087B RID: 2171
		// (get) Token: 0x060038D1 RID: 14545
		[__DynamicallyInvokable]
		bool IsFixedSize
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x060038D2 RID: 14546
		[__DynamicallyInvokable]
		int IndexOf(object value);

		// Token: 0x060038D3 RID: 14547
		[__DynamicallyInvokable]
		void Insert(int index, object value);

		// Token: 0x060038D4 RID: 14548
		[__DynamicallyInvokable]
		void Remove(object value);

		// Token: 0x060038D5 RID: 14549
		[__DynamicallyInvokable]
		void RemoveAt(int index);
	}
}
