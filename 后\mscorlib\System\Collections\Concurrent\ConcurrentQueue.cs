﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Runtime.Serialization;
using System.Security.Permissions;
using System.Threading;

namespace System.Collections.Concurrent
{
	// Token: 0x020004AF RID: 1199
	[ComVisible(false)]
	[DebuggerDisplay("Count = {Count}")]
	[DebuggerTypeProxy(typeof(SystemCollectionsConcurrent_ProducerConsumerCollectionDebugView<>))]
	[__DynamicallyInvokable]
	[HostProtection(SecurityAction.LinkDemand, Synchronization = true, ExternalThreading = true)]
	[Serializable]
	public class ConcurrentQueue<T> : IProducerConsumerCollection<T>, IEnumerable<T>, IEnumerable, ICollection, IReadOnlyCollection<T>
	{
		// Token: 0x06003981 RID: 14721 RVA: 0x000DC4A8 File Offset: 0x000DA6A8
		[__DynamicallyInvokable]
		public ConcurrentQueue()
		{
			this.m_head = (this.m_tail = new ConcurrentQueue<T>.Segment(0L, this));
		}

		// Token: 0x06003982 RID: 14722 RVA: 0x000DC4D8 File Offset: 0x000DA6D8
		private void InitializeFromCollection(IEnumerable<T> collection)
		{
			ConcurrentQueue<T>.Segment segment = new ConcurrentQueue<T>.Segment(0L, this);
			this.m_head = segment;
			int num = 0;
			foreach (T t in collection)
			{
				segment.UnsafeAdd(t);
				num++;
				if (num >= 32)
				{
					segment = segment.UnsafeGrow();
					num = 0;
				}
			}
			this.m_tail = segment;
		}

		// Token: 0x06003983 RID: 14723 RVA: 0x000DC550 File Offset: 0x000DA750
		[__DynamicallyInvokable]
		public ConcurrentQueue(IEnumerable<T> collection)
		{
			if (collection == null)
			{
				throw new ArgumentNullException("collection");
			}
			this.InitializeFromCollection(collection);
		}

		// Token: 0x06003984 RID: 14724 RVA: 0x000DC56D File Offset: 0x000DA76D
		[OnSerializing]
		private void OnSerializing(StreamingContext context)
		{
			this.m_serializationArray = this.ToArray();
		}

		// Token: 0x06003985 RID: 14725 RVA: 0x000DC57B File Offset: 0x000DA77B
		[OnDeserialized]
		private void OnDeserialized(StreamingContext context)
		{
			this.InitializeFromCollection(this.m_serializationArray);
			this.m_serializationArray = null;
		}

		// Token: 0x06003986 RID: 14726 RVA: 0x000DC590 File Offset: 0x000DA790
		[__DynamicallyInvokable]
		void ICollection.CopyTo(Array array, int index)
		{
			if (array == null)
			{
				throw new ArgumentNullException("array");
			}
			((ICollection)this.ToList()).CopyTo(array, index);
		}

		// Token: 0x1700089E RID: 2206
		// (get) Token: 0x06003987 RID: 14727 RVA: 0x000DC5AD File Offset: 0x000DA7AD
		[__DynamicallyInvokable]
		bool ICollection.IsSynchronized
		{
			[__DynamicallyInvokable]
			get
			{
				return false;
			}
		}

		// Token: 0x1700089F RID: 2207
		// (get) Token: 0x06003988 RID: 14728 RVA: 0x000DC5B0 File Offset: 0x000DA7B0
		[__DynamicallyInvokable]
		object ICollection.SyncRoot
		{
			[__DynamicallyInvokable]
			get
			{
				throw new NotSupportedException(Environment.GetResourceString("ConcurrentCollection_SyncRoot_NotSupported"));
			}
		}

		// Token: 0x06003989 RID: 14729 RVA: 0x000DC5C1 File Offset: 0x000DA7C1
		[__DynamicallyInvokable]
		IEnumerator IEnumerable.GetEnumerator()
		{
			return ((IEnumerable<T>)this).GetEnumerator();
		}

		// Token: 0x0600398A RID: 14730 RVA: 0x000DC5C9 File Offset: 0x000DA7C9
		[__DynamicallyInvokable]
		bool IProducerConsumerCollection<T>.TryAdd(T item)
		{
			this.Enqueue(item);
			return true;
		}

		// Token: 0x0600398B RID: 14731 RVA: 0x000DC5D3 File Offset: 0x000DA7D3
		[__DynamicallyInvokable]
		bool IProducerConsumerCollection<T>.TryTake(out T item)
		{
			return this.TryDequeue(out item);
		}

		// Token: 0x170008A0 RID: 2208
		// (get) Token: 0x0600398C RID: 14732 RVA: 0x000DC5DC File Offset: 0x000DA7DC
		[__DynamicallyInvokable]
		public bool IsEmpty
		{
			[__DynamicallyInvokable]
			get
			{
				ConcurrentQueue<T>.Segment segment = this.m_head;
				if (!segment.IsEmpty)
				{
					return false;
				}
				if (segment.Next == null)
				{
					return true;
				}
				SpinWait spinWait = default(SpinWait);
				while (segment.IsEmpty)
				{
					if (segment.Next == null)
					{
						return true;
					}
					spinWait.SpinOnce();
					segment = this.m_head;
				}
				return false;
			}
		}

		// Token: 0x0600398D RID: 14733 RVA: 0x000DC633 File Offset: 0x000DA833
		[__DynamicallyInvokable]
		public T[] ToArray()
		{
			return this.ToList().ToArray();
		}

		// Token: 0x0600398E RID: 14734 RVA: 0x000DC640 File Offset: 0x000DA840
		private List<T> ToList()
		{
			Interlocked.Increment(ref this.m_numSnapshotTakers);
			List<T> list = new List<T>();
			try
			{
				ConcurrentQueue<T>.Segment segment;
				ConcurrentQueue<T>.Segment segment2;
				int num;
				int num2;
				this.GetHeadTailPositions(out segment, out segment2, out num, out num2);
				if (segment == segment2)
				{
					segment.AddToList(list, num, num2);
				}
				else
				{
					segment.AddToList(list, num, 31);
					for (ConcurrentQueue<T>.Segment segment3 = segment.Next; segment3 != segment2; segment3 = segment3.Next)
					{
						segment3.AddToList(list, 0, 31);
					}
					segment2.AddToList(list, 0, num2);
				}
			}
			finally
			{
				Interlocked.Decrement(ref this.m_numSnapshotTakers);
			}
			return list;
		}

		// Token: 0x0600398F RID: 14735 RVA: 0x000DC6D4 File Offset: 0x000DA8D4
		private void GetHeadTailPositions(out ConcurrentQueue<T>.Segment head, out ConcurrentQueue<T>.Segment tail, out int headLow, out int tailHigh)
		{
			head = this.m_head;
			tail = this.m_tail;
			headLow = head.Low;
			tailHigh = tail.High;
			SpinWait spinWait = default(SpinWait);
			while (head != this.m_head || tail != this.m_tail || headLow != head.Low || tailHigh != tail.High || head.m_index > tail.m_index)
			{
				spinWait.SpinOnce();
				head = this.m_head;
				tail = this.m_tail;
				headLow = head.Low;
				tailHigh = tail.High;
			}
		}

		// Token: 0x170008A1 RID: 2209
		// (get) Token: 0x06003990 RID: 14736 RVA: 0x000DC780 File Offset: 0x000DA980
		[__DynamicallyInvokable]
		public int Count
		{
			[__DynamicallyInvokable]
			get
			{
				ConcurrentQueue<T>.Segment segment;
				ConcurrentQueue<T>.Segment segment2;
				int num;
				int num2;
				this.GetHeadTailPositions(out segment, out segment2, out num, out num2);
				if (segment == segment2)
				{
					return num2 - num + 1;
				}
				int num3 = 32 - num;
				num3 += 32 * (int)(segment2.m_index - segment.m_index - 1L);
				return num3 + (num2 + 1);
			}
		}

		// Token: 0x06003991 RID: 14737 RVA: 0x000DC7CE File Offset: 0x000DA9CE
		[__DynamicallyInvokable]
		public void CopyTo(T[] array, int index)
		{
			if (array == null)
			{
				throw new ArgumentNullException("array");
			}
			this.ToList().CopyTo(array, index);
		}

		// Token: 0x06003992 RID: 14738 RVA: 0x000DC7EC File Offset: 0x000DA9EC
		[__DynamicallyInvokable]
		public IEnumerator<T> GetEnumerator()
		{
			Interlocked.Increment(ref this.m_numSnapshotTakers);
			ConcurrentQueue<T>.Segment segment;
			ConcurrentQueue<T>.Segment segment2;
			int num;
			int num2;
			this.GetHeadTailPositions(out segment, out segment2, out num, out num2);
			return this.GetEnumerator(segment, segment2, num, num2);
		}

		// Token: 0x06003993 RID: 14739 RVA: 0x000DC81D File Offset: 0x000DAA1D
		private IEnumerator<T> GetEnumerator(ConcurrentQueue<T>.Segment head, ConcurrentQueue<T>.Segment tail, int headLow, int tailHigh)
		{
			try
			{
				SpinWait spin = default(SpinWait);
				if (head == tail)
				{
					int num;
					for (int i = headLow; i <= tailHigh; i = num + 1)
					{
						spin.Reset();
						while (!head.m_state[i].m_value)
						{
							spin.SpinOnce();
						}
						yield return head.m_array[i];
						num = i;
					}
				}
				else
				{
					int num;
					for (int j = headLow; j < 32; j = num + 1)
					{
						spin.Reset();
						while (!head.m_state[j].m_value)
						{
							spin.SpinOnce();
						}
						yield return head.m_array[j];
						num = j;
					}
					ConcurrentQueue<T>.Segment curr;
					for (curr = head.Next; curr != tail; curr = curr.Next)
					{
						for (int k = 0; k < 32; k = num + 1)
						{
							spin.Reset();
							while (!curr.m_state[k].m_value)
							{
								spin.SpinOnce();
							}
							yield return curr.m_array[k];
							num = k;
						}
					}
					for (int l = 0; l <= tailHigh; l = num + 1)
					{
						spin.Reset();
						while (!tail.m_state[l].m_value)
						{
							spin.SpinOnce();
						}
						yield return tail.m_array[l];
						num = l;
					}
					curr = null;
				}
			}
			finally
			{
				Interlocked.Decrement(ref this.m_numSnapshotTakers);
			}
			yield break;
			yield break;
		}

		// Token: 0x06003994 RID: 14740 RVA: 0x000DC84C File Offset: 0x000DAA4C
		[__DynamicallyInvokable]
		public void Enqueue(T item)
		{
			SpinWait spinWait = default(SpinWait);
			for (;;)
			{
				ConcurrentQueue<T>.Segment tail = this.m_tail;
				if (tail.TryAppend(item))
				{
					break;
				}
				spinWait.SpinOnce();
			}
		}

		// Token: 0x06003995 RID: 14741 RVA: 0x000DC87C File Offset: 0x000DAA7C
		[__DynamicallyInvokable]
		public bool TryDequeue(out T result)
		{
			while (!this.IsEmpty)
			{
				ConcurrentQueue<T>.Segment head = this.m_head;
				if (head.TryRemove(out result))
				{
					return true;
				}
			}
			result = default(T);
			return false;
		}

		// Token: 0x06003996 RID: 14742 RVA: 0x000DC8B0 File Offset: 0x000DAAB0
		[__DynamicallyInvokable]
		public bool TryPeek(out T result)
		{
			Interlocked.Increment(ref this.m_numSnapshotTakers);
			while (!this.IsEmpty)
			{
				ConcurrentQueue<T>.Segment head = this.m_head;
				if (head.TryPeek(out result))
				{
					Interlocked.Decrement(ref this.m_numSnapshotTakers);
					return true;
				}
			}
			result = default(T);
			Interlocked.Decrement(ref this.m_numSnapshotTakers);
			return false;
		}

		// Token: 0x04001927 RID: 6439
		[NonSerialized]
		private volatile ConcurrentQueue<T>.Segment m_head;

		// Token: 0x04001928 RID: 6440
		[NonSerialized]
		private volatile ConcurrentQueue<T>.Segment m_tail;

		// Token: 0x04001929 RID: 6441
		private T[] m_serializationArray;

		// Token: 0x0400192A RID: 6442
		private const int SEGMENT_SIZE = 32;

		// Token: 0x0400192B RID: 6443
		[NonSerialized]
		internal volatile int m_numSnapshotTakers;

		// Token: 0x02000BC9 RID: 3017
		private class Segment
		{
			// Token: 0x06006E8F RID: 28303 RVA: 0x0017D3DB File Offset: 0x0017B5DB
			internal Segment(long index, ConcurrentQueue<T> source)
			{
				this.m_array = new T[32];
				this.m_state = new VolatileBool[32];
				this.m_high = -1;
				this.m_index = index;
				this.m_source = source;
			}

			// Token: 0x170012E3 RID: 4835
			// (get) Token: 0x06006E90 RID: 28304 RVA: 0x0017D41A File Offset: 0x0017B61A
			internal ConcurrentQueue<T>.Segment Next
			{
				get
				{
					return this.m_next;
				}
			}

			// Token: 0x170012E4 RID: 4836
			// (get) Token: 0x06006E91 RID: 28305 RVA: 0x0017D424 File Offset: 0x0017B624
			internal bool IsEmpty
			{
				get
				{
					return this.Low > this.High;
				}
			}

			// Token: 0x06006E92 RID: 28306 RVA: 0x0017D434 File Offset: 0x0017B634
			internal void UnsafeAdd(T value)
			{
				this.m_high++;
				this.m_array[this.m_high] = value;
				this.m_state[this.m_high].m_value = true;
			}

			// Token: 0x06006E93 RID: 28307 RVA: 0x0017D488 File Offset: 0x0017B688
			internal ConcurrentQueue<T>.Segment UnsafeGrow()
			{
				ConcurrentQueue<T>.Segment segment = new ConcurrentQueue<T>.Segment(this.m_index + 1L, this.m_source);
				this.m_next = segment;
				return segment;
			}

			// Token: 0x06006E94 RID: 28308 RVA: 0x0017D4B8 File Offset: 0x0017B6B8
			internal void Grow()
			{
				ConcurrentQueue<T>.Segment segment = new ConcurrentQueue<T>.Segment(this.m_index + 1L, this.m_source);
				this.m_next = segment;
				this.m_source.m_tail = this.m_next;
			}

			// Token: 0x06006E95 RID: 28309 RVA: 0x0017D4FC File Offset: 0x0017B6FC
			internal bool TryAppend(T value)
			{
				if (this.m_high >= 31)
				{
					return false;
				}
				int num = 32;
				try
				{
				}
				finally
				{
					num = Interlocked.Increment(ref this.m_high);
					if (num <= 31)
					{
						this.m_array[num] = value;
						this.m_state[num].m_value = true;
					}
					if (num == 31)
					{
						this.Grow();
					}
				}
				return num <= 31;
			}

			// Token: 0x06006E96 RID: 28310 RVA: 0x0017D578 File Offset: 0x0017B778
			internal bool TryRemove(out T result)
			{
				SpinWait spinWait = default(SpinWait);
				int i = this.Low;
				int num = this.High;
				while (i <= num)
				{
					if (Interlocked.CompareExchange(ref this.m_low, i + 1, i) == i)
					{
						SpinWait spinWait2 = default(SpinWait);
						while (!this.m_state[i].m_value)
						{
							spinWait2.SpinOnce();
						}
						result = this.m_array[i];
						if (this.m_source.m_numSnapshotTakers <= 0)
						{
							this.m_array[i] = default(T);
						}
						if (i + 1 >= 32)
						{
							spinWait2 = default(SpinWait);
							while (this.m_next == null)
							{
								spinWait2.SpinOnce();
							}
							this.m_source.m_head = this.m_next;
						}
						return true;
					}
					spinWait.SpinOnce();
					i = this.Low;
					num = this.High;
				}
				result = default(T);
				return false;
			}

			// Token: 0x06006E97 RID: 28311 RVA: 0x0017D67C File Offset: 0x0017B87C
			internal bool TryPeek(out T result)
			{
				result = default(T);
				int low = this.Low;
				if (low > this.High)
				{
					return false;
				}
				SpinWait spinWait = default(SpinWait);
				while (!this.m_state[low].m_value)
				{
					spinWait.SpinOnce();
				}
				result = this.m_array[low];
				return true;
			}

			// Token: 0x06006E98 RID: 28312 RVA: 0x0017D6E0 File Offset: 0x0017B8E0
			internal void AddToList(List<T> list, int start, int end)
			{
				for (int i = start; i <= end; i++)
				{
					SpinWait spinWait = default(SpinWait);
					while (!this.m_state[i].m_value)
					{
						spinWait.SpinOnce();
					}
					list.Add(this.m_array[i]);
				}
			}

			// Token: 0x170012E5 RID: 4837
			// (get) Token: 0x06006E99 RID: 28313 RVA: 0x0017D735 File Offset: 0x0017B935
			internal int Low
			{
				get
				{
					return Math.Min(this.m_low, 32);
				}
			}

			// Token: 0x170012E6 RID: 4838
			// (get) Token: 0x06006E9A RID: 28314 RVA: 0x0017D746 File Offset: 0x0017B946
			internal int High
			{
				get
				{
					return Math.Min(this.m_high, 31);
				}
			}

			// Token: 0x040035B1 RID: 13745
			internal volatile T[] m_array;

			// Token: 0x040035B2 RID: 13746
			internal volatile VolatileBool[] m_state;

			// Token: 0x040035B3 RID: 13747
			private volatile ConcurrentQueue<T>.Segment m_next;

			// Token: 0x040035B4 RID: 13748
			internal readonly long m_index;

			// Token: 0x040035B5 RID: 13749
			private volatile int m_low;

			// Token: 0x040035B6 RID: 13750
			private volatile int m_high;

			// Token: 0x040035B7 RID: 13751
			private volatile ConcurrentQueue<T> m_source;
		}
	}
}
