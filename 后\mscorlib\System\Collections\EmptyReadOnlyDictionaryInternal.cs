﻿using System;

namespace System.Collections
{
	// Token: 0x02000496 RID: 1174
	[Serializable]
	internal sealed class EmptyReadOnlyDictionaryInternal : IDictionary, ICollection, IEnumerable
	{
		// Token: 0x06003859 RID: 14425 RVA: 0x000D8385 File Offset: 0x000D6585
		IEnumerator IEnumerable.GetEnumerator()
		{
			return new EmptyReadOnlyDictionaryInternal.NodeEnumerator();
		}

		// Token: 0x0600385A RID: 14426 RVA: 0x000D838C File Offset: 0x000D658C
		public void CopyTo(Array array, int index)
		{
			if (array == null)
			{
				throw new ArgumentNullException("array");
			}
			if (array.Rank != 1)
			{
				throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
			}
			if (index < 0)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (array.Length - index < this.Count)
			{
				throw new ArgumentException(Environment.GetResourceString("ArgumentOutOfRange_Index"), "index");
			}
		}

		// Token: 0x17000857 RID: 2135
		// (get) Token: 0x0600385B RID: 14427 RVA: 0x000D83FE File Offset: 0x000D65FE
		public int Count
		{
			get
			{
				return 0;
			}
		}

		// Token: 0x17000858 RID: 2136
		// (get) Token: 0x0600385C RID: 14428 RVA: 0x000D8401 File Offset: 0x000D6601
		public object SyncRoot
		{
			get
			{
				return this;
			}
		}

		// Token: 0x17000859 RID: 2137
		// (get) Token: 0x0600385D RID: 14429 RVA: 0x000D8404 File Offset: 0x000D6604
		public bool IsSynchronized
		{
			get
			{
				return false;
			}
		}

		// Token: 0x1700085A RID: 2138
		public object this[object key]
		{
			get
			{
				if (key == null)
				{
					throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
				}
				return null;
			}
			set
			{
				if (key == null)
				{
					throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
				}
				if (!key.GetType().IsSerializable)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_NotSerializable"), "key");
				}
				if (value != null && !value.GetType().IsSerializable)
				{
					throw new ArgumentException(Environment.GetResourceString("Argument_NotSerializable"), "value");
				}
				throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_ReadOnly"));
			}
		}

		// Token: 0x1700085B RID: 2139
		// (get) Token: 0x06003860 RID: 14432 RVA: 0x000D849F File Offset: 0x000D669F
		public ICollection Keys
		{
			get
			{
				return EmptyArray<object>.Value;
			}
		}

		// Token: 0x1700085C RID: 2140
		// (get) Token: 0x06003861 RID: 14433 RVA: 0x000D84A6 File Offset: 0x000D66A6
		public ICollection Values
		{
			get
			{
				return EmptyArray<object>.Value;
			}
		}

		// Token: 0x06003862 RID: 14434 RVA: 0x000D84AD File Offset: 0x000D66AD
		public bool Contains(object key)
		{
			return false;
		}

		// Token: 0x06003863 RID: 14435 RVA: 0x000D84B0 File Offset: 0x000D66B0
		public void Add(object key, object value)
		{
			if (key == null)
			{
				throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
			}
			if (!key.GetType().IsSerializable)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_NotSerializable"), "key");
			}
			if (value != null && !value.GetType().IsSerializable)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_NotSerializable"), "value");
			}
			throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_ReadOnly"));
		}

		// Token: 0x06003864 RID: 14436 RVA: 0x000D852B File Offset: 0x000D672B
		public void Clear()
		{
			throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_ReadOnly"));
		}

		// Token: 0x1700085D RID: 2141
		// (get) Token: 0x06003865 RID: 14437 RVA: 0x000D853C File Offset: 0x000D673C
		public bool IsReadOnly
		{
			get
			{
				return true;
			}
		}

		// Token: 0x1700085E RID: 2142
		// (get) Token: 0x06003866 RID: 14438 RVA: 0x000D853F File Offset: 0x000D673F
		public bool IsFixedSize
		{
			get
			{
				return true;
			}
		}

		// Token: 0x06003867 RID: 14439 RVA: 0x000D8542 File Offset: 0x000D6742
		public IDictionaryEnumerator GetEnumerator()
		{
			return new EmptyReadOnlyDictionaryInternal.NodeEnumerator();
		}

		// Token: 0x06003868 RID: 14440 RVA: 0x000D8549 File Offset: 0x000D6749
		public void Remove(object key)
		{
			throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_ReadOnly"));
		}

		// Token: 0x02000BB7 RID: 2999
		private sealed class NodeEnumerator : IDictionaryEnumerator, IEnumerator
		{
			// Token: 0x06006DFD RID: 28157 RVA: 0x0017BCCB File Offset: 0x00179ECB
			public bool MoveNext()
			{
				return false;
			}

			// Token: 0x170012AC RID: 4780
			// (get) Token: 0x06006DFE RID: 28158 RVA: 0x0017BCCE File Offset: 0x00179ECE
			public object Current
			{
				get
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
				}
			}

			// Token: 0x06006DFF RID: 28159 RVA: 0x0017BCDF File Offset: 0x00179EDF
			public void Reset()
			{
			}

			// Token: 0x170012AD RID: 4781
			// (get) Token: 0x06006E00 RID: 28160 RVA: 0x0017BCE1 File Offset: 0x00179EE1
			public object Key
			{
				get
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
				}
			}

			// Token: 0x170012AE RID: 4782
			// (get) Token: 0x06006E01 RID: 28161 RVA: 0x0017BCF2 File Offset: 0x00179EF2
			public object Value
			{
				get
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
				}
			}

			// Token: 0x170012AF RID: 4783
			// (get) Token: 0x06006E02 RID: 28162 RVA: 0x0017BD03 File Offset: 0x00179F03
			public DictionaryEntry Entry
			{
				get
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
				}
			}
		}
	}
}
