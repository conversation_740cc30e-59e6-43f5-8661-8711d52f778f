﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004D2 RID: 1234
	[__DynamicallyInvokable]
	public interface IDictionary<TKey, TValue> : ICollection<KeyValuePair<TKey, TValue>>, IEnumerable<KeyValuePair<TKey, TValue>>, IEnumerable
	{
		// Token: 0x170008E8 RID: 2280
		[__DynamicallyInvokable]
		TValue this[TKey key]
		{
			[__DynamicallyInvokable]
			get;
			[__DynamicallyInvokable]
			set;
		}

		// Token: 0x170008E9 RID: 2281
		// (get) Token: 0x06003AD7 RID: 15063
		[__DynamicallyInvokable]
		ICollection<TKey> Keys
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x170008EA RID: 2282
		// (get) Token: 0x06003AD8 RID: 15064
		[__DynamicallyInvokable]
		ICollection<TValue> Values
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x06003AD9 RID: 15065
		[__DynamicallyInvokable]
		bool ContainsKey(TKey key);

		// Token: 0x06003ADA RID: 15066
		[__DynamicallyInvokable]
		void Add(TKey key, TValue value);

		// Token: 0x06003ADB RID: 15067
		[__DynamicallyInvokable]
		bool Remove(TKey key);

		// Token: 0x06003ADC RID: 15068
		[__DynamicallyInvokable]
		bool TryGetValue(TKey key, out TValue value);
	}
}
