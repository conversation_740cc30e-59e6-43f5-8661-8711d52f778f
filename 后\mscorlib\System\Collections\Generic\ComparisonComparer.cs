﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004BE RID: 1214
	[Serializable]
	internal class ComparisonComparer<T> : Comparer<T>
	{
		// Token: 0x06003A41 RID: 14913 RVA: 0x000DDFDE File Offset: 0x000DC1DE
		public ComparisonComparer(Comparison<T> comparison)
		{
			this._comparison = comparison;
		}

		// Token: 0x06003A42 RID: 14914 RVA: 0x000DDFED File Offset: 0x000DC1ED
		public override int Compare(T x, T y)
		{
			return this._comparison(x, y);
		}

		// Token: 0x04001942 RID: 6466
		private readonly Comparison<T> _comparison;
	}
}
