﻿using System;
using System.Runtime.InteropServices;
using System.Runtime.Serialization;

namespace System.Collections.Generic
{
	// Token: 0x020004DA RID: 1242
	[ComVisible(true)]
	[__DynamicallyInvokable]
	[Serializable]
	public class KeyNotFoundException : SystemException, ISerializable
	{
		// Token: 0x06003AED RID: 15085 RVA: 0x000DFB48 File Offset: 0x000DDD48
		[__DynamicallyInvokable]
		public KeyNotFoundException()
			: base(Environment.GetResourceString("Arg_KeyNotFound"))
		{
			base.SetErrorCode(-2146232969);
		}

		// Token: 0x06003AEE RID: 15086 RVA: 0x000DFB65 File Offset: 0x000DDD65
		[__DynamicallyInvokable]
		public KeyNotFoundException(string message)
			: base(message)
		{
			base.SetErrorCode(-2146232969);
		}

		// Token: 0x06003AEF RID: 15087 RVA: 0x000DFB79 File Offset: 0x000DDD79
		[__DynamicallyInvokable]
		public KeyNotFoundException(string message, Exception innerException)
			: base(message, innerException)
		{
			base.SetErrorCode(-2146232969);
		}

		// Token: 0x06003AF0 RID: 15088 RVA: 0x000DFB8E File Offset: 0x000DDD8E
		protected KeyNotFoundException(SerializationInfo info, StreamingContext context)
			: base(info, context)
		{
		}
	}
}
