﻿using System;
using System.Diagnostics;

namespace System.Collections.Concurrent
{
	// Token: 0x020004AC RID: 1196
	internal sealed class SystemCollectionsConcurrent_ProducerConsumerCollectionDebugView<T>
	{
		// Token: 0x06003930 RID: 14640 RVA: 0x000DAC84 File Offset: 0x000D8E84
		public SystemCollectionsConcurrent_ProducerConsumerCollectionDebugView(IProducerConsumerCollection<T> collection)
		{
			if (collection == null)
			{
				throw new ArgumentNullException("collection");
			}
			this.m_collection = collection;
		}

		// Token: 0x1700088D RID: 2189
		// (get) Token: 0x06003931 RID: 14641 RVA: 0x000DACA1 File Offset: 0x000D8EA1
		[DebuggerBrowsable(DebuggerBrowsableState.RootHidden)]
		public T[] Items
		{
			get
			{
				return this.m_collection.ToArray();
			}
		}

		// Token: 0x04001914 RID: 6420
		private IProducerConsumerCollection<T> m_collection;
	}
}
