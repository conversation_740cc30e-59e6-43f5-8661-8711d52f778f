﻿using System;
using System.Diagnostics;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Security.Permissions;
using System.Threading;

namespace System.Collections
{
	// Token: 0x020004A4 RID: 1188
	[DebuggerTypeProxy(typeof(SortedList.SortedListDebugView))]
	[DebuggerDisplay("Count = {Count}")]
	[ComVisible(true)]
	[Serializable]
	public class SortedList : IDictionary, ICollection, IEnumerable, ICloneable
	{
		// Token: 0x060038D9 RID: 14553 RVA: 0x000D9DB3 File Offset: 0x000D7FB3
		public SortedList()
		{
			this.Init();
		}

		// Token: 0x060038DA RID: 14554 RVA: 0x000D9DC1 File Offset: 0x000D7FC1
		private void Init()
		{
			this.keys = SortedList.emptyArray;
			this.values = SortedList.emptyArray;
			this._size = 0;
			this.comparer = new Comparer(CultureInfo.CurrentCulture);
		}

		// Token: 0x060038DB RID: 14555 RVA: 0x000D9DF0 File Offset: 0x000D7FF0
		public SortedList(int initialCapacity)
		{
			if (initialCapacity < 0)
			{
				throw new ArgumentOutOfRangeException("initialCapacity", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			this.keys = new object[initialCapacity];
			this.values = new object[initialCapacity];
			this.comparer = new Comparer(CultureInfo.CurrentCulture);
		}

		// Token: 0x060038DC RID: 14556 RVA: 0x000D9E44 File Offset: 0x000D8044
		public SortedList(IComparer comparer)
			: this()
		{
			if (comparer != null)
			{
				this.comparer = comparer;
			}
		}

		// Token: 0x060038DD RID: 14557 RVA: 0x000D9E56 File Offset: 0x000D8056
		public SortedList(IComparer comparer, int capacity)
			: this(comparer)
		{
			this.Capacity = capacity;
		}

		// Token: 0x060038DE RID: 14558 RVA: 0x000D9E66 File Offset: 0x000D8066
		public SortedList(IDictionary d)
			: this(d, null)
		{
		}

		// Token: 0x060038DF RID: 14559 RVA: 0x000D9E70 File Offset: 0x000D8070
		public SortedList(IDictionary d, IComparer comparer)
			: this(comparer, (d != null) ? d.Count : 0)
		{
			if (d == null)
			{
				throw new ArgumentNullException("d", Environment.GetResourceString("ArgumentNull_Dictionary"));
			}
			d.Keys.CopyTo(this.keys, 0);
			d.Values.CopyTo(this.values, 0);
			Array.Sort(this.keys, this.values, comparer);
			this._size = d.Count;
		}

		// Token: 0x060038E0 RID: 14560 RVA: 0x000D9EEC File Offset: 0x000D80EC
		public virtual void Add(object key, object value)
		{
			if (key == null)
			{
				throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
			}
			int num = Array.BinarySearch(this.keys, 0, this._size, key, this.comparer);
			if (num >= 0)
			{
				throw new ArgumentException(Environment.GetResourceString("Argument_AddingDuplicate__", new object[]
				{
					this.GetKey(num),
					key
				}));
			}
			this.Insert(~num, key, value);
		}

		// Token: 0x1700087E RID: 2174
		// (get) Token: 0x060038E1 RID: 14561 RVA: 0x000D9F5D File Offset: 0x000D815D
		// (set) Token: 0x060038E2 RID: 14562 RVA: 0x000D9F68 File Offset: 0x000D8168
		public virtual int Capacity
		{
			get
			{
				return this.keys.Length;
			}
			set
			{
				if (value < this.Count)
				{
					throw new ArgumentOutOfRangeException("value", Environment.GetResourceString("ArgumentOutOfRange_SmallCapacity"));
				}
				if (value != this.keys.Length)
				{
					if (value > 0)
					{
						object[] array = new object[value];
						object[] array2 = new object[value];
						if (this._size > 0)
						{
							Array.Copy(this.keys, 0, array, 0, this._size);
							Array.Copy(this.values, 0, array2, 0, this._size);
						}
						this.keys = array;
						this.values = array2;
						return;
					}
					this.keys = SortedList.emptyArray;
					this.values = SortedList.emptyArray;
				}
			}
		}

		// Token: 0x1700087F RID: 2175
		// (get) Token: 0x060038E3 RID: 14563 RVA: 0x000DA006 File Offset: 0x000D8206
		public virtual int Count
		{
			get
			{
				return this._size;
			}
		}

		// Token: 0x17000880 RID: 2176
		// (get) Token: 0x060038E4 RID: 14564 RVA: 0x000DA00E File Offset: 0x000D820E
		public virtual ICollection Keys
		{
			get
			{
				return this.GetKeyList();
			}
		}

		// Token: 0x17000881 RID: 2177
		// (get) Token: 0x060038E5 RID: 14565 RVA: 0x000DA016 File Offset: 0x000D8216
		public virtual ICollection Values
		{
			get
			{
				return this.GetValueList();
			}
		}

		// Token: 0x17000882 RID: 2178
		// (get) Token: 0x060038E6 RID: 14566 RVA: 0x000DA01E File Offset: 0x000D821E
		public virtual bool IsReadOnly
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000883 RID: 2179
		// (get) Token: 0x060038E7 RID: 14567 RVA: 0x000DA021 File Offset: 0x000D8221
		public virtual bool IsFixedSize
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000884 RID: 2180
		// (get) Token: 0x060038E8 RID: 14568 RVA: 0x000DA024 File Offset: 0x000D8224
		public virtual bool IsSynchronized
		{
			get
			{
				return false;
			}
		}

		// Token: 0x17000885 RID: 2181
		// (get) Token: 0x060038E9 RID: 14569 RVA: 0x000DA027 File Offset: 0x000D8227
		public virtual object SyncRoot
		{
			get
			{
				if (this._syncRoot == null)
				{
					Interlocked.CompareExchange<object>(ref this._syncRoot, new object(), null);
				}
				return this._syncRoot;
			}
		}

		// Token: 0x060038EA RID: 14570 RVA: 0x000DA049 File Offset: 0x000D8249
		public virtual void Clear()
		{
			this.version++;
			Array.Clear(this.keys, 0, this._size);
			Array.Clear(this.values, 0, this._size);
			this._size = 0;
		}

		// Token: 0x060038EB RID: 14571 RVA: 0x000DA084 File Offset: 0x000D8284
		public virtual object Clone()
		{
			SortedList sortedList = new SortedList(this._size);
			Array.Copy(this.keys, 0, sortedList.keys, 0, this._size);
			Array.Copy(this.values, 0, sortedList.values, 0, this._size);
			sortedList._size = this._size;
			sortedList.version = this.version;
			sortedList.comparer = this.comparer;
			return sortedList;
		}

		// Token: 0x060038EC RID: 14572 RVA: 0x000DA0F4 File Offset: 0x000D82F4
		public virtual bool Contains(object key)
		{
			return this.IndexOfKey(key) >= 0;
		}

		// Token: 0x060038ED RID: 14573 RVA: 0x000DA103 File Offset: 0x000D8303
		public virtual bool ContainsKey(object key)
		{
			return this.IndexOfKey(key) >= 0;
		}

		// Token: 0x060038EE RID: 14574 RVA: 0x000DA112 File Offset: 0x000D8312
		public virtual bool ContainsValue(object value)
		{
			return this.IndexOfValue(value) >= 0;
		}

		// Token: 0x060038EF RID: 14575 RVA: 0x000DA124 File Offset: 0x000D8324
		public virtual void CopyTo(Array array, int arrayIndex)
		{
			if (array == null)
			{
				throw new ArgumentNullException("array", Environment.GetResourceString("ArgumentNull_Array"));
			}
			if (array.Rank != 1)
			{
				throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
			}
			if (arrayIndex < 0)
			{
				throw new ArgumentOutOfRangeException("arrayIndex", Environment.GetResourceString("ArgumentOutOfRange_NeedNonNegNum"));
			}
			if (array.Length - arrayIndex < this.Count)
			{
				throw new ArgumentException(Environment.GetResourceString("Arg_ArrayPlusOffTooSmall"));
			}
			for (int i = 0; i < this.Count; i++)
			{
				DictionaryEntry dictionaryEntry = new DictionaryEntry(this.keys[i], this.values[i]);
				array.SetValue(dictionaryEntry, i + arrayIndex);
			}
		}

		// Token: 0x060038F0 RID: 14576 RVA: 0x000DA1D4 File Offset: 0x000D83D4
		internal virtual KeyValuePairs[] ToKeyValuePairsArray()
		{
			KeyValuePairs[] array = new KeyValuePairs[this.Count];
			for (int i = 0; i < this.Count; i++)
			{
				array[i] = new KeyValuePairs(this.keys[i], this.values[i]);
			}
			return array;
		}

		// Token: 0x060038F1 RID: 14577 RVA: 0x000DA218 File Offset: 0x000D8418
		private void EnsureCapacity(int min)
		{
			int num = ((this.keys.Length == 0) ? 16 : (this.keys.Length * 2));
			if (num > 2146435071)
			{
				num = 2146435071;
			}
			if (num < min)
			{
				num = min;
			}
			this.Capacity = num;
		}

		// Token: 0x060038F2 RID: 14578 RVA: 0x000DA258 File Offset: 0x000D8458
		public virtual object GetByIndex(int index)
		{
			if (index < 0 || index >= this.Count)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			return this.values[index];
		}

		// Token: 0x060038F3 RID: 14579 RVA: 0x000DA284 File Offset: 0x000D8484
		IEnumerator IEnumerable.GetEnumerator()
		{
			return new SortedList.SortedListEnumerator(this, 0, this._size, 3);
		}

		// Token: 0x060038F4 RID: 14580 RVA: 0x000DA294 File Offset: 0x000D8494
		public virtual IDictionaryEnumerator GetEnumerator()
		{
			return new SortedList.SortedListEnumerator(this, 0, this._size, 3);
		}

		// Token: 0x060038F5 RID: 14581 RVA: 0x000DA2A4 File Offset: 0x000D84A4
		public virtual object GetKey(int index)
		{
			if (index < 0 || index >= this.Count)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			return this.keys[index];
		}

		// Token: 0x060038F6 RID: 14582 RVA: 0x000DA2D0 File Offset: 0x000D84D0
		public virtual IList GetKeyList()
		{
			if (this.keyList == null)
			{
				this.keyList = new SortedList.KeyList(this);
			}
			return this.keyList;
		}

		// Token: 0x060038F7 RID: 14583 RVA: 0x000DA2EC File Offset: 0x000D84EC
		public virtual IList GetValueList()
		{
			if (this.valueList == null)
			{
				this.valueList = new SortedList.ValueList(this);
			}
			return this.valueList;
		}

		// Token: 0x17000886 RID: 2182
		public virtual object this[object key]
		{
			get
			{
				int num = this.IndexOfKey(key);
				if (num >= 0)
				{
					return this.values[num];
				}
				return null;
			}
			set
			{
				if (key == null)
				{
					throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
				}
				int num = Array.BinarySearch(this.keys, 0, this._size, key, this.comparer);
				if (num >= 0)
				{
					this.values[num] = value;
					this.version++;
					return;
				}
				this.Insert(~num, key, value);
			}
		}

		// Token: 0x060038FA RID: 14586 RVA: 0x000DA394 File Offset: 0x000D8594
		public virtual int IndexOfKey(object key)
		{
			if (key == null)
			{
				throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
			}
			int num = Array.BinarySearch(this.keys, 0, this._size, key, this.comparer);
			if (num < 0)
			{
				return -1;
			}
			return num;
		}

		// Token: 0x060038FB RID: 14587 RVA: 0x000DA3DA File Offset: 0x000D85DA
		public virtual int IndexOfValue(object value)
		{
			return Array.IndexOf<object>(this.values, value, 0, this._size);
		}

		// Token: 0x060038FC RID: 14588 RVA: 0x000DA3F0 File Offset: 0x000D85F0
		private void Insert(int index, object key, object value)
		{
			if (this._size == this.keys.Length)
			{
				this.EnsureCapacity(this._size + 1);
			}
			if (index < this._size)
			{
				Array.Copy(this.keys, index, this.keys, index + 1, this._size - index);
				Array.Copy(this.values, index, this.values, index + 1, this._size - index);
			}
			this.keys[index] = key;
			this.values[index] = value;
			this._size++;
			this.version++;
		}

		// Token: 0x060038FD RID: 14589 RVA: 0x000DA48C File Offset: 0x000D868C
		public virtual void RemoveAt(int index)
		{
			if (index < 0 || index >= this.Count)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			this._size--;
			if (index < this._size)
			{
				Array.Copy(this.keys, index + 1, this.keys, index, this._size - index);
				Array.Copy(this.values, index + 1, this.values, index, this._size - index);
			}
			this.keys[this._size] = null;
			this.values[this._size] = null;
			this.version++;
		}

		// Token: 0x060038FE RID: 14590 RVA: 0x000DA538 File Offset: 0x000D8738
		public virtual void Remove(object key)
		{
			int num = this.IndexOfKey(key);
			if (num >= 0)
			{
				this.RemoveAt(num);
			}
		}

		// Token: 0x060038FF RID: 14591 RVA: 0x000DA558 File Offset: 0x000D8758
		public virtual void SetByIndex(int index, object value)
		{
			if (index < 0 || index >= this.Count)
			{
				throw new ArgumentOutOfRangeException("index", Environment.GetResourceString("ArgumentOutOfRange_Index"));
			}
			this.values[index] = value;
			this.version++;
		}

		// Token: 0x06003900 RID: 14592 RVA: 0x000DA593 File Offset: 0x000D8793
		[HostProtection(SecurityAction.LinkDemand, Synchronization = true)]
		public static SortedList Synchronized(SortedList list)
		{
			if (list == null)
			{
				throw new ArgumentNullException("list");
			}
			return new SortedList.SyncSortedList(list);
		}

		// Token: 0x06003901 RID: 14593 RVA: 0x000DA5A9 File Offset: 0x000D87A9
		public virtual void TrimToSize()
		{
			this.Capacity = this._size;
		}

		// Token: 0x04001905 RID: 6405
		private object[] keys;

		// Token: 0x04001906 RID: 6406
		private object[] values;

		// Token: 0x04001907 RID: 6407
		private int _size;

		// Token: 0x04001908 RID: 6408
		private int version;

		// Token: 0x04001909 RID: 6409
		private IComparer comparer;

		// Token: 0x0400190A RID: 6410
		private SortedList.KeyList keyList;

		// Token: 0x0400190B RID: 6411
		private SortedList.ValueList valueList;

		// Token: 0x0400190C RID: 6412
		[NonSerialized]
		private object _syncRoot;

		// Token: 0x0400190D RID: 6413
		private const int _defaultCapacity = 16;

		// Token: 0x0400190E RID: 6414
		private static object[] emptyArray = EmptyArray<object>.Value;

		// Token: 0x02000BBE RID: 3006
		[Serializable]
		private class SyncSortedList : SortedList
		{
			// Token: 0x06006E31 RID: 28209 RVA: 0x0017C504 File Offset: 0x0017A704
			internal SyncSortedList(SortedList list)
			{
				this._list = list;
				this._root = list.SyncRoot;
			}

			// Token: 0x170012C3 RID: 4803
			// (get) Token: 0x06006E32 RID: 28210 RVA: 0x0017C520 File Offset: 0x0017A720
			public override int Count
			{
				get
				{
					object root = this._root;
					int count;
					lock (root)
					{
						count = this._list.Count;
					}
					return count;
				}
			}

			// Token: 0x170012C4 RID: 4804
			// (get) Token: 0x06006E33 RID: 28211 RVA: 0x0017C568 File Offset: 0x0017A768
			public override object SyncRoot
			{
				get
				{
					return this._root;
				}
			}

			// Token: 0x170012C5 RID: 4805
			// (get) Token: 0x06006E34 RID: 28212 RVA: 0x0017C570 File Offset: 0x0017A770
			public override bool IsReadOnly
			{
				get
				{
					return this._list.IsReadOnly;
				}
			}

			// Token: 0x170012C6 RID: 4806
			// (get) Token: 0x06006E35 RID: 28213 RVA: 0x0017C57D File Offset: 0x0017A77D
			public override bool IsFixedSize
			{
				get
				{
					return this._list.IsFixedSize;
				}
			}

			// Token: 0x170012C7 RID: 4807
			// (get) Token: 0x06006E36 RID: 28214 RVA: 0x0017C58A File Offset: 0x0017A78A
			public override bool IsSynchronized
			{
				get
				{
					return true;
				}
			}

			// Token: 0x170012C8 RID: 4808
			public override object this[object key]
			{
				get
				{
					object root = this._root;
					object obj;
					lock (root)
					{
						obj = this._list[key];
					}
					return obj;
				}
				set
				{
					object root = this._root;
					lock (root)
					{
						this._list[key] = value;
					}
				}
			}

			// Token: 0x06006E39 RID: 28217 RVA: 0x0017C620 File Offset: 0x0017A820
			public override void Add(object key, object value)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Add(key, value);
				}
			}

			// Token: 0x170012C9 RID: 4809
			// (get) Token: 0x06006E3A RID: 28218 RVA: 0x0017C668 File Offset: 0x0017A868
			public override int Capacity
			{
				get
				{
					object root = this._root;
					int capacity;
					lock (root)
					{
						capacity = this._list.Capacity;
					}
					return capacity;
				}
			}

			// Token: 0x06006E3B RID: 28219 RVA: 0x0017C6B0 File Offset: 0x0017A8B0
			public override void Clear()
			{
				object root = this._root;
				lock (root)
				{
					this._list.Clear();
				}
			}

			// Token: 0x06006E3C RID: 28220 RVA: 0x0017C6F8 File Offset: 0x0017A8F8
			public override object Clone()
			{
				object root = this._root;
				object obj;
				lock (root)
				{
					obj = this._list.Clone();
				}
				return obj;
			}

			// Token: 0x06006E3D RID: 28221 RVA: 0x0017C740 File Offset: 0x0017A940
			public override bool Contains(object key)
			{
				object root = this._root;
				bool flag2;
				lock (root)
				{
					flag2 = this._list.Contains(key);
				}
				return flag2;
			}

			// Token: 0x06006E3E RID: 28222 RVA: 0x0017C788 File Offset: 0x0017A988
			public override bool ContainsKey(object key)
			{
				object root = this._root;
				bool flag2;
				lock (root)
				{
					flag2 = this._list.ContainsKey(key);
				}
				return flag2;
			}

			// Token: 0x06006E3F RID: 28223 RVA: 0x0017C7D0 File Offset: 0x0017A9D0
			public override bool ContainsValue(object key)
			{
				object root = this._root;
				bool flag2;
				lock (root)
				{
					flag2 = this._list.ContainsValue(key);
				}
				return flag2;
			}

			// Token: 0x06006E40 RID: 28224 RVA: 0x0017C818 File Offset: 0x0017AA18
			public override void CopyTo(Array array, int index)
			{
				object root = this._root;
				lock (root)
				{
					this._list.CopyTo(array, index);
				}
			}

			// Token: 0x06006E41 RID: 28225 RVA: 0x0017C860 File Offset: 0x0017AA60
			public override object GetByIndex(int index)
			{
				object root = this._root;
				object byIndex;
				lock (root)
				{
					byIndex = this._list.GetByIndex(index);
				}
				return byIndex;
			}

			// Token: 0x06006E42 RID: 28226 RVA: 0x0017C8A8 File Offset: 0x0017AAA8
			public override IDictionaryEnumerator GetEnumerator()
			{
				object root = this._root;
				IDictionaryEnumerator enumerator;
				lock (root)
				{
					enumerator = this._list.GetEnumerator();
				}
				return enumerator;
			}

			// Token: 0x06006E43 RID: 28227 RVA: 0x0017C8F0 File Offset: 0x0017AAF0
			public override object GetKey(int index)
			{
				object root = this._root;
				object key;
				lock (root)
				{
					key = this._list.GetKey(index);
				}
				return key;
			}

			// Token: 0x06006E44 RID: 28228 RVA: 0x0017C938 File Offset: 0x0017AB38
			public override IList GetKeyList()
			{
				object root = this._root;
				IList keyList;
				lock (root)
				{
					keyList = this._list.GetKeyList();
				}
				return keyList;
			}

			// Token: 0x06006E45 RID: 28229 RVA: 0x0017C980 File Offset: 0x0017AB80
			public override IList GetValueList()
			{
				object root = this._root;
				IList valueList;
				lock (root)
				{
					valueList = this._list.GetValueList();
				}
				return valueList;
			}

			// Token: 0x06006E46 RID: 28230 RVA: 0x0017C9C8 File Offset: 0x0017ABC8
			public override int IndexOfKey(object key)
			{
				if (key == null)
				{
					throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
				}
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.IndexOfKey(key);
				}
				return num;
			}

			// Token: 0x06006E47 RID: 28231 RVA: 0x0017CA28 File Offset: 0x0017AC28
			public override int IndexOfValue(object value)
			{
				object root = this._root;
				int num;
				lock (root)
				{
					num = this._list.IndexOfValue(value);
				}
				return num;
			}

			// Token: 0x06006E48 RID: 28232 RVA: 0x0017CA70 File Offset: 0x0017AC70
			public override void RemoveAt(int index)
			{
				object root = this._root;
				lock (root)
				{
					this._list.RemoveAt(index);
				}
			}

			// Token: 0x06006E49 RID: 28233 RVA: 0x0017CAB8 File Offset: 0x0017ACB8
			public override void Remove(object key)
			{
				object root = this._root;
				lock (root)
				{
					this._list.Remove(key);
				}
			}

			// Token: 0x06006E4A RID: 28234 RVA: 0x0017CB00 File Offset: 0x0017AD00
			public override void SetByIndex(int index, object value)
			{
				object root = this._root;
				lock (root)
				{
					this._list.SetByIndex(index, value);
				}
			}

			// Token: 0x06006E4B RID: 28235 RVA: 0x0017CB48 File Offset: 0x0017AD48
			internal override KeyValuePairs[] ToKeyValuePairsArray()
			{
				return this._list.ToKeyValuePairsArray();
			}

			// Token: 0x06006E4C RID: 28236 RVA: 0x0017CB58 File Offset: 0x0017AD58
			public override void TrimToSize()
			{
				object root = this._root;
				lock (root)
				{
					this._list.TrimToSize();
				}
			}

			// Token: 0x0400358B RID: 13707
			private SortedList _list;

			// Token: 0x0400358C RID: 13708
			private object _root;
		}

		// Token: 0x02000BBF RID: 3007
		[Serializable]
		private class SortedListEnumerator : IDictionaryEnumerator, IEnumerator, ICloneable
		{
			// Token: 0x06006E4D RID: 28237 RVA: 0x0017CBA0 File Offset: 0x0017ADA0
			internal SortedListEnumerator(SortedList sortedList, int index, int count, int getObjRetType)
			{
				this.sortedList = sortedList;
				this.index = index;
				this.startIndex = index;
				this.endIndex = index + count;
				this.version = sortedList.version;
				this.getObjectRetType = getObjRetType;
				this.current = false;
			}

			// Token: 0x06006E4E RID: 28238 RVA: 0x0017CBEC File Offset: 0x0017ADEC
			public object Clone()
			{
				return base.MemberwiseClone();
			}

			// Token: 0x170012CA RID: 4810
			// (get) Token: 0x06006E4F RID: 28239 RVA: 0x0017CBF4 File Offset: 0x0017ADF4
			public virtual object Key
			{
				get
				{
					if (this.version != this.sortedList.version)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
					}
					if (!this.current)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
					}
					return this.key;
				}
			}

			// Token: 0x06006E50 RID: 28240 RVA: 0x0017CC44 File Offset: 0x0017AE44
			public virtual bool MoveNext()
			{
				if (this.version != this.sortedList.version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
				}
				if (this.index < this.endIndex)
				{
					this.key = this.sortedList.keys[this.index];
					this.value = this.sortedList.values[this.index];
					this.index++;
					this.current = true;
					return true;
				}
				this.key = null;
				this.value = null;
				this.current = false;
				return false;
			}

			// Token: 0x170012CB RID: 4811
			// (get) Token: 0x06006E51 RID: 28241 RVA: 0x0017CCE0 File Offset: 0x0017AEE0
			public virtual DictionaryEntry Entry
			{
				get
				{
					if (this.version != this.sortedList.version)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
					}
					if (!this.current)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
					}
					return new DictionaryEntry(this.key, this.value);
				}
			}

			// Token: 0x170012CC RID: 4812
			// (get) Token: 0x06006E52 RID: 28242 RVA: 0x0017CD3C File Offset: 0x0017AF3C
			public virtual object Current
			{
				get
				{
					if (!this.current)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
					}
					if (this.getObjectRetType == 1)
					{
						return this.key;
					}
					if (this.getObjectRetType == 2)
					{
						return this.value;
					}
					return new DictionaryEntry(this.key, this.value);
				}
			}

			// Token: 0x170012CD RID: 4813
			// (get) Token: 0x06006E53 RID: 28243 RVA: 0x0017CD98 File Offset: 0x0017AF98
			public virtual object Value
			{
				get
				{
					if (this.version != this.sortedList.version)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
					}
					if (!this.current)
					{
						throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumOpCantHappen"));
					}
					return this.value;
				}
			}

			// Token: 0x06006E54 RID: 28244 RVA: 0x0017CDE8 File Offset: 0x0017AFE8
			public virtual void Reset()
			{
				if (this.version != this.sortedList.version)
				{
					throw new InvalidOperationException(Environment.GetResourceString("InvalidOperation_EnumFailedVersion"));
				}
				this.index = this.startIndex;
				this.current = false;
				this.key = null;
				this.value = null;
			}

			// Token: 0x0400358D RID: 13709
			private SortedList sortedList;

			// Token: 0x0400358E RID: 13710
			private object key;

			// Token: 0x0400358F RID: 13711
			private object value;

			// Token: 0x04003590 RID: 13712
			private int index;

			// Token: 0x04003591 RID: 13713
			private int startIndex;

			// Token: 0x04003592 RID: 13714
			private int endIndex;

			// Token: 0x04003593 RID: 13715
			private int version;

			// Token: 0x04003594 RID: 13716
			private bool current;

			// Token: 0x04003595 RID: 13717
			private int getObjectRetType;

			// Token: 0x04003596 RID: 13718
			internal const int Keys = 1;

			// Token: 0x04003597 RID: 13719
			internal const int Values = 2;

			// Token: 0x04003598 RID: 13720
			internal const int DictEntry = 3;
		}

		// Token: 0x02000BC0 RID: 3008
		[Serializable]
		private class KeyList : IList, ICollection, IEnumerable
		{
			// Token: 0x06006E55 RID: 28245 RVA: 0x0017CE39 File Offset: 0x0017B039
			internal KeyList(SortedList sortedList)
			{
				this.sortedList = sortedList;
			}

			// Token: 0x170012CE RID: 4814
			// (get) Token: 0x06006E56 RID: 28246 RVA: 0x0017CE48 File Offset: 0x0017B048
			public virtual int Count
			{
				get
				{
					return this.sortedList._size;
				}
			}

			// Token: 0x170012CF RID: 4815
			// (get) Token: 0x06006E57 RID: 28247 RVA: 0x0017CE55 File Offset: 0x0017B055
			public virtual bool IsReadOnly
			{
				get
				{
					return true;
				}
			}

			// Token: 0x170012D0 RID: 4816
			// (get) Token: 0x06006E58 RID: 28248 RVA: 0x0017CE58 File Offset: 0x0017B058
			public virtual bool IsFixedSize
			{
				get
				{
					return true;
				}
			}

			// Token: 0x170012D1 RID: 4817
			// (get) Token: 0x06006E59 RID: 28249 RVA: 0x0017CE5B File Offset: 0x0017B05B
			public virtual bool IsSynchronized
			{
				get
				{
					return this.sortedList.IsSynchronized;
				}
			}

			// Token: 0x170012D2 RID: 4818
			// (get) Token: 0x06006E5A RID: 28250 RVA: 0x0017CE68 File Offset: 0x0017B068
			public virtual object SyncRoot
			{
				get
				{
					return this.sortedList.SyncRoot;
				}
			}

			// Token: 0x06006E5B RID: 28251 RVA: 0x0017CE75 File Offset: 0x0017B075
			public virtual int Add(object key)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x06006E5C RID: 28252 RVA: 0x0017CE86 File Offset: 0x0017B086
			public virtual void Clear()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x06006E5D RID: 28253 RVA: 0x0017CE97 File Offset: 0x0017B097
			public virtual bool Contains(object key)
			{
				return this.sortedList.Contains(key);
			}

			// Token: 0x06006E5E RID: 28254 RVA: 0x0017CEA5 File Offset: 0x0017B0A5
			public virtual void CopyTo(Array array, int arrayIndex)
			{
				if (array != null && array.Rank != 1)
				{
					throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
				}
				Array.Copy(this.sortedList.keys, 0, array, arrayIndex, this.sortedList.Count);
			}

			// Token: 0x06006E5F RID: 28255 RVA: 0x0017CEE1 File Offset: 0x0017B0E1
			public virtual void Insert(int index, object value)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x170012D3 RID: 4819
			public virtual object this[int index]
			{
				get
				{
					return this.sortedList.GetKey(index);
				}
				set
				{
					throw new NotSupportedException(Environment.GetResourceString("NotSupported_KeyCollectionSet"));
				}
			}

			// Token: 0x06006E62 RID: 28258 RVA: 0x0017CF11 File Offset: 0x0017B111
			public virtual IEnumerator GetEnumerator()
			{
				return new SortedList.SortedListEnumerator(this.sortedList, 0, this.sortedList.Count, 1);
			}

			// Token: 0x06006E63 RID: 28259 RVA: 0x0017CF2C File Offset: 0x0017B12C
			public virtual int IndexOf(object key)
			{
				if (key == null)
				{
					throw new ArgumentNullException("key", Environment.GetResourceString("ArgumentNull_Key"));
				}
				int num = Array.BinarySearch(this.sortedList.keys, 0, this.sortedList.Count, key, this.sortedList.comparer);
				if (num >= 0)
				{
					return num;
				}
				return -1;
			}

			// Token: 0x06006E64 RID: 28260 RVA: 0x0017CF81 File Offset: 0x0017B181
			public virtual void Remove(object key)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x06006E65 RID: 28261 RVA: 0x0017CF92 File Offset: 0x0017B192
			public virtual void RemoveAt(int index)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x04003599 RID: 13721
			private SortedList sortedList;
		}

		// Token: 0x02000BC1 RID: 3009
		[Serializable]
		private class ValueList : IList, ICollection, IEnumerable
		{
			// Token: 0x06006E66 RID: 28262 RVA: 0x0017CFA3 File Offset: 0x0017B1A3
			internal ValueList(SortedList sortedList)
			{
				this.sortedList = sortedList;
			}

			// Token: 0x170012D4 RID: 4820
			// (get) Token: 0x06006E67 RID: 28263 RVA: 0x0017CFB2 File Offset: 0x0017B1B2
			public virtual int Count
			{
				get
				{
					return this.sortedList._size;
				}
			}

			// Token: 0x170012D5 RID: 4821
			// (get) Token: 0x06006E68 RID: 28264 RVA: 0x0017CFBF File Offset: 0x0017B1BF
			public virtual bool IsReadOnly
			{
				get
				{
					return true;
				}
			}

			// Token: 0x170012D6 RID: 4822
			// (get) Token: 0x06006E69 RID: 28265 RVA: 0x0017CFC2 File Offset: 0x0017B1C2
			public virtual bool IsFixedSize
			{
				get
				{
					return true;
				}
			}

			// Token: 0x170012D7 RID: 4823
			// (get) Token: 0x06006E6A RID: 28266 RVA: 0x0017CFC5 File Offset: 0x0017B1C5
			public virtual bool IsSynchronized
			{
				get
				{
					return this.sortedList.IsSynchronized;
				}
			}

			// Token: 0x170012D8 RID: 4824
			// (get) Token: 0x06006E6B RID: 28267 RVA: 0x0017CFD2 File Offset: 0x0017B1D2
			public virtual object SyncRoot
			{
				get
				{
					return this.sortedList.SyncRoot;
				}
			}

			// Token: 0x06006E6C RID: 28268 RVA: 0x0017CFDF File Offset: 0x0017B1DF
			public virtual int Add(object key)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x06006E6D RID: 28269 RVA: 0x0017CFF0 File Offset: 0x0017B1F0
			public virtual void Clear()
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x06006E6E RID: 28270 RVA: 0x0017D001 File Offset: 0x0017B201
			public virtual bool Contains(object value)
			{
				return this.sortedList.ContainsValue(value);
			}

			// Token: 0x06006E6F RID: 28271 RVA: 0x0017D00F File Offset: 0x0017B20F
			public virtual void CopyTo(Array array, int arrayIndex)
			{
				if (array != null && array.Rank != 1)
				{
					throw new ArgumentException(Environment.GetResourceString("Arg_RankMultiDimNotSupported"));
				}
				Array.Copy(this.sortedList.values, 0, array, arrayIndex, this.sortedList.Count);
			}

			// Token: 0x06006E70 RID: 28272 RVA: 0x0017D04B File Offset: 0x0017B24B
			public virtual void Insert(int index, object value)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x170012D9 RID: 4825
			public virtual object this[int index]
			{
				get
				{
					return this.sortedList.GetByIndex(index);
				}
				set
				{
					throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
				}
			}

			// Token: 0x06006E73 RID: 28275 RVA: 0x0017D07B File Offset: 0x0017B27B
			public virtual IEnumerator GetEnumerator()
			{
				return new SortedList.SortedListEnumerator(this.sortedList, 0, this.sortedList.Count, 2);
			}

			// Token: 0x06006E74 RID: 28276 RVA: 0x0017D095 File Offset: 0x0017B295
			public virtual int IndexOf(object value)
			{
				return Array.IndexOf<object>(this.sortedList.values, value, 0, this.sortedList.Count);
			}

			// Token: 0x06006E75 RID: 28277 RVA: 0x0017D0B4 File Offset: 0x0017B2B4
			public virtual void Remove(object value)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x06006E76 RID: 28278 RVA: 0x0017D0C5 File Offset: 0x0017B2C5
			public virtual void RemoveAt(int index)
			{
				throw new NotSupportedException(Environment.GetResourceString("NotSupported_SortedListNestedWrite"));
			}

			// Token: 0x0400359A RID: 13722
			private SortedList sortedList;
		}

		// Token: 0x02000BC2 RID: 3010
		internal class SortedListDebugView
		{
			// Token: 0x06006E77 RID: 28279 RVA: 0x0017D0D6 File Offset: 0x0017B2D6
			public SortedListDebugView(SortedList sortedList)
			{
				if (sortedList == null)
				{
					throw new ArgumentNullException("sortedList");
				}
				this.sortedList = sortedList;
			}

			// Token: 0x170012DA RID: 4826
			// (get) Token: 0x06006E78 RID: 28280 RVA: 0x0017D0F3 File Offset: 0x0017B2F3
			[DebuggerBrowsable(DebuggerBrowsableState.RootHidden)]
			public KeyValuePairs[] Items
			{
				get
				{
					return this.sortedList.ToKeyValuePairsArray();
				}
			}

			// Token: 0x0400359B RID: 13723
			private SortedList sortedList;
		}
	}
}
