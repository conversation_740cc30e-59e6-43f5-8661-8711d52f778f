﻿using System;
using System.Collections.Generic;
using System.Security.Permissions;

namespace System.Collections.Concurrent
{
	// Token: 0x020004B1 RID: 1201
	[__DynamicallyInvokable]
	[HostProtection(SecurityAction.LinkDemand, Synchronization = true, ExternalThreading = true)]
	public abstract class Partitioner<TSource>
	{
		// Token: 0x06003998 RID: 14744
		[__DynamicallyInvokable]
		public abstract IList<IEnumerator<TSource>> GetPartitions(int partitionCount);

		// Token: 0x170008A2 RID: 2210
		// (get) Token: 0x06003999 RID: 14745 RVA: 0x000DC912 File Offset: 0x000DAB12
		[__DynamicallyInvokable]
		public virtual bool SupportsDynamicPartitions
		{
			[__DynamicallyInvokable]
			get
			{
				return false;
			}
		}

		// Token: 0x0600399A RID: 14746 RVA: 0x000DC915 File Offset: 0x000DAB15
		[__DynamicallyInvokable]
		public virtual IEnumerable<TSource> GetDynamicPartitions()
		{
			throw new NotSupportedException(Environment.GetResourceString("Partitioner_DynamicPartitionsNotSupported"));
		}

		// Token: 0x0600399B RID: 14747 RVA: 0x000DC926 File Offset: 0x000DAB26
		[__DynamicallyInvokable]
		protected Partitioner()
		{
		}
	}
}
