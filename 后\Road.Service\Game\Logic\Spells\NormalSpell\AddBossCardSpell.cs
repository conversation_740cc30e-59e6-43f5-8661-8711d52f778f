using System;
using Game.Logic.Phy.Object;
using SqlDataProvider.Data;

namespace Game.Logic.Spells.NormalSpell
{
	// Token: 0x02000CB7 RID: 3255
	[SpellAttibute(119)]
	public class AddBossCardSpell : ISpellHandler
	{
		// Token: 0x060074E6 RID: 29926 RVA: 0x0026EB34 File Offset: 0x0026CD34
		public void Execute(BaseGame game, Player player, ItemTemplateInfo item)
		{
			// 增加玩家的BossCardCount
			int addCount = item.Property2; // 从物品的Property2获取增加的次数
			if (addCount <= 0)
			{
				addCount = 1; // 默认增加1次
			}
			
			player.BossCardCount += addCount;
			
			// 发送消息给玩家
			string message = string.Format("使用翻牌赠送卡，增加了{0}次BOSS翻牌机会！", addCount);
			game.SendMessage(player.PlayerDetail, message, message, 1);
		}
	}
}
