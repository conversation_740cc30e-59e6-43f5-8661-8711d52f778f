﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004C2 RID: 1218
	[Serializable]
	internal class NullableEqualityComparer<T> : EqualityComparer<T?> where T : struct, IEquatable<T>
	{
		// Token: 0x06003A8A RID: 14986 RVA: 0x000DF2C2 File Offset: 0x000DD4C2
		public override bool Equals(T? x, T? y)
		{
			if (x != null)
			{
				return y != null && x.value.Equals(y.value);
			}
			return y == null;
		}

		// Token: 0x06003A8B RID: 14987 RVA: 0x000DF2FD File Offset: 0x000DD4FD
		public override int GetHashCode(T? obj)
		{
			return obj.GetHashCode();
		}

		// Token: 0x06003A8C RID: 14988 RVA: 0x000DF30C File Offset: 0x000DD50C
		internal override int IndexOf(T?[] array, T? value, int startIndex, int count)
		{
			int num = startIndex + count;
			if (value == null)
			{
				for (int i = startIndex; i < num; i++)
				{
					if (array[i] == null)
					{
						return i;
					}
				}
			}
			else
			{
				for (int j = startIndex; j < num; j++)
				{
					if (array[j] != null && array[j].value.Equals(value.value))
					{
						return j;
					}
				}
			}
			return -1;
		}

		// Token: 0x06003A8D RID: 14989 RVA: 0x000DF384 File Offset: 0x000DD584
		internal override int LastIndexOf(T?[] array, T? value, int startIndex, int count)
		{
			int num = startIndex - count + 1;
			if (value == null)
			{
				for (int i = startIndex; i >= num; i--)
				{
					if (array[i] == null)
					{
						return i;
					}
				}
			}
			else
			{
				for (int j = startIndex; j >= num; j--)
				{
					if (array[j] != null && array[j].value.Equals(value.value))
					{
						return j;
					}
				}
			}
			return -1;
		}

		// Token: 0x06003A8E RID: 14990 RVA: 0x000DF3FC File Offset: 0x000DD5FC
		public override bool Equals(object obj)
		{
			NullableEqualityComparer<T> nullableEqualityComparer = obj as NullableEqualityComparer<T>;
			return nullableEqualityComparer != null;
		}

		// Token: 0x06003A8F RID: 14991 RVA: 0x000DF414 File Offset: 0x000DD614
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}
	}
}
