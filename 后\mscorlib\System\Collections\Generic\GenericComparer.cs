﻿using System;

namespace System.Collections.Generic
{
	// Token: 0x020004BB RID: 1211
	[Serializable]
	internal class GenericComparer<T> : Comparer<T> where T : IComparable<T>
	{
		// Token: 0x06003A35 RID: 14901 RVA: 0x000DDEBF File Offset: 0x000DC0BF
		public override int Compare(T x, T y)
		{
			if (x != null)
			{
				if (y != null)
				{
					return x.CompareTo(y);
				}
				return 1;
			}
			else
			{
				if (y != null)
				{
					return -1;
				}
				return 0;
			}
		}

		// Token: 0x06003A36 RID: 14902 RVA: 0x000DDEF0 File Offset: 0x000DC0F0
		public override bool Equals(object obj)
		{
			GenericComparer<T> genericComparer = obj as GenericComparer<T>;
			return genericComparer != null;
		}

		// Token: 0x06003A37 RID: 14903 RVA: 0x000DDF08 File Offset: 0x000DC108
		public override int GetHashCode()
		{
			return base.GetType().Name.GetHashCode();
		}
	}
}
