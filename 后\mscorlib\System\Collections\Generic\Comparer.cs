﻿using System;
using System.Runtime.CompilerServices;
using System.Security;

namespace System.Collections.Generic
{
	// Token: 0x020004BA RID: 1210
	[TypeDependency("System.Collections.Generic.ObjectComparer`1")]
	[__DynamicallyInvokable]
	[Serializable]
	public abstract class Comparer<T> : I<PERSON><PERSON><PERSON><PERSON>, IComparer<T>
	{
		// Token: 0x170008CE RID: 2254
		// (get) Token: 0x06003A2E RID: 14894 RVA: 0x000DDD9A File Offset: 0x000DBF9A
		[__DynamicallyInvokable]
		public static Comparer<T> Default
		{
			[__DynamicallyInvokable]
			get
			{
				return Comparer<T>.defaultComparer;
			}
		}

		// Token: 0x06003A2F RID: 14895 RVA: 0x000DDDA1 File Offset: 0x000DBFA1
		[__DynamicallyInvokable]
		public static Comparer<T> Create(Comparison<T> comparison)
		{
			if (comparison == null)
			{
				throw new ArgumentNullException("comparison");
			}
			return new ComparisonComparer<T>(comparison);
		}

		// Token: 0x06003A30 RID: 14896 RVA: 0x000DDDB8 File Offset: 0x000DBFB8
		[SecuritySafeCritical]
		private static Comparer<T> CreateComparer()
		{
			RuntimeType runtimeType = (RuntimeType)typeof(T);
			if (typeof(IComparable<T>).IsAssignableFrom(runtimeType))
			{
				return (Comparer<T>)RuntimeTypeHandle.CreateInstanceForAnotherGenericParameter((RuntimeType)typeof(GenericComparer<int>), runtimeType);
			}
			if (runtimeType.IsGenericType && runtimeType.GetGenericTypeDefinition() == typeof(Nullable<>))
			{
				RuntimeType runtimeType2 = (RuntimeType)runtimeType.GetGenericArguments()[0];
				if (typeof(IComparable<>).MakeGenericType(new Type[] { runtimeType2 }).IsAssignableFrom(runtimeType2))
				{
					return (Comparer<T>)RuntimeTypeHandle.CreateInstanceForAnotherGenericParameter((RuntimeType)typeof(NullableComparer<int>), runtimeType2);
				}
			}
			return new ObjectComparer<T>();
		}

		// Token: 0x06003A31 RID: 14897
		[__DynamicallyInvokable]
		public abstract int Compare(T x, T y);

		// Token: 0x06003A32 RID: 14898 RVA: 0x000DDE70 File Offset: 0x000DC070
		[__DynamicallyInvokable]
		int IComparer.Compare(object x, object y)
		{
			if (x == null)
			{
				if (y != null)
				{
					return -1;
				}
				return 0;
			}
			else
			{
				if (y == null)
				{
					return 1;
				}
				if (x is T && y is T)
				{
					return this.Compare((T)((object)x), (T)((object)y));
				}
				ThrowHelper.ThrowArgumentException(ExceptionResource.Argument_InvalidArgumentForComparison);
				return 0;
			}
		}

		// Token: 0x06003A33 RID: 14899 RVA: 0x000DDEAB File Offset: 0x000DC0AB
		[__DynamicallyInvokable]
		protected Comparer()
		{
		}

		// Token: 0x04001941 RID: 6465
		private static readonly Comparer<T> defaultComparer = Comparer<T>.CreateComparer();
	}
}
