﻿using System;
using System.Diagnostics;

namespace System.Collections.Generic
{
	// Token: 0x020004CD RID: 1229
	internal sealed class Mscorlib_DictionaryValueCollectionDebugView<TKey, TValue>
	{
		// Token: 0x06003AC7 RID: 15047 RVA: 0x000DFA74 File Offset: 0x000DDC74
		public Mscorlib_DictionaryValueCollectionDebugView(ICollection<TValue> collection)
		{
			if (collection == null)
			{
				ThrowHelper.ThrowArgumentNullException(ExceptionArgument.collection);
			}
			this.collection = collection;
		}

		// Token: 0x170008E3 RID: 2275
		// (get) Token: 0x06003AC8 RID: 15048 RVA: 0x000DFA8C File Offset: 0x000DDC8C
		[DebuggerBrowsable(DebuggerBrowsableState.RootHidden)]
		public TValue[] Items
		{
			get
			{
				TValue[] array = new TValue[this.collection.Count];
				this.collection.CopyTo(array, 0);
				return array;
			}
		}

		// Token: 0x04001956 RID: 6486
		private ICollection<TValue> collection;
	}
}
