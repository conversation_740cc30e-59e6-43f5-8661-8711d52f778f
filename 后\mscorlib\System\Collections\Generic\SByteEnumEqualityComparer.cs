﻿using System;
using System.Runtime.CompilerServices;
using System.Runtime.Serialization;

namespace System.Collections.Generic
{
	// Token: 0x020004C6 RID: 1222
	[Serializable]
	internal sealed class SByteEnumEqualityComparer<T> : EnumEqualityComparer<T>, ISerializable where T : struct
	{
		// Token: 0x06003AA6 RID: 15014 RVA: 0x000DF73A File Offset: 0x000DD93A
		public SByteEnumEqualityComparer()
		{
		}

		// Token: 0x06003AA7 RID: 15015 RVA: 0x000DF742 File Offset: 0x000DD942
		public SByteEnumEqualityComparer(SerializationInfo information, StreamingContext context)
		{
		}

		// Token: 0x06003AA8 RID: 15016 RVA: 0x000DF74C File Offset: 0x000DD94C
		public override int GetHashCode(T obj)
		{
			int num = JitHelpers.UnsafeEnumCast<T>(obj);
			return ((sbyte)num).GetHashCode();
		}
	}
}
