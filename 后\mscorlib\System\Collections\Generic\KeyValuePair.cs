﻿using System;
using System.Text;

namespace System.Collections.Generic
{
	// Token: 0x020004DB RID: 1243
	[__DynamicallyInvokable]
	[Serializable]
	public struct KeyValuePair<TKey, TValue>
	{
		// Token: 0x06003AF1 RID: 15089 RVA: 0x000DFB98 File Offset: 0x000DDD98
		[__DynamicallyInvokable]
		public KeyValuePair(TKey key, TValue value)
		{
			this.key = key;
			this.value = value;
		}

		// Token: 0x170008F2 RID: 2290
		// (get) Token: 0x06003AF2 RID: 15090 RVA: 0x000DFBA8 File Offset: 0x000DDDA8
		[__DynamicallyInvokable]
		public TKey Key
		{
			[__DynamicallyInvokable]
			get
			{
				return this.key;
			}
		}

		// Token: 0x170008F3 RID: 2291
		// (get) Token: 0x06003AF3 RID: 15091 RVA: 0x000DFBB0 File Offset: 0x000DDDB0
		[__DynamicallyInvokable]
		public TValue Value
		{
			[__DynamicallyInvokable]
			get
			{
				return this.value;
			}
		}

		// Token: 0x06003AF4 RID: 15092 RVA: 0x000DFBB8 File Offset: 0x000DDDB8
		[__DynamicallyInvokable]
		public override string ToString()
		{
			StringBuilder stringBuilder = StringBuilderCache.Acquire(16);
			stringBuilder.Append('[');
			if (this.Key != null)
			{
				StringBuilder stringBuilder2 = stringBuilder;
				TKey tkey = this.Key;
				stringBuilder2.Append(tkey.ToString());
			}
			stringBuilder.Append(", ");
			if (this.Value != null)
			{
				StringBuilder stringBuilder3 = stringBuilder;
				TValue tvalue = this.Value;
				stringBuilder3.Append(tvalue.ToString());
			}
			stringBuilder.Append(']');
			return StringBuilderCache.GetStringAndRelease(stringBuilder);
		}

		// Token: 0x04001959 RID: 6489
		private TKey key;

		// Token: 0x0400195A RID: 6490
		private TValue value;
	}
}
