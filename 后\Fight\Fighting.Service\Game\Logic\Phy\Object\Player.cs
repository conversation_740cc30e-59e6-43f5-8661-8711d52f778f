using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using Bussiness;
using Bussiness.Managers;
using Game.Logic.Actions;
using Game.Logic.CardEffect.Effects;
using Game.Logic.Effects;
using Game.Logic.Event;
using Game.Logic.GhostEffect;
using Game.Logic.PetEffects;
using Game.Logic.PetEffects.Element;
using Game.Logic.PetEffects.Element.Actives;
using Game.Logic.PetEffects.Element.Passives;
using Game.Logic.PetSkills.ActiveSkills;
using Game.Logic.PetSkills.ContinueSkills;
using Game.Logic.PetSkills.PassiveSkills;
using Game.Logic.Phy.Maths;
using Game.Logic.Spells;
using Newtonsoft.Json;
using SqlDataProvider.Data;

namespace Game.Logic.Phy.Object
{
	// Token: 0x020004EF RID: 1263
	public class Player : TurnedLiving
	{
		// Token: 0x17000F3D RID: 3901
		// (get) Token: 0x06003679 RID: 13945 RVA: 0x000D21F0 File Offset: 0x000D03F0
		// (set) Token: 0x0600367A RID: 13946 RVA: 0x00019437 File Offset: 0x00017637
		public int PowerRatio
		{
			get
			{
				return this.m_ratioPower;
			}
			set
			{
				this.m_ratioPower = value;
			}
		}

		// Token: 0x17000F3E RID: 3902
		// (get) Token: 0x0600367B RID: 13947 RVA: 0x00019441 File Offset: 0x00017641
		public Dictionary<int, PetSkillInfo> PetSkillCD
		{
			get
			{
				return this._petSkillCd;
			}
		}

		// Token: 0x17000F3F RID: 3903
		// (get) Token: 0x0600367C RID: 13948 RVA: 0x00019449 File Offset: 0x00017649
		public UserPetInfo UserPet
		{
			get
			{
				return this.m_pet;
			}
		}

		// Token: 0x17000F40 RID: 3904
		// (get) Token: 0x0600367D RID: 13949 RVA: 0x00019451 File Offset: 0x00017651
		// (set) Token: 0x0600367E RID: 13950 RVA: 0x00019459 File Offset: 0x00017659
		public bool jaUsouSkill { get; set; }

		// Token: 0x17000F41 RID: 3905
		// (get) Token: 0x0600367F RID: 13951 RVA: 0x00019462 File Offset: 0x00017662
		// (set) Token: 0x06003680 RID: 13952 RVA: 0x0001946A File Offset: 0x0001766A
		public bool IsBlackFlame { get; set; }

		// Token: 0x17000F42 RID: 3906
		// (get) Token: 0x06003681 RID: 13953 RVA: 0x00019473 File Offset: 0x00017673
		// (set) Token: 0x06003682 RID: 13954 RVA: 0x0001947B File Offset: 0x0001767B
		public int CureBombValue { get; set; }

		// Token: 0x17000F43 RID: 3907
		// (get) Token: 0x06003683 RID: 13955 RVA: 0x00019484 File Offset: 0x00017684
		public IGamePlayer PlayerDetail
		{
			get
			{
				return this.m_player;
			}
		}

		// Token: 0x17000F44 RID: 3908
		// (get) Token: 0x06003684 RID: 13956 RVA: 0x0001948C File Offset: 0x0001768C
		public ItemInfo Weapon
		{
			get
			{
				return this.m_weapon;
			}
		}

		// Token: 0x17000F45 RID: 3909
		// (get) Token: 0x06003685 RID: 13957 RVA: 0x00019494 File Offset: 0x00017694
		public ItemInfo DeputyWeapon
		{
			get
			{
				return this.m_DeputyWeapon;
			}
		}

		// Token: 0x17000F46 RID: 3910
		// (get) Token: 0x06003686 RID: 13958 RVA: 0x0001949C File Offset: 0x0001769C
		public bool IsActive
		{
			get
			{
				return this.m_isActive;
			}
		}

		// Token: 0x17000F47 RID: 3911
		// (get) Token: 0x06003687 RID: 13959 RVA: 0x000D2208 File Offset: 0x000D0408
		// (set) Token: 0x06003688 RID: 13960 RVA: 0x000194A4 File Offset: 0x000176A4
		public List<int> Prop
		{
			get
			{
				return this.m_prop;
			}
			set
			{
				this.m_prop = value;
			}
		}

		// Token: 0x17000F48 RID: 3912
		// (get) Token: 0x06003689 RID: 13961 RVA: 0x000D2220 File Offset: 0x000D0420
		// (set) Token: 0x0600368A RID: 13962 RVA: 0x000D2238 File Offset: 0x000D0438
		public bool CanGetProp
		{
			get
			{
				return this.m_canGetProp;
			}
			set
			{
				bool flag = this.m_canGetProp != value;
				if (flag)
				{
					this.m_canGetProp = value;
				}
			}
		}

		// Token: 0x17000F49 RID: 3913
		// (get) Token: 0x0600368B RID: 13963 RVA: 0x000D2260 File Offset: 0x000D0460
		// (set) Token: 0x0600368C RID: 13964 RVA: 0x000194AE File Offset: 0x000176AE
		public int KilledPunishmentOffer
		{
			get
			{
				return this.m_killedPunishmentOffer;
			}
			set
			{
				this.m_killedPunishmentOffer = value;
			}
		}

		// Token: 0x17000F4A RID: 3914
		// (get) Token: 0x0600368D RID: 13965 RVA: 0x000D2278 File Offset: 0x000D0478
		// (set) Token: 0x0600368E RID: 13966 RVA: 0x000D2290 File Offset: 0x000D0490
		public int LoadingProcess
		{
			get
			{
				return this.m_loadingProcess;
			}
			set
			{
				bool flag = this.m_loadingProcess != value;
				if (flag)
				{
					this.m_loadingProcess = value;
					bool flag2 = this.m_loadingProcess >= 100;
					if (flag2)
					{
						this.OnLoadingCompleted();
					}
				}
			}
		}

		// Token: 0x17000F4B RID: 3915
		// (get) Token: 0x0600368F RID: 13967 RVA: 0x000D22D4 File Offset: 0x000D04D4
		public int LevelPlusBlood
		{
			get
			{
				int num = 0;
				for (int i = 10; i <= 60; i += 10)
				{
					bool flag = this.PlayerDetail.PlayerCharacter.Grade - i > 0;
					if (flag)
					{
						num += (this.PlayerDetail.PlayerCharacter.Grade - i) * (i + 20);
					}
				}
				return num;
			}
		}

		// Token: 0x17000F4C RID: 3916
		// (get) Token: 0x06003690 RID: 13968 RVA: 0x000D2338 File Offset: 0x000D0538
		// (set) Token: 0x06003691 RID: 13969 RVA: 0x000194B8 File Offset: 0x000176B8
		public int Energy
		{
			get
			{
				return this.m_energy;
			}
			set
			{
				this.m_energy = value;
			}
		}

		// Token: 0x17000F4D RID: 3917
		// (get) Token: 0x06003692 RID: 13970 RVA: 0x000194C2 File Offset: 0x000176C2
		public BallInfo CurrentBall
		{
			get
			{
				return this.m_currentBall;
			}
		}

		// Token: 0x17000F4E RID: 3918
		// (get) Token: 0x06003693 RID: 13971 RVA: 0x000194CA File Offset: 0x000176CA
		public bool IsSpecialSkill
		{
			get
			{
				return this.m_currentBall.ID == this.m_spBallId;
			}
		}

		// Token: 0x17000F4F RID: 3919
		// (get) Token: 0x06003694 RID: 13972 RVA: 0x000D2350 File Offset: 0x000D0550
		// (set) Token: 0x06003695 RID: 13973 RVA: 0x000194DF File Offset: 0x000176DF
		public int ChangeSpecialBall
		{
			get
			{
				return this.m_changeSpecialball;
			}
			set
			{
				this.m_changeSpecialball = value;
			}
		}

		// Token: 0x17000F50 RID: 3920
		// (get) Token: 0x06003696 RID: 13974 RVA: 0x000D2368 File Offset: 0x000D0568
		// (set) Token: 0x06003697 RID: 13975 RVA: 0x000D2380 File Offset: 0x000D0580
		public int ShootCount
		{
			get
			{
				return this.m_shootCount;
			}
			set
			{
				bool flag = this.m_shootCount != value;
				if (flag)
				{
					this.m_shootCount = value;
					this.m_game.SendGameUpdateShootCount(this);
				}
			}
		}

		// Token: 0x17000F51 RID: 3921
		// (get) Token: 0x06003698 RID: 13976 RVA: 0x000D23B4 File Offset: 0x000D05B4
		// (set) Token: 0x06003699 RID: 13977 RVA: 0x000D23CC File Offset: 0x000D05CC
		public int BallCount
		{
			get
			{
				return this.m_ballCount;
			}
			set
			{
				bool flag = this.m_ballCount != value;
				if (flag)
				{
					this.m_ballCount = value;
				}
			}
		}

		// Token: 0x17000F52 RID: 3922
		// (get) Token: 0x0600369A RID: 13978 RVA: 0x000194E9 File Offset: 0x000176E9
		public int flyCount
		{
			get
			{
				return this._flyCoolDown;
			}
		}

		// Token: 0x17000F53 RID: 3923
		// (get) Token: 0x0600369B RID: 13979 RVA: 0x000194F1 File Offset: 0x000176F1
		public int deputyWeaponCount
		{
			get
			{
				return this.deputyWeaponResCount;
			}
		}

		// Token: 0x17000F54 RID: 3924
		// (get) Token: 0x0600369C RID: 13980 RVA: 0x000194F9 File Offset: 0x000176F9
		// (set) Token: 0x0600369D RID: 13981 RVA: 0x00019501 File Offset: 0x00017701
		public int ReloadPointDietYeu { get; set; }

		// Token: 0x17000F55 RID: 3925
		// (get) Token: 0x0600369E RID: 13982 RVA: 0x0001950A File Offset: 0x0001770A
		// (set) Token: 0x0600369F RID: 13983 RVA: 0x00019512 File Offset: 0x00017712
		public bool ActiveKimCang { get; set; }

		// Token: 0x17000F56 RID: 3926
		// (get) Token: 0x060036A0 RID: 13984 RVA: 0x0001951B File Offset: 0x0001771B
		// (set) Token: 0x060036A1 RID: 13985 RVA: 0x00019523 File Offset: 0x00017723
		public bool ActiveThanMayMan { get; set; }

		// Token: 0x17000F57 RID: 3927
		// (get) Token: 0x060036A2 RID: 13986 RVA: 0x0001952C File Offset: 0x0001772C
		// (set) Token: 0x060036A3 RID: 13987 RVA: 0x00019534 File Offset: 0x00017734
		public bool ActiveKhiepSo { get; set; }

		// Token: 0x14000034 RID: 52
		// (add) Token: 0x060036A4 RID: 13988 RVA: 0x000D23F4 File Offset: 0x000D05F4
		// (remove) Token: 0x060036A5 RID: 13989 RVA: 0x000D242C File Offset: 0x000D062C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle LoadingCompleted;

		// Token: 0x14000035 RID: 53
		// (add) Token: 0x060036A6 RID: 13990 RVA: 0x000D2464 File Offset: 0x000D0664
		// (remove) Token: 0x060036A7 RID: 13991 RVA: 0x000D249C File Offset: 0x000D069C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerShoot;

		// Token: 0x14000036 RID: 54
		// (add) Token: 0x060036A8 RID: 13992 RVA: 0x000D24D4 File Offset: 0x000D06D4
		// (remove) Token: 0x060036A9 RID: 13993 RVA: 0x000D250C File Offset: 0x000D070C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerSkip;

		// Token: 0x14000037 RID: 55
		// (add) Token: 0x060036AA RID: 13994 RVA: 0x000D2544 File Offset: 0x000D0744
		// (remove) Token: 0x060036AB RID: 13995 RVA: 0x000D257C File Offset: 0x000D077C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerAfterBuffSkillPet;

		// Token: 0x14000038 RID: 56
		// (add) Token: 0x060036AC RID: 13996 RVA: 0x000D25B4 File Offset: 0x000D07B4
		// (remove) Token: 0x060036AD RID: 13997 RVA: 0x000D25EC File Offset: 0x000D07EC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBuffSkill;

		// Token: 0x14000039 RID: 57
		// (add) Token: 0x060036AE RID: 13998 RVA: 0x000D2624 File Offset: 0x000D0824
		// (remove) Token: 0x060036AF RID: 13999 RVA: 0x000D265C File Offset: 0x000D085C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerAnyShellThrow;

		// Token: 0x1400003A RID: 58
		// (add) Token: 0x060036B0 RID: 14000 RVA: 0x000D2694 File Offset: 0x000D0894
		// (remove) Token: 0x060036B1 RID: 14001 RVA: 0x000D26CC File Offset: 0x000D08CC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerShootCure;

		// Token: 0x1400003B RID: 59
		// (add) Token: 0x060036B2 RID: 14002 RVA: 0x000D2704 File Offset: 0x000D0904
		// (remove) Token: 0x060036B3 RID: 14003 RVA: 0x000D273C File Offset: 0x000D093C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBeginMoving;

		// Token: 0x1400003C RID: 60
		// (add) Token: 0x060036B4 RID: 14004 RVA: 0x000D2774 File Offset: 0x000D0974
		// (remove) Token: 0x060036B5 RID: 14005 RVA: 0x000D27AC File Offset: 0x000D09AC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerShootEventHandle BeforePlayerShoot;

		// Token: 0x1400003D RID: 61
		// (add) Token: 0x060036B6 RID: 14006 RVA: 0x000D27E4 File Offset: 0x000D09E4
		// (remove) Token: 0x060036B7 RID: 14007 RVA: 0x000D281C File Offset: 0x000D0A1C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle AfterPlayerShooted;

		// Token: 0x1400003E RID: 62
		// (add) Token: 0x060036B8 RID: 14008 RVA: 0x000D2854 File Offset: 0x000D0A54
		// (remove) Token: 0x060036B9 RID: 14009 RVA: 0x000D288C File Offset: 0x000D0A8C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerCompleteShoot;

		// Token: 0x1400003F RID: 63
		// (add) Token: 0x060036BA RID: 14010 RVA: 0x000D28C4 File Offset: 0x000D0AC4
		// (remove) Token: 0x060036BB RID: 14011 RVA: 0x000D28FC File Offset: 0x000D0AFC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerGuard;

		// Token: 0x14000040 RID: 64
		// (add) Token: 0x060036BC RID: 14012 RVA: 0x000D2934 File Offset: 0x000D0B34
		// (remove) Token: 0x060036BD RID: 14013 RVA: 0x000D296C File Offset: 0x000D0B6C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerCure;

		// Token: 0x14000041 RID: 65
		// (add) Token: 0x060036BE RID: 14014 RVA: 0x000D29A4 File Offset: 0x000D0BA4
		// (remove) Token: 0x060036BF RID: 14015 RVA: 0x000D29DC File Offset: 0x000D0BDC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBuffSkillPet;

		// Token: 0x14000042 RID: 66
		// (add) Token: 0x060036C0 RID: 14016 RVA: 0x000D2A14 File Offset: 0x000D0C14
		// (remove) Token: 0x060036C1 RID: 14017 RVA: 0x000D2A4C File Offset: 0x000D0C4C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerClearBuffSkillPet;

		// Token: 0x14000043 RID: 67
		// (add) Token: 0x060036C2 RID: 14018 RVA: 0x000D2A84 File Offset: 0x000D0C84
		// (remove) Token: 0x060036C3 RID: 14019 RVA: 0x000D2ABC File Offset: 0x000D0CBC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle CollidByObject;

		// Token: 0x14000044 RID: 68
		// (add) Token: 0x060036C4 RID: 14020 RVA: 0x000D2AF4 File Offset: 0x000D0CF4
		// (remove) Token: 0x060036C5 RID: 14021 RVA: 0x000D2B2C File Offset: 0x000D0D2C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerUseDander;

		// Token: 0x14000045 RID: 69
		// (add) Token: 0x060036C6 RID: 14022 RVA: 0x000D2B64 File Offset: 0x000D0D64
		// (remove) Token: 0x060036C7 RID: 14023 RVA: 0x000D2B9C File Offset: 0x000D0D9C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBeforeReset;

		// Token: 0x14000046 RID: 70
		// (add) Token: 0x060036C8 RID: 14024 RVA: 0x000D2BD4 File Offset: 0x000D0DD4
		// (remove) Token: 0x060036C9 RID: 14025 RVA: 0x000D2C0C File Offset: 0x000D0E0C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerAfterReset;

		// Token: 0x14000047 RID: 71
		// (add) Token: 0x060036CA RID: 14026 RVA: 0x000D2C44 File Offset: 0x000D0E44
		// (remove) Token: 0x060036CB RID: 14027 RVA: 0x000D2C7C File Offset: 0x000D0E7C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerSecondWeaponEventHandle PlayerUseSecondWeapon;

		// Token: 0x14000048 RID: 72
		// (add) Token: 0x060036CC RID: 14028 RVA: 0x000D2CB4 File Offset: 0x000D0EB4
		// (remove) Token: 0x060036CD RID: 14029 RVA: 0x000D2CEC File Offset: 0x000D0EEC
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event Player.PlayerUsePetMPEventHandle PlayerUsePetMP;

		// Token: 0x060036CE RID: 14030 RVA: 0x000D2D24 File Offset: 0x000D0F24
		public Player(IGamePlayer player, int id, BaseGame game, int team, int maxBlood)
			: base(id, game, team, "", "", 1000, 0, 1)
		{
			this.m_rect = new Rectangle(-15, -20, 30, 30);
			this.m_player = player;
			this.m_player.GamePlayerId = id;
			this.m_canGetProp = true;
			this.m_isActive = true;
			this.Grade = player.PlayerCharacter.Grade;
			this.TotalAllHurt = 0;
			this.TotalAllHitTargetCount = 0;
			this.TotalAllShootCount = 0;
			this.TotalAllKill = 0;
			this.TotalAllExperience = 0;
			this.TotalAllScore = 0;
			this.TotalAllCure = 0;
			this.m_prop = new List<int>();
			this.propsBloqueados = new List<int>();
			this.IsShowEffectA = true;
			this.IsShowEffectB = true;
			this.m_weapon = this.m_player.MainWeapon;
			this.m_DeputyWeapon = this.m_player.SecondWeapon;
			this.m_loadingProcess = 0;
			base.VaneOpen = player.PlayerCharacter.IsWeakGuildFinish(9);
			this.CanUsePetSkill = true;
			this.ChangeSpecialBall = 0;
			this.EquipGhostReduceCritDamage = 0;
			this.m_pet = player.UserPet;
			this._petSkillCd = new Dictionary<int, PetSkillInfo>();
			bool flag = game == null;
			if (!flag)
			{
				this.InitBuffer(this.m_player.EquipEffect);
				this.InitFightBuffer(this.m_player.FightBuffs);
				this.InitCardBuffer(this.m_player.CardEffect);
				this.InitEquipGhostSkills();
				bool flag2 = this.m_pet != null;
				if (flag2)
				{
					this.InitPetSkillEffect();
				}
				bool flag3 = this.m_DeputyWeapon != null;
				if (flag3)
				{
					this.deputyWeaponResCount = this.m_DeputyWeapon.StrengthenLevel + 1;
				}
				else
				{
					this.deputyWeaponResCount = 1;
				}
				bool flag4 = !game.IsSpecialPVE();
				if (flag4)
				{
					UserBufferInfo fightBuffByType = this.GetFightBuffByType(eBuffType.Agility);
					bool flag5 = fightBuffByType != null && this.m_player.UsePayBuff(eBuffType.Agility) && this.m_game.RoomType == eRoomType.Dungeon;
					if (flag5)
					{
						this.m_bufferPoint = fightBuffByType;
					}
				}
				bool flag6 = this.m_weapon != null;
				if (flag6)
				{
					BombConfigInfo bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.TemplateID);
					bool flag7 = this.m_weapon.GoldValidDate();
					if (flag7)
					{
						bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.GoldEquip.TemplateID);
					}
					bool flag8 = bombConfigInfo != null;
					if (flag8)
					{
						this.m_mainBallId = bombConfigInfo.Common;
						this.m_spBallId = bombConfigInfo.Special;
						this.m_spsBallId = bombConfigInfo.SpecialII;
						this.m_AddWoundBallId = bombConfigInfo.CommonAddWound;
						this.m_MultiBallId = bombConfigInfo.CommonMultiBall;
					}
				}
			}
		}

		// Token: 0x060036CF RID: 14031 RVA: 0x000D3008 File Offset: 0x000D1208
		public override void Reset()
		{
			this.OnPlayerBeforeReset();
			bool flag = this.m_game.RoomType == eRoomType.Dungeon || this.m_game.RoomType == eRoomType.Boss || this.m_game.RoomType == eRoomType.Academy;
			if (flag)
			{
				this.m_game.Cards = new int[21];
			}
			else
			{
				this.m_game.Cards = new int[9];
			}
			base.Dander = 0;
			this.ReloadPointDietYeu = 0;
			base.psychic = 0;
			base.IsLiving = true;
			this.FinishTakeCard = false;
			this.m_changeSpecialball = 0;
			this.m_weapon = this.m_player.MainWeapon;
			BombConfigInfo bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.TemplateID);
			bool flag2 = bombConfigInfo != null;
			if (flag2)
			{
				this.m_mainBallId = bombConfigInfo.Common;
				this.m_spBallId = bombConfigInfo.Special;
				this.m_spsBallId = bombConfigInfo.SpecialII;
				this.m_AddWoundBallId = bombConfigInfo.CommonAddWound;
				this.m_MultiBallId = bombConfigInfo.CommonMultiBall;
			}
			else
			{
				Console.WriteLine("systerm do not suport this weapon ID {0}, enter db and check dbo.BallConfig", this.m_weapon.TemplateID);
			}
			this.m_DeputyWeapon = this.m_player.SecondWeapon;
			bool flag3 = this.m_DeputyWeapon != null;
			if (flag3)
			{
				this.deputyWeaponResCount = this.m_DeputyWeapon.StrengthenLevel + 1;
			}
			else
			{
				this.deputyWeaponResCount = 1;
			}
			this.m_maxBlood = this.m_player.PlayerCharacter.Blood;
			bool flag4 = base.FightBuffers.ConsortionAddMaxBlood > 0;
			if (flag4)
			{
				this.m_maxBlood += this.m_maxBlood * base.FightBuffers.ConsortionAddMaxBlood / 100;
			}
			this.m_maxBlood += base.FightBuffers.WorldBossHP + base.FightBuffers.WorldBossHP_MoneyBuff;
			this.m_maxBlood += ((base.PetEffects != null) ? base.PetEffects.AddMaxBloodValue : 0);
			this.BaseDamage = this.m_player.GetBaseAttack();
			bool flag5 = base.FightBuffers.ConsortionAddDamage > 0;
			if (flag5)
			{
				this.BaseDamage += (double)base.FightBuffers.ConsortionAddDamage;
			}
			bool flag6 = base.FightBuffers.WorldBossAttrack_MoneyBuff > 0;
			if (flag6)
			{
				this.BaseDamage += (double)base.FightBuffers.WorldBossAttrack_MoneyBuff;
			}
			this.BaseGuard = this.m_player.GetBaseDefence();
			this.Attack = (double)this.m_player.PlayerCharacter.Attack;
			this.Defence = (double)this.m_player.PlayerCharacter.Defence;
			this.Agility = (double)this.m_player.PlayerCharacter.Agility;
			this.Lucky = (double)this.m_player.PlayerCharacter.Luck;
			bool flag7 = base.FightBuffers.ConsortionAddProperty > 0;
			if (flag7)
			{
				this.Attack += (double)base.FightBuffers.ConsortionAddProperty;
				this.Defence += (double)base.FightBuffers.ConsortionAddProperty;
				this.Agility += (double)base.FightBuffers.ConsortionAddProperty;
				this.Lucky += (double)base.FightBuffers.ConsortionAddProperty;
			}
			bool flag8 = this.m_bufferPoint != null;
			if (flag8)
			{
				this.Attack += this.Attack / 100.0 * (double)this.m_bufferPoint.Value;
				this.Defence += this.Defence / 100.0 * (double)this.m_bufferPoint.Value;
				this.Agility += this.Agility / 100.0 * (double)this.m_bufferPoint.Value;
				this.Lucky += this.Lucky / 100.0 * (double)this.m_bufferPoint.Value;
			}
			this.m_energy = (int)this.Agility / 30 + 240;
			bool flag9 = base.FightBuffers.ConsortionAddEnergy > 0;
			if (flag9)
			{
				this.m_energy += base.FightBuffers.ConsortionAddEnergy;
			}
			bool flag10 = this.m_game.GameType == eGameType.GuildLeage || this.m_game.GameType == eGameType.Free;
			if (flag10)
			{
				this.Agility = 3000.0;
				this.Lucky = 2000.0;
				this.m_player.SendMessage("当前比赛为平衡竞技，您的属性已经平衡.");
			}
			this.m_currentBall = BallMgr.FindBall(this.m_mainBallId);
			this.m_shootCount = 1;
			this.m_ballCount = 1;
			this.CurrentIsHitTarget = false;
			this.LimitEnergy = false;
			this.TotalCure = 0;
			this.TotalHitTargetCount = 0;
			this.TotalHurt = 0;
			this.TotalKill = 0;
			this.TotalShootCount = 0;
			this.TotalDamageForMatch = 0;
			this.LockDirection = false;
			this.isLockXY = false;
			this.GainGP = 0;
			this.GainOffer = 0;
			this.Ready = false;
			this.IsAddTurn = false;
			base.OldAttack = this.Attack;
			base.OldDefence = this.Defence;
			base.OldAgility = this.Agility;
			base.OldLucky = this.Lucky;
			base.OldBaseDamage = this.BaseDamage;
			base.OldBaseGuard = this.BaseGuard;
			this.LoadingProcess = 0;
			this.InitBuffer(this.m_player.EquipEffect);
			this.InitFightBuffer(this.m_player.FightBuffs);
			this.isLockEmery = false;
			this.InitCardBuffer(this.m_player.CardEffect);
			bool flag11 = !this.isLockEmery;
			if (flag11)
			{
				this.m_ratioPower = 100 - base.FightBuffers.ConsortionReduceEnergyUse;
			}
			else
			{
				this.m_ratioPower = 0;
			}
			this.SoulPropCount = 0;
			this.m_prop.Clear();
			this.PlayerDetail.ClearTempBag();
			this.m_delay = this.GetTurnDelay();
			base.PetMP = 10;
			base.PetFlag = 0;
			this.ResetSkillCd();
			this.OnPlayerAfterReset();
			base.Reset();
		}

		// Token: 0x060036D0 RID: 14032 RVA: 0x000D3644 File Offset: 0x000D1844
		private int GetTurnDelay()
		{
			return 1600 - 1200 * this.PlayerDetail.PlayerCharacter.Agility / (this.PlayerDetail.PlayerCharacter.Agility + 1200) + this.PlayerDetail.PlayerCharacter.Attack / 10;
		}

		// Token: 0x060036D1 RID: 14033 RVA: 0x000D36A0 File Offset: 0x000D18A0
		public void CalculatePlayerOffer(Player player)
		{
			bool flag = this.m_game.RoomType == eRoomType.Match && (this.m_game.GameType == eGameType.Guild || this.m_game.GameType == eGameType.Free || this.m_game.GameType == eGameType.Leage || this.m_game.GameType == eGameType.GuildLeage) && !player.IsLiving;
			if (flag)
			{
				int num = ((base.Game.GameType == eGameType.Guild || this.m_game.GameType == eGameType.GuildLeage) ? 10 : ((this.PlayerDetail.PlayerCharacter.ConsortiaID == 0 || player.PlayerDetail.PlayerCharacter.ConsortiaID == 0) ? 1 : 3));
				bool flag2 = num > player.PlayerDetail.PlayerCharacter.Offer;
				if (flag2)
				{
					num = player.PlayerDetail.PlayerCharacter.Offer;
				}
				bool flag3 = num > 0;
				if (flag3)
				{
					this.GainOffer += num;
					player.KilledPunishmentOffer = num;
				}
			}
		}

		// Token: 0x060036D2 RID: 14034 RVA: 0x000D37A0 File Offset: 0x000D19A0
		public void InitBuffer(List<int> equpedEffect)
		{
			base.EffectList.StopAllEffect();
			for (int i = 0; i < equpedEffect.Count; i++)
			{
				ItemTemplateInfo itemTemplateInfo = ItemMgr.FindItemTemplate(equpedEffect[i]);
				switch (itemTemplateInfo.Property3)
				{
				case 1:
					new AddAttackEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 2:
					new AddDefenceEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 3:
					new AddAgilityEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 4:
					new AddLuckyEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 5:
					new AddDamageEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 6:
					new ReduceDamageEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 7:
					new AddBloodEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 8:
					new FatalEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 9:
					new IceFronzeEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 10:
					new NoHoleEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 11:
					new AtomBombEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 12:
					new ArmorPiercerEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 13:
					new AvoidDamageEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 14:
					new MakeCriticalEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 15:
					new AssimilateDamageEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 16:
					new AssimilateBloodEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 17:
					new SealEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 18:
					new AddTurnEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5, itemTemplateInfo.TemplateID).Start(this);
					break;
				case 19:
					new AddDanderEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 20:
					new ReflexDamageEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 21:
					new ReduceStrengthEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 22:
					new ContinueReduceBloodEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 23:
					new LockDirectionEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 24:
					new AddBombEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 25:
					new ContinueReduceBaseDamageEquipEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				case 26:
					new RecoverBloodEffect(itemTemplateInfo.Property4, itemTemplateInfo.Property5).Start(this);
					break;
				}
			}
		}

		// Token: 0x060036D3 RID: 14035 RVA: 0x000D3B50 File Offset: 0x000D1D50
		public void InitFightBuffer(List<UserBufferInfo> buffers)
		{
			foreach (UserBufferInfo userBufferInfo in buffers)
			{
				switch (userBufferInfo.Type)
				{
				case 101:
					base.FightBuffers.ConsortionAddBloodGunCount = userBufferInfo.Value;
					break;
				case 102:
					base.FightBuffers.ConsortionAddDamage = userBufferInfo.Value;
					break;
				case 103:
					base.FightBuffers.ConsortionAddCritical = userBufferInfo.Value;
					break;
				case 104:
					base.FightBuffers.ConsortionAddMaxBlood = userBufferInfo.Value;
					break;
				case 105:
					base.FightBuffers.ConsortionAddProperty = userBufferInfo.Value;
					break;
				case 106:
					base.FightBuffers.ConsortionReduceEnergyUse = userBufferInfo.Value;
					break;
				case 107:
					base.FightBuffers.ConsortionAddEnergy = userBufferInfo.Value;
					break;
				case 108:
					base.FightBuffers.ConsortionAddEffectTurn = userBufferInfo.Value;
					break;
				case 109:
					base.FightBuffers.ConsortionAddOfferRate = userBufferInfo.Value;
					break;
				case 110:
					base.FightBuffers.ConsortionAddPercentGoldOrGP = userBufferInfo.Value;
					break;
				case 111:
					base.FightBuffers.ConsortionAddSpellCount = userBufferInfo.Value;
					break;
				case 112:
					base.FightBuffers.ConsortionReduceDander = userBufferInfo.Value;
					break;
				default:
					Console.WriteLine(string.Format("Not Found FightBuff Type {0} Value {1}", userBufferInfo.Type, userBufferInfo.Value));
					break;
				}
			}
		}

		// Token: 0x060036D4 RID: 14036 RVA: 0x000D3D24 File Offset: 0x000D1F24
		public void InitCardBuffer(List<int> cards)
		{
			base.CardEffectList.StopAllEffect();
			int num = 30;
			foreach (int num2 in cards)
			{
				bool flag = num2 < 1100;
				if (flag)
				{
					num = num2 - 1000;
				}
			}
			int num3 = 0;
			bool flag2 = num >= 10;
			if (flag2)
			{
				num3 = 1;
			}
			bool flag3 = num >= 20;
			if (flag3)
			{
				num3 = 2;
			}
			bool flag4 = num >= 30;
			if (flag4)
			{
				num3 = 3;
			}
			Dictionary<int, List<CardGroupInfo>> allCard = CardBuffMgr.GetAllCard();
			List<CardBuffInfo> list = new List<CardBuffInfo>();
			int num4 = 0;
			List<CardBuffInfo> list2 = new List<CardBuffInfo>();
			foreach (int num5 in allCard.Keys)
			{
				num4 = 0;
				foreach (CardGroupInfo cardGroupInfo in allCard[num5])
				{
					foreach (int num6 in cards)
					{
						bool flag5 = num6 == cardGroupInfo.TemplateID;
						if (flag5)
						{
							num4++;
						}
					}
				}
				list = CardBuffMgr.FindCardBuffs(num5);
				bool flag6 = list == null;
				if (!flag6)
				{
					list.Reverse();
					foreach (CardBuffInfo cardBuffInfo in list)
					{
						bool flag7 = num4 >= cardBuffInfo.Condition;
						if (flag7)
						{
							CardInfo cardInfo = CardBuffMgr.FindCard(num5);
							bool flag8 = cardInfo != null && cardBuffInfo != null;
							if (flag8)
							{
								list2.Add(cardBuffInfo);
							}
						}
					}
				}
			}
			foreach (CardBuffInfo cardBuffInfo2 in list2)
			{
				Console.WriteLine(string.Format("Card ID = {0}", cardBuffInfo2.CardID));
				switch (cardBuffInfo2.CardID)
				{
				case 1:
					new AntCaveEffect(num3, cardBuffInfo2).Start(this);
					break;
				case 2:
				{
					bool flag9 = cardBuffInfo2.Condition >= 2;
					if (flag9)
					{
						new GuluKingdom2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag10 = cardBuffInfo2.Condition >= 4;
					if (flag10)
					{
						new GuluKingdom4Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 3:
				{
					bool flag11 = cardBuffInfo2.Condition >= 3;
					if (flag11)
					{
						new EvilTribe3Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag12 = cardBuffInfo2.Condition >= 5;
					if (flag12)
					{
						new EvilTribe5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 4:
				{
					bool flag13 = cardBuffInfo2.Condition >= 2;
					if (flag13)
					{
						new ShadowDevil2Effect(num3, cardBuffInfo2).Start(this);
						bool flag14 = this.m_game.IsPVE();
						if (flag14)
						{
							this.isLockEmery = true;
						}
					}
					bool flag15 = cardBuffInfo2.Condition >= 4;
					if (flag15)
					{
						new ShadowDevil4Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 5:
				{
					bool flag16 = cardBuffInfo2.Condition >= 2;
					if (flag16)
					{
						new FourArtifacts2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag17 = cardBuffInfo2.Condition >= 4;
					if (flag17)
					{
						new FourArtifacts4Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 6:
				{
					bool flag18 = cardBuffInfo2.Condition >= 2;
					if (flag18)
					{
						new Goblin2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag19 = cardBuffInfo2.Condition >= 4;
					if (flag19)
					{
						new Goblin4Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag20 = cardBuffInfo2.Condition >= 5;
					if (flag20)
					{
						new Goblin5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 7:
				{
					bool flag21 = cardBuffInfo2.Condition >= 2;
					if (flag21)
					{
						new RunRunChicken2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag22 = cardBuffInfo2.Condition >= 4;
					if (flag22)
					{
						new RunRunChicken4Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 8:
				{
					bool flag23 = cardBuffInfo2.Condition >= 2;
					if (flag23)
					{
						new GuluSportsMeeting2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag24 = cardBuffInfo2.Condition >= 4;
					if (flag24)
					{
						new GuluSportsMeeting4Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag25 = cardBuffInfo2.Condition >= 5;
					if (flag25)
					{
						new GuluSportsMeeting5Effect(num3, cardBuffInfo2).Start(this);
						bool flag26 = base.Game is PVEGame && (base.Game as PVEGame).Info.ID == 8;
						if (flag26)
						{
							this.isLockEmery = true;
						}
					}
					break;
				}
				case 9:
				{
					bool flag27 = cardBuffInfo2.Condition >= 2;
					if (flag27)
					{
						new FiveGodSoldier2Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag28 = cardBuffInfo2.Condition >= 5;
					if (flag28)
					{
						new FiveGodSoldier5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 10:
				{
					bool flag29 = cardBuffInfo2.Condition >= 3;
					if (flag29)
					{
						new TimeVortex3Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag30 = cardBuffInfo2.Condition >= 5;
					if (flag30)
					{
						new TimeVortex5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 11:
				{
					bool flag31 = cardBuffInfo2.Condition >= 3;
					if (flag31)
					{
						new WarriorsArena3Effect(num3, cardBuffInfo2).Start(this);
					}
					bool flag32 = cardBuffInfo2.Condition >= 5;
					if (flag32)
					{
						new WarriorsArena5Effect(num3, cardBuffInfo2).Start(this);
					}
					break;
				}
				case 12:
					new PioneerEffect(num3, cardBuffInfo2).Start(this);
					break;
				case 13:
					new WeaponMasterEffect(num3, cardBuffInfo2).Start(this);
					break;
				case 14:
					new DivineEffect(num3, cardBuffInfo2).Start(this);
					break;
				case 15:
					new LuckyEffect(num3, cardBuffInfo2).Start(this);
					break;
				default:
					Console.WriteLine("CardBuffer --- CardID Not Found");
					break;
				}
			}
		}

		// Token: 0x060036D5 RID: 14037 RVA: 0x000D445C File Offset: 0x000D265C
		private void InitEquipGhostSkills()
		{
			Dictionary<string, UserEquipGhostInfo> dictionary = JsonConvert.DeserializeObject<Dictionary<string, UserEquipGhostInfo>>(this.PlayerDetail.PlayerCharacter.GhostEquipList) ?? new Dictionary<string, UserEquipGhostInfo>();
			bool flag = dictionary == null;
			if (!flag)
			{
				foreach (UserEquipGhostInfo userEquipGhostInfo in dictionary.Values)
				{
					int spiritSkill = SpiritInfoMgr.GetSpiritSkill(userEquipGhostInfo.BagType, userEquipGhostInfo.Place, userEquipGhostInfo.Level);
					PetSkillInfo petSkillInfo = PetMgr.FindPetSkill(spiritSkill);
					bool flag2 = petSkillInfo == null;
					if (!flag2)
					{
						string[] array = petSkillInfo.ElementIDs.Split(new char[] { ',' });
						string[] array2 = array;
						foreach (string text in array2)
						{
							string text2 = text;
							string text3 = text2;
							if (text3 != null)
							{
								int length = text3.Length;
								if (length == 4)
								{
									switch (text3[3])
									{
									case '0':
										if (!(text3 == "2110"))
										{
											if (text3 == "2210")
											{
												this.EquipGhostReduceCritDamage = 50;
												goto IL_03B8;
											}
											if (!(text3 == "2410"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '1':
										if (!(text3 == "2101"))
										{
											if (text3 == "2201")
											{
												this.EquipGhostReduceCritDamage = 5;
												goto IL_03B8;
											}
											if (!(text3 == "2401"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '2':
										if (!(text3 == "2102"))
										{
											if (text3 == "2202")
											{
												this.EquipGhostReduceCritDamage = 10;
												goto IL_03B8;
											}
											if (!(text3 == "2402"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '3':
										if (!(text3 == "2103"))
										{
											if (text3 == "2203")
											{
												this.EquipGhostReduceCritDamage = 15;
												goto IL_03B8;
											}
											if (!(text3 == "2403"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '4':
										if (!(text3 == "2104"))
										{
											if (text3 == "2204")
											{
												this.EquipGhostReduceCritDamage = 20;
												goto IL_03B8;
											}
											if (!(text3 == "2404"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '5':
										if (!(text3 == "2105"))
										{
											if (text3 == "2205")
											{
												this.EquipGhostReduceCritDamage = 25;
												goto IL_03B8;
											}
											if (!(text3 == "2405"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '6':
										if (!(text3 == "2106"))
										{
											if (text3 == "2206")
											{
												this.EquipGhostReduceCritDamage = 30;
												goto IL_03B8;
											}
											if (!(text3 == "2406"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '7':
										if (!(text3 == "2107"))
										{
											if (text3 == "2207")
											{
												this.EquipGhostReduceCritDamage = 35;
												goto IL_03B8;
											}
											if (!(text3 == "2407"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '8':
										if (!(text3 == "2108"))
										{
											if (text3 == "2208")
											{
												this.EquipGhostReduceCritDamage = 40;
												goto IL_03B8;
											}
											if (!(text3 == "2408"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									case '9':
										if (!(text3 == "2109"))
										{
											if (text3 == "2209")
											{
												this.EquipGhostReduceCritDamage = 45;
												goto IL_03B8;
											}
											if (!(text3 == "2409"))
											{
												goto IL_03B8;
											}
											goto IL_03A8;
										}
										break;
									default:
										goto IL_03B8;
									}
									new AddDanderDamage(text).Start(this);
									goto IL_03B8;
									IL_03A8:
									new AddDefence(text).Start(this);
								}
							}
							IL_03B8:;
						}
					}
				}
			}
		}

		// Token: 0x060036D6 RID: 14038 RVA: 0x000D4870 File Offset: 0x000D2A70
		public void InitPetSkillEffect()
		{
			string[] array = this.m_pet.SkillEquip.Split(new char[] { '|' });
			string[] array2 = array;
			foreach (string text in array2)
			{
				int num = int.Parse(text.Split(new char[] { ',' })[0]);
				PetSkillInfo petSkillInfo = PetMgr.FindPetSkill(num);
				bool flag = petSkillInfo == null;
				if (!flag)
				{
					string[] array4 = petSkillInfo.ElementIDs.Split(new char[] { ',' });
					int coldDown = petSkillInfo.ColdDown;
					int probability = petSkillInfo.Probability;
					int delay = petSkillInfo.Delay;
					int gameType = petSkillInfo.GameType;
					bool flag2 = !this._petSkillCd.ContainsKey(num);
					if (flag2)
					{
						this._petSkillCd.Add(num, petSkillInfo);
					}
					Console.WriteLine("加载宠物技能, 技能ID: " + petSkillInfo.ElementIDs);
					string[] array5 = array4;
					foreach (string text2 in array5)
					{
						bool flag3 = !string.IsNullOrEmpty(text2);
						if (flag3)
						{
							string text3 = text2;
							string text4 = text3;
							uint num2 = <PrivateImplementationDetails>.ComputeStringHash(text4);
							if (num2 <= 2375872611U)
							{
								if (num2 <= 1724466550U)
								{
									if (num2 <= 496337998U)
									{
										if (num2 <= 271175550U)
										{
											if (num2 <= 69402837U)
											{
												if (num2 <= 19069980U)
												{
													if (num2 <= 2439456U)
													{
														if (num2 != 2292361U)
														{
															if (num2 != 2439456U)
															{
																goto IL_531B;
															}
															if (!(text4 == "1149"))
															{
																goto IL_531B;
															}
															new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 40).Start(this);
															goto IL_5341;
														}
														else
														{
															if (!(text4 == "1155"))
															{
																goto IL_531B;
															}
															new AE1155(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_5341;
														}
													}
													else if (num2 != 13002317U)
													{
														if (num2 != 19069980U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1152"))
														{
															goto IL_531B;
														}
														new AE1152(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1201"))
														{
															goto IL_531B;
														}
														new AE1201(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 <= 41156218U)
												{
													if (num2 != 35847599U)
													{
														if (num2 != 41156218U)
														{
															goto IL_531B;
														}
														if (!(text4 == "4261"))
														{
															goto IL_531B;
														}
														new AddAllDamage(num, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1153"))
														{
															goto IL_531B;
														}
														new AE1153(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 52625218U)
												{
													if (num2 != 57933837U)
													{
														if (num2 != 69402837U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1151"))
														{
															goto IL_531B;
														}
														new AE1151(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "4260"))
														{
															goto IL_531B;
														}
														new PetReduceTakeDamage_Passive(probability, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1150"))
													{
														goto IL_531B;
													}
													new AE1150(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 203917979U)
											{
												if (num2 <= 136807503U)
												{
													if (num2 != 120029884U)
													{
														if (num2 != 136807503U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1139"))
														{
															goto IL_531B;
														}
													}
													else
													{
														if (!(text4 == "1138"))
														{
															goto IL_531B;
														}
														new AE1138(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 187140360U)
												{
													if (num2 != 190717139U)
													{
														if (num2 != 203917979U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1135"))
														{
															goto IL_531B;
														}
														new AE1135(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "4554"))
														{
															goto IL_531B;
														}
														new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 100).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1134"))
													{
														goto IL_531B;
													}
													new AE1134(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 246100736U)
											{
												if (num2 != 237620312U)
												{
													if (num2 != 246100736U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1084"))
													{
														goto IL_531B;
													}
													new AE1084(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1127"))
													{
														goto IL_531B;
													}
													new AE1127(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 254397931U)
											{
												if (num2 != 262878355U)
												{
													if (num2 != 271175550U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1125"))
													{
														goto IL_531B;
													}
													new AE1125(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1085"))
													{
														goto IL_531B;
													}
													new AE1085(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1126"))
												{
													goto IL_531B;
												}
												new AE1126(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 329988831U)
										{
											if (num2 <= 304583693U)
											{
												if (num2 <= 287806074U)
												{
													if (num2 != 279655974U)
													{
														if (num2 != 287806074U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1132"))
														{
															goto IL_531B;
														}
														new AE1132(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1086"))
														{
															goto IL_531B;
														}
														new AE1086(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 287953169U)
												{
													if (num2 != 296433593U)
													{
														if (num2 != 304583693U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1133"))
														{
															goto IL_531B;
														}
														new AE1133(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1087"))
														{
															goto IL_531B;
														}
														new AE1087(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1124"))
													{
														goto IL_531B;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 30).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 313211212U)
											{
												if (num2 != 304730788U)
												{
													if (num2 != 313211212U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1080"))
													{
														goto IL_531B;
													}
													new PE1080(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else if (!(text4 == "1123"))
												{
													goto IL_531B;
												}
											}
											else if (num2 != 321508407U)
											{
												if (num2 != 328561808U)
												{
													if (num2 != 329988831U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1081"))
													{
														goto IL_531B;
													}
													new PE1081(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1079"))
													{
														goto IL_531B;
													}
													new PE1079(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1122"))
												{
													goto IL_531B;
												}
												new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 10).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 363544069U)
										{
											if (num2 <= 345339427U)
											{
												if (num2 != 338286026U)
												{
													if (num2 != 345339427U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1078"))
													{
														goto IL_531B;
													}
													new PE1078(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1121"))
													{
														goto IL_531B;
													}
													new PE1121(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 346766450U)
											{
												if (num2 != 355063645U)
												{
													if (num2 != 363544069U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1083"))
													{
														goto IL_531B;
													}
													new AE1083(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1120"))
													{
														goto IL_531B;
													}
													new PE1120(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1082"))
												{
													goto IL_531B;
												}
												new AE1082(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 447432164U)
										{
											if (num2 != 375270948U)
											{
												if (num2 != 447432164U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1088"))
												{
													goto IL_531B;
												}
												new AE1088(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "4559"))
												{
													goto IL_531B;
												}
												new AddBlood(3, num, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 464209783U)
										{
											if (num2 != 479560379U)
											{
												if (num2 != 496337998U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1073"))
												{
													goto IL_531B;
												}
												new PE1073(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1070"))
												{
													goto IL_531B;
												}
												goto IL_533F;
											}
										}
										else
										{
											if (!(text4 == "1089"))
											{
												goto IL_531B;
											}
											new AE1090(coldDown, probability, gameType, num, delay, "1090").Start(this);
											new AE1091(coldDown, probability, gameType, num, delay, "1091").Start(this);
											goto IL_5341;
										}
										new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 20).Start(this);
									}
									else if (num2 <= 936040045U)
									{
										if (num2 <= 580226093U)
										{
											if (num2 <= 546670855U)
											{
												if (num2 <= 529893236U)
												{
													if (num2 != 513115617U)
													{
														if (num2 != 529893236U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1075"))
														{
															goto IL_531B;
														}
														new PE1075(coldDown, probability, gameType, num, delay, text2).Start(this);
													}
													else
													{
														if (!(text4 == "1072"))
														{
															goto IL_531B;
														}
														new PE1072(coldDown, probability, gameType, num, delay, text2).Start(this);
													}
												}
												else if (num2 != 530187426U)
												{
													if (num2 != 546670855U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1074"))
													{
														goto IL_531B;
													}
													new PE1074(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1019"))
													{
														goto IL_531B;
													}
													new AE1019(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else if (num2 <= 550448998U)
											{
												if (num2 != 546965045U)
												{
													if (num2 != 550448998U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1770"))
													{
														goto IL_531B;
													}
													new PetChangeSuit(3, num, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1018"))
													{
														goto IL_531B;
													}
													new AE1018(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else if (num2 != 563448474U)
											{
												if (num2 != 563742664U)
												{
													if (num2 != 580226093U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1076"))
													{
														goto IL_531B;
													}
													new PE1076(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
												else
												{
													if (!(text4 == "1017"))
													{
														goto IL_531B;
													}
													new AE1017(coldDown, probability, gameType, num, delay, text2).Start(this);
												}
											}
											else
											{
												if (!(text4 == "1077"))
												{
													goto IL_531B;
												}
												new PE1077(coldDown, probability, gameType, num, delay, text2).Start(this);
											}
										}
										else
										{
											if (num2 <= 885707188U)
											{
												if (num2 <= 818596712U)
												{
													if (num2 != 751486236U)
													{
														if (num2 != 818596712U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1754"))
														{
															goto IL_531B;
														}
													}
													else
													{
														if (!(text4 == "1758"))
														{
															goto IL_531B;
														}
														new PetProhibitionAddBloodWithFire(3, num, text).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 835521426U)
												{
													if (num2 != 852299045U)
													{
														if (num2 != 885707188U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1750"))
														{
															goto IL_531B;
														}
														new PetShowFireOfResentment(2, num, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1748"))
														{
															goto IL_531B;
														}
														new PetAddMpAfterCount_Passive(text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1749"))
													{
														goto IL_531B;
													}
													new PetBlockUseTrident(2, probability, num, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 902631902U)
											{
												if (num2 != 902484807U)
												{
													if (num2 != 902631902U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1745"))
													{
														goto IL_531B;
													}
													new PetReduceBloodAfterFinish(2, probability, num, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1751"))
													{
														goto IL_531B;
													}
													new PetShowFireOfHatred(2, num, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 919262426U)
											{
												if (num2 != 919409521U)
												{
													if (num2 != 936040045U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1753"))
													{
														goto IL_531B;
													}
												}
												else
												{
													if (!(text4 == "1744"))
													{
														goto IL_531B;
													}
													new PetAddBaseDamage(2, probability, num, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (!(text4 == "1752"))
											{
												goto IL_531B;
											}
											new PetReduceTakeDamageForTeamWithFire(2, probability, num, text2).Start(this);
										}
									}
									else
									{
										if (num2 <= 1239794534U)
										{
											if (num2 <= 1202459705U)
											{
												if (num2 <= 969742378U)
												{
													if (num2 != 936187140U)
													{
														if (num2 != 969742378U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1741"))
														{
															goto IL_531B;
														}
														new PetReduceEnergy(3, probability, num, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1743"))
														{
															goto IL_531B;
														}
														new PetAddMagicAttack(2, probability, num, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 1168904467U)
												{
													if (num2 != 1185682086U)
													{
														if (num2 != 1202459705U)
														{
															goto IL_531B;
														}
														if (!(text4 == "3210"))
														{
															goto IL_531B;
														}
														new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 50).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "3211"))
														{
															goto IL_531B;
														}
														new AE1138(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (!(text4 == "3212"))
												{
													goto IL_531B;
												}
											}
											else if (num2 <= 1219237324U)
											{
												if (num2 != 1206239296U)
												{
													if (num2 != 1219237324U)
													{
														goto IL_531B;
													}
													if (!(text4 == "3217"))
													{
														goto IL_531B;
													}
													new PetDamageAllEnemy(probability, num, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1574"))
													{
														goto IL_531B;
													}
													new PetKungFuSpecialization(probability, num, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 1223016915U)
											{
												if (num2 != 1235867848U)
												{
													if (num2 != 1239794534U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1576"))
													{
														goto IL_531B;
													}
													new GongfuSpecial(num, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "3208"))
													{
														goto IL_531B;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 30).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1575"))
												{
													goto IL_531B;
												}
												new PetKungFuSpecialization_AddMPPassive(probability, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 1306905010U)
										{
											if (num2 <= 1252792562U)
											{
												if (num2 != 1252645467U)
												{
													if (num2 != 1252792562U)
													{
														goto IL_531B;
													}
													if (!(text4 == "3215"))
													{
														goto IL_531B;
													}
												}
												else
												{
													if (!(text4 == "3209"))
													{
														goto IL_531B;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 40).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 1256572153U)
											{
												if (num2 != 1269570181U)
												{
													if (num2 != 1306905010U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1572"))
													{
														goto IL_531B;
													}
													new PetViolentPosture_ReduceBlood(probability, num, text2).Start(this);
													goto IL_5341;
												}
												else if (!(text4 == "3214"))
												{
													goto IL_531B;
												}
											}
											else
											{
												if (!(text4 == "1577"))
												{
													goto IL_531B;
												}
												new PetAttackPosture_ReduceDander(probability, num, text2).Start(this);
												new PetAttackPosture_AddAgilityAndSpeed(probability, num, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 1336680657U)
										{
											if (num2 != 1319903038U)
											{
												if (num2 != 1336680657U)
												{
													goto IL_531B;
												}
												if (!(text4 == "3218"))
												{
													goto IL_531B;
												}
												new PetAddBaseDamageOnPlayerBuffSkill_Passive(text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "3219"))
												{
													goto IL_531B;
												}
												new PetGetshield_Passive(4, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 1690911312U)
										{
											if (num2 != 1707688931U)
											{
												if (num2 != 1724466550U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1213"))
												{
													goto IL_531B;
												}
												new AE1213(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1210"))
												{
													goto IL_531B;
												}
												new AE1210(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "1211"))
											{
												goto IL_531B;
											}
											new AE1211(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										new PetMakeDamagePercent(probability, num, text2).Start(this);
									}
								}
								else
								{
									if (num2 <= 2083452592U)
									{
										if (num2 <= 1942722692U)
										{
											if (num2 <= 1808501740U)
											{
												if (num2 <= 1774799407U)
												{
													if (num2 <= 1741244169U)
													{
														if (num2 != 1735842832U)
														{
															if (num2 != 1741244169U)
															{
																goto IL_531B;
															}
															if (!(text4 == "1212"))
															{
																goto IL_531B;
															}
															new AE1212(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_5341;
														}
														else
														{
															if (!(text4 == "4252"))
															{
																goto IL_531B;
															}
															new PetMakeDamagePercent(probability, num, text2).Start(this);
															goto IL_5341;
														}
													}
													else if (num2 != 1758021788U)
													{
														if (num2 != 1774799407U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1214"))
														{
															goto IL_531B;
														}
														new PE1214(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1215"))
														{
															goto IL_531B;
														}
														new PE1215(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 <= 1791577026U)
												{
													if (num2 != 1774946502U)
													{
														if (num2 != 1791577026U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1217"))
														{
															goto IL_531B;
														}
														new PE1217(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1266"))
														{
															goto IL_531B;
														}
														new AE1266(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 1791724121U)
												{
													if (num2 != 1808354645U)
													{
														if (num2 != 1808501740U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1260"))
														{
															goto IL_531B;
														}
														new AE1260(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1216"))
														{
															goto IL_531B;
														}
														new PE1216(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1267"))
													{
														goto IL_531B;
													}
													new AE1267(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 1858834597U)
											{
												if (num2 <= 1836508546U)
												{
													if (num2 != 1825132264U)
													{
														if (num2 != 1836508546U)
														{
															goto IL_531B;
														}
														if (!(text4 == "4254"))
														{
															goto IL_531B;
														}
														new PetReduceTargetMaxBlood(3, probability, num, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1219"))
														{
															goto IL_531B;
														}
														goto IL_533F;
													}
												}
												else if (num2 != 1841909883U)
												{
													if (num2 != 1842204073U)
													{
														if (num2 != 1858834597U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1263"))
														{
															goto IL_531B;
														}
														new PE1263(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1274"))
														{
															goto IL_531B;
														}
														new PE1274(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1218"))
													{
														goto IL_531B;
													}
													goto IL_533F;
												}
											}
											else if (num2 <= 1892536930U)
											{
												if (num2 != 1875906406U)
												{
													if (num2 != 1892536930U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1271"))
													{
														goto IL_531B;
													}
													new AE1271(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1248"))
													{
														goto IL_531B;
													}
													goto IL_4404;
												}
											}
											else if (num2 != 1892684025U)
											{
												if (num2 != 1909314549U)
												{
													if (num2 != 1942722692U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1268"))
													{
														goto IL_531B;
													}
													new AE1268(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1270"))
													{
														goto IL_531B;
													}
													new AE1270(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1249"))
												{
													goto IL_531B;
												}
												goto IL_4424;
											}
										}
										else if (num2 <= 2048764521U)
										{
											if (num2 <= 2010127358U)
											{
												if (num2 <= 1976572120U)
												{
													if (num2 != 1959500311U)
													{
														if (num2 != 1976572120U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1242"))
														{
															goto IL_531B;
														}
														new PE1242(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1269"))
														{
															goto IL_531B;
														}
														new AE1269(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 1993349739U)
												{
													if (num2 != 1998431664U)
													{
														if (num2 != 2010127358U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1240"))
														{
															goto IL_531B;
														}
														new AE1240(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1170"))
														{
															goto IL_531B;
														}
														new AE1170(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1243"))
													{
														goto IL_531B;
													}
													new AE1243(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 2026904977U)
											{
												if (num2 != 2015209283U)
												{
													if (num2 != 2026904977U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1241"))
													{
														goto IL_531B;
													}
													new PE1241(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1171"))
													{
														goto IL_531B;
													}
													new AE1171(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2031986902U)
											{
												if (num2 != 2043682596U)
												{
													if (num2 != 2048764521U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1173"))
													{
														goto IL_531B;
													}
													new AE1173(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1246"))
													{
														goto IL_531B;
													}
													new AE1246(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1172"))
												{
													goto IL_531B;
												}
												new AE1172(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 2065689235U)
										{
											if (num2 <= 2049750259U)
											{
												if (num2 != 2048911616U)
												{
													if (num2 != 2049750259U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1113"))
													{
														goto IL_531B;
													}
													new PE1113(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1163"))
													{
														goto IL_531B;
													}
													new PE1163(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2060460215U)
											{
												if (num2 != 2065542140U)
												{
													if (num2 != 2065689235U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1162"))
													{
														goto IL_531B;
													}
													new AE1162(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1174"))
													{
														goto IL_531B;
													}
													new AE1174(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1247"))
												{
													goto IL_531B;
												}
												new AE1247(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 2077237834U)
										{
											if (num2 != 2066527878U)
											{
												if (num2 != 2077237834U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1244"))
												{
													goto IL_531B;
												}
												new AE1244(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else if (!(text4 == "1110"))
											{
												goto IL_531B;
											}
										}
										else if (num2 != 2082319759U)
										{
											if (num2 != 2082466854U)
											{
												if (num2 != 2083452592U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1105"))
												{
													goto IL_531B;
												}
												new AE1105(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1161"))
												{
													goto IL_531B;
												}
												new AE1161(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "1175"))
											{
												goto IL_531B;
											}
											new AE1175(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 <= 2218806377U)
									{
										if (num2 <= 2133785449U)
										{
											if (num2 <= 2116860735U)
											{
												if (num2 <= 2100083116U)
												{
													if (num2 != 2099097378U)
													{
														if (num2 != 2100083116U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1116"))
														{
															goto IL_531B;
														}
														new AE1116(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1176"))
														{
															goto IL_531B;
														}
														new AE1176(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 2100230211U)
												{
													if (num2 != 2115874997U)
													{
														if (num2 != 2116860735U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1117"))
														{
															goto IL_531B;
														}
														new AE1117(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1177"))
														{
															goto IL_531B;
														}
														new AE1177(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1104"))
													{
														goto IL_531B;
													}
													new AE1104(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 2132652616U)
											{
												if (num2 != 2117007830U)
												{
													if (num2 != 2132652616U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1178"))
													{
														goto IL_531B;
													}
													new AE1178(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1107"))
													{
														goto IL_531B;
													}
													new PE1107(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2132799711U)
											{
												if (num2 != 2133638354U)
												{
													if (num2 != 2133785449U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1106"))
													{
														goto IL_531B;
													}
													new PE1106(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1114"))
													{
														goto IL_531B;
													}
													new PE1114(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1166"))
												{
													goto IL_531B;
												}
												new PE1166(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (num2 > 2166354949U)
											{
												if (num2 <= 2200748830U)
												{
													if (num2 != 2167340687U)
													{
														if (num2 != 2200748830U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1118"))
														{
															goto IL_531B;
														}
													}
													else
													{
														if (!(text4 == "1100"))
														{
															goto IL_531B;
														}
														new AE1100(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 2202028758U)
												{
													if (num2 != 2217526449U)
													{
														if (num2 != 2218806377U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1199"))
														{
															goto IL_531B;
														}
														new AE1199(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else if (!(text4 == "1119"))
													{
														goto IL_531B;
													}
												}
												else
												{
													if (!(text4 == "1198"))
													{
														goto IL_531B;
													}
													new AE1198(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												new AE1118(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											if (num2 <= 2149577330U)
											{
												if (num2 != 2149430235U)
												{
													if (num2 != 2149577330U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1165"))
													{
														goto IL_531B;
													}
													new PE1165(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1179"))
													{
														goto IL_531B;
													}
													new AE1179(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2150415973U)
											{
												if (num2 != 2150563068U)
												{
													if (num2 != 2166354949U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1164"))
													{
														goto IL_531B;
													}
													new PE1164(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1101"))
													{
														goto IL_531B;
													}
													new AE1101(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1115"))
												{
													goto IL_531B;
												}
												new AE1115(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
									}
									else if (num2 <= 2341331635U)
									{
										if (num2 <= 2302841567U)
										{
											if (num2 <= 2286063948U)
											{
												if (num2 != 2284784020U)
												{
													if (num2 != 2286063948U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1189"))
													{
														goto IL_531B;
													}
												}
												else
												{
													if (!(text4 == "1109"))
													{
														goto IL_531B;
													}
													goto IL_3E1D;
												}
											}
											else if (num2 != 2301561639U)
											{
												if (num2 != 2302694472U)
												{
													if (num2 != 2302841567U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1188"))
													{
														goto IL_531B;
													}
												}
												else
												{
													if (!(text4 == "1192"))
													{
														goto IL_531B;
													}
													new PE1192(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1108"))
												{
													goto IL_531B;
												}
												new PE1108(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											new AE1189(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										if (num2 <= 2324554016U)
										{
											if (num2 != 2319472091U)
											{
												if (num2 != 2324554016U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1040"))
												{
													goto IL_531B;
												}
												new AE1040(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1193"))
												{
													goto IL_531B;
												}
												new PE1193(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 2326837640U)
										{
											if (num2 != 2336249710U)
											{
												if (num2 != 2341331635U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1041"))
												{
													goto IL_531B;
												}
												new AE1041(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1190"))
												{
													goto IL_531B;
												}
												new PE1190(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "4393"))
											{
												goto IL_531B;
											}
											new PetChangeSuit(4, num, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 <= 2359094992U)
									{
										if (num2 <= 2353174424U)
										{
											if (num2 != 2353027329U)
											{
												if (num2 != 2353174424U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1185"))
												{
													goto IL_531B;
												}
												new AE1185(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1191"))
												{
													goto IL_531B;
												}
												new PE1191(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 2358109254U)
										{
											if (num2 != 2358860995U)
											{
												if (num2 != 2359094992U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1022"))
												{
													goto IL_531B;
												}
												new AE1022(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "3182"))
												{
													goto IL_531B;
												}
												new PE3182(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "1042"))
											{
												goto IL_531B;
											}
											new AE1042(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 <= 2369952043U)
									{
										if (num2 != 2369804948U)
										{
											if (num2 != 2369952043U)
											{
												goto IL_531B;
											}
											if (!(text4 == "1184"))
											{
												goto IL_531B;
											}
											new AE1184(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										else
										{
											if (!(text4 == "1196"))
											{
												goto IL_531B;
											}
											new AE1196(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 != 2374886873U)
									{
										if (num2 != 2375638614U)
										{
											if (num2 != 2375872611U)
											{
												goto IL_531B;
											}
											if (!(text4 == "1023"))
											{
												goto IL_531B;
											}
											new AE1023(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										else
										{
											if (!(text4 == "3181"))
											{
												goto IL_531B;
											}
											new PE3181(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else
									{
										if (!(text4 == "1043"))
										{
											goto IL_531B;
										}
										new AE1043(coldDown, probability, gameType, num, delay, text2).Start(this);
										goto IL_5341;
									}
									IL_3E1D:
									new PE1110(coldDown, probability, gameType, num, delay, "1110").Start(this);
								}
								IL_533F:;
							}
							else
							{
								if (num2 <= 2916240372U)
								{
									if (num2 <= 2511226396U)
									{
										if (num2 <= 2441997349U)
										{
											if (num2 <= 2408442111U)
											{
												if (num2 <= 2392416233U)
												{
													if (num2 <= 2386729662U)
													{
														if (num2 != 2386582567U)
														{
															if (num2 != 2386729662U)
															{
																goto IL_531B;
															}
															if (!(text4 == "1187"))
															{
																goto IL_531B;
															}
															new AE1187(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_5341;
														}
														else
														{
															if (!(text4 == "1197"))
															{
																goto IL_531B;
															}
															new AE1197(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_5341;
														}
													}
													else if (num2 != 2391664492U)
													{
														if (num2 != 2392416233U)
														{
															goto IL_531B;
														}
														if (!(text4 == "3180"))
														{
															goto IL_531B;
														}
														new PE3180(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1044"))
														{
															goto IL_531B;
														}
														new AE1044(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 <= 2393948116U)
												{
													if (num2 != 2392650230U)
													{
														if (num2 != 2393948116U)
														{
															goto IL_531B;
														}
														if (!(text4 == "4397"))
														{
															goto IL_531B;
														}
														new PetAddMaxBlood(3, probability, num, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1020"))
														{
															goto IL_531B;
														}
														new AE1020(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 2403360186U)
												{
													if (num2 != 2403507281U)
													{
														if (num2 != 2408442111U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1045"))
														{
															goto IL_531B;
														}
														new AE1045(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1186"))
														{
															goto IL_531B;
														}
														new AE1186(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1194"))
													{
														goto IL_531B;
													}
													new AE1194(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 2425366825U)
											{
												if (num2 <= 2420137805U)
												{
													if (num2 != 2409427849U)
													{
														if (num2 != 2420137805U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1195"))
														{
															goto IL_531B;
														}
														new AE1195(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1021"))
														{
															goto IL_531B;
														}
														new AE1021(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 2420284900U)
												{
													if (num2 != 2425219730U)
													{
														if (num2 != 2425366825U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1050"))
														{
															goto IL_531B;
														}
														new AE1050(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1046"))
														{
															goto IL_531B;
														}
														new AE1046(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1181"))
													{
														goto IL_531B;
													}
													new AE1181(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 2426352563U)
											{
												if (num2 != 2426205468U)
												{
													if (num2 != 2426352563U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1034"))
													{
														goto IL_531B;
													}
													new AE1034(coldDown, probability, gameType, num, delay, "1031").Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1026"))
													{
														goto IL_531B;
													}
													new AE1026(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2427503354U)
											{
												if (num2 != 2437062519U)
												{
													if (num2 != 2441997349U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1047"))
													{
														goto IL_531B;
													}
													new AE1047(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1180"))
													{
														goto IL_531B;
													}
													new AE1180(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "4395"))
												{
													goto IL_531B;
												}
												new PetChangeWhiteTiger(4, num, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 2460893539U)
										{
											if (num2 <= 2453840138U)
											{
												if (num2 <= 2442291539U)
												{
													if (num2 != 2442144444U)
													{
														if (num2 != 2442291539U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1067"))
														{
															goto IL_531B;
														}
														new AE1067(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1057"))
														{
															goto IL_531B;
														}
														new AE1057(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 2442983087U)
												{
													if (num2 != 2444115920U)
													{
														if (num2 != 2453840138U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1183"))
														{
															goto IL_531B;
														}
														new AE1183(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1097"))
														{
															goto IL_531B;
														}
														new AE1097(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1027"))
													{
														goto IL_531B;
													}
													new AE1027(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 2458922063U)
											{
												if (num2 != 2458774968U)
												{
													if (num2 != 2458922063U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1056"))
													{
														goto IL_531B;
													}
													new AE1056(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1048"))
													{
														goto IL_531B;
													}
													new AE1048(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2459069158U)
											{
												if (num2 != 2459760706U)
												{
													if (num2 != 2460893539U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1096"))
													{
														goto IL_531B;
													}
													new AE1096(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1024"))
													{
														goto IL_531B;
													}
													new AE1024(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1064"))
												{
													goto IL_531B;
												}
												new AE1064(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 2477671158U)
										{
											if (num2 <= 2475552587U)
											{
												if (num2 != 2470617757U)
												{
													if (num2 != 2475552587U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1049"))
													{
														goto IL_531B;
													}
													new AE1049(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1182"))
													{
														goto IL_531B;
													}
													new AE1182(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2475699682U)
											{
												if (num2 != 2476538325U)
												{
													if (num2 != 2477671158U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1095"))
													{
														goto IL_531B;
													}
													new AE1095(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1025"))
													{
														goto IL_531B;
													}
													new AE1025(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1055"))
												{
													goto IL_531B;
												}
												new AE1053(coldDown, probability, gameType, num, delay, "1053").Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 2495200518U)
										{
											if (num2 != 2494448777U)
											{
												if (num2 != 2495200518U)
												{
													goto IL_531B;
												}
												if (!(text4 == "3178"))
												{
													goto IL_531B;
												}
											}
											else
											{
												if (!(text4 == "1094"))
												{
													goto IL_531B;
												}
												new AE1094(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 2509402015U)
										{
											if (num2 != 2510240658U)
											{
												if (num2 != 2511226396U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1093"))
												{
													goto IL_531B;
												}
												new AE1093(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1033"))
												{
													goto IL_531B;
												}
												new AE1033(coldDown, probability, gameType, num, delay, "1030").Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "1063"))
											{
												goto IL_531B;
											}
											new AE1063(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else
									{
										if (num2 > 2679901422U)
										{
											if (num2 <= 2748464182U)
											{
												if (num2 <= 2713309565U)
												{
													if (num2 <= 2696531946U)
													{
														if (num2 != 2695780205U)
														{
															if (num2 != 2696531946U)
															{
																goto IL_531B;
															}
															if (!(text4 == "3174"))
															{
																goto IL_531B;
															}
															new PE3174(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_5341;
														}
														else
														{
															if (!(text4 == "1098"))
															{
																goto IL_531B;
															}
															new AE1098(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_5341;
														}
													}
													else if (num2 != 2696679041U)
													{
														if (num2 != 2696998492U)
														{
															if (num2 != 2713309565U)
															{
																goto IL_531B;
															}
															if (!(text4 == "3175"))
															{
																goto IL_531B;
															}
															new AE3175(coldDown, probability, gameType, num, delay, text2).Start(this);
															goto IL_5341;
														}
														else if (!(text4 == "1714"))
														{
															goto IL_531B;
														}
													}
													else
													{
														if (!(text4 == "3166"))
														{
															goto IL_531B;
														}
														new PE3166(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 <= 2714908944U)
												{
													if (num2 != 2713776111U)
													{
														if (num2 != 2714908944U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1765"))
														{
															goto IL_531B;
														}
														new PetAddMpAfterUseSkillCount_Passive(text2).Start(this);
														goto IL_5341;
													}
													else if (!(text4 == "1715"))
													{
														goto IL_531B;
													}
												}
												else if (num2 != 2730553730U)
												{
													if (num2 != 2747331349U)
													{
														if (num2 != 2748464182U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1767"))
														{
															goto IL_531B;
														}
														goto IL_4E46;
													}
													else
													{
														if (!(text4 == "1717"))
														{
															goto IL_531B;
														}
														goto IL_4E18;
													}
												}
												else if (!(text4 == "1716"))
												{
													goto IL_531B;
												}
												new PetShareTakeDamageToTeam(2, probability, num, text2).Start(this);
												goto IL_5341;
											}
											if (num2 <= 2797958396U)
											{
												if (num2 <= 2780886587U)
												{
													if (num2 != 2764108968U)
													{
														if (num2 != 2780886587U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1719"))
														{
															goto IL_531B;
														}
													}
													else if (!(text4 == "1718"))
													{
														goto IL_531B;
													}
												}
												else if (num2 != 2781180777U)
												{
													if (num2 != 2781327872U)
													{
														if (num2 != 2797958396U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1732"))
														{
															goto IL_531B;
														}
														new PetReduceSpeed(2, probability, num, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1729"))
														{
															goto IL_531B;
														}
														new PetChangeWindDirection(num, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1735"))
													{
														goto IL_531B;
													}
													new PetAddMaxBlood(3, probability, num, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 2848291253U)
											{
												if (num2 != 2831513634U)
												{
													if (num2 != 2848291253U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1731"))
													{
														goto IL_531B;
													}
													new PetIceImmunity(2, probability, num, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1730"))
													{
														goto IL_531B;
													}
													new PetAddShootCount(num, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2849424086U)
											{
												if (num2 != 2899609848U)
												{
													if (num2 != 2916240372U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1769"))
													{
														goto IL_531B;
													}
													goto IL_4E46;
												}
												else
												{
													if (!(text4 == "1790"))
													{
														goto IL_531B;
													}
													new PetBlackFlameBomb(2, probability, num, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1789"))
												{
													goto IL_531B;
												}
												new PetAddMpBeginSelfTurn(3, probability, num, text2).Start(this);
												goto IL_5341;
											}
											IL_4E18:
											new PetReduceEnemyBlood(3, probability, num, text2).Start(this);
											goto IL_5341;
										}
										if (num2 <= 2610906372U)
										{
											if (num2 <= 2542810158U)
											{
												if (num2 <= 2526871182U)
												{
													if (num2 != 2511978137U)
													{
														if (num2 != 2526871182U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1028"))
														{
															goto IL_531B;
														}
														new AE1028(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else if (!(text4 == "3179"))
													{
														goto IL_531B;
													}
												}
												else if (num2 != 2527018277U)
												{
													if (num2 != 2528004015U)
													{
														if (num2 != 2542810158U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1059"))
														{
															goto IL_531B;
														}
														new AE1059(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1092"))
														{
															goto IL_531B;
														}
														new AE1092(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "1032"))
													{
														goto IL_531B;
													}
													new AE1032(coldDown, probability, gameType, num, delay, "1029").Start(this);
													goto IL_5341;
												}
											}
											else if (num2 <= 2579235708U)
											{
												if (num2 != 2559587777U)
												{
													if (num2 != 2579235708U)
													{
														goto IL_531B;
													}
													if (!(text4 == "3169"))
													{
														goto IL_531B;
													}
													new AE3169(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1058"))
													{
														goto IL_531B;
													}
													new AE1058(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2595866232U)
											{
												if (num2 != 2596013327U)
												{
													if (num2 != 2610906372U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1039"))
													{
														goto IL_531B;
													}
													new AE1039(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "3168"))
													{
														goto IL_531B;
													}
													new PE3168(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "3172"))
												{
													goto IL_531B;
												}
												new AE3173(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 2646199089U)
										{
											if (num2 <= 2622927441U)
											{
												if (num2 != 2612643851U)
												{
													if (num2 != 2622927441U)
													{
														goto IL_531B;
													}
													if (!(text4 == "4567"))
													{
														goto IL_531B;
													}
													new HeihuBigPower(3, probability, num, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "3173"))
													{
														goto IL_531B;
													}
													new AE3173(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 2627683991U)
											{
												if (num2 != 2629421470U)
												{
													if (num2 != 2646199089U)
													{
														goto IL_531B;
													}
													if (!(text4 == "3171"))
													{
														goto IL_531B;
													}
													new AE3171(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "3170"))
													{
														goto IL_531B;
													}
													new AE3170(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1038"))
												{
													goto IL_531B;
												}
												new AE1038(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 2662976708U)
										{
											if (num2 != 2660400586U)
											{
												if (num2 != 2662976708U)
												{
													goto IL_531B;
												}
												if (!(text4 == "3176"))
												{
													goto IL_531B;
												}
												new AE3176(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1068"))
												{
													goto IL_531B;
												}
												new AE1068(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 2679002586U)
										{
											if (num2 != 2679754327U)
											{
												if (num2 != 2679901422U)
												{
													goto IL_531B;
												}
												if (!(text4 == "3167"))
												{
													goto IL_531B;
												}
												new PE3167(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "3177"))
												{
													goto IL_531B;
												}
												new AE3177(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "1099"))
											{
												goto IL_531B;
											}
											new AE1099(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									new AE3179(coldDown, probability, gameType, num, delay, "3179").Start(this);
									goto IL_5341;
								}
								if (num2 <= 3849681832U)
								{
									if (num2 > 3404254480U)
									{
										if (num2 <= 3605733003U)
										{
											if (num2 <= 3488289670U)
											{
												if (num2 <= 3434030127U)
												{
													if (num2 != 3417252508U)
													{
														if (num2 != 3434030127U)
														{
															goto IL_531B;
														}
														if (!(text4 == "3223"))
														{
															goto IL_531B;
														}
														new PetReduceTakeDamage_Passive(probability, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "3222"))
														{
															goto IL_531B;
														}
														new binglongfuhuo(coldDown, probability, gameType, num, delay, text2).Start(this);
														goto IL_5341;
													}
												}
												else if (num2 != 3450807746U)
												{
													if (num2 != 3488142575U)
													{
														if (num2 != 3488289670U)
														{
															goto IL_531B;
														}
														if (!(text4 == "1558"))
														{
															goto IL_531B;
														}
														new PetStealthPosture_HidePassive(probability, text2).Start(this);
														goto IL_5341;
													}
													else
													{
														if (!(text4 == "1562"))
														{
															goto IL_531B;
														}
														new PetControlPosture_LockDirection(2, probability, num, text2).Start(this);
														goto IL_5341;
													}
												}
												else
												{
													if (!(text4 == "3220"))
													{
														goto IL_531B;
													}
													new PetReduceTakeDamageOnGameStarted_Passive(2, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (num2 > 3572324860U)
												{
													if (num2 != 3588955384U)
													{
														if (num2 != 3589102479U)
														{
															if (num2 != 3605733003U)
															{
																goto IL_531B;
															}
															if (!(text4 == "1553"))
															{
																goto IL_531B;
															}
														}
														else
														{
															if (!(text4 == "1548"))
															{
																goto IL_531B;
															}
															goto IL_508E;
														}
													}
													else if (!(text4 == "1552"))
													{
														goto IL_531B;
													}
													new PetDefensivePosture_CritDamage(2, probability, num, text2).Start(this);
													goto IL_5341;
												}
												if (num2 != 3521697813U)
												{
													if (num2 != 3572324860U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1549"))
													{
														goto IL_531B;
													}
												}
												else
												{
													if (!(text4 == "1560"))
													{
														goto IL_531B;
													}
													new PetClonePosture(1, probability, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
										}
										else if (num2 <= 3672990574U)
										{
											if (num2 <= 3639288241U)
											{
												if (num2 != 3622510622U)
												{
													if (num2 != 3639288241U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1551"))
													{
														goto IL_531B;
													}
													new PetCritRate100(probability, num, text2).Start(this);
													goto IL_5341;
												}
												else if (!(text4 == "1550"))
												{
													goto IL_531B;
												}
											}
											else if (num2 != 3656065860U)
											{
												if (num2 != 3656212955U)
												{
													if (num2 != 3672990574U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1547"))
													{
														goto IL_531B;
													}
													new PetReduceTargetBaseDamage(2, probability, num, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1544"))
													{
														goto IL_531B;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 50).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1556"))
												{
													goto IL_531B;
												}
												new PetAddTurn(120, probability, num, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (num2 <= 3706398717U)
											{
												if (num2 != 3689621098U)
												{
													if (num2 != 3706398717U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1555"))
													{
														goto IL_531B;
													}
												}
												else if (!(text4 == "1554"))
												{
													goto IL_531B;
												}
												new PetDefensivePosture_Damage(2, probability, num, text2).Start(this);
												goto IL_5341;
											}
											if (num2 != 3740101050U)
											{
												if (num2 != 3756878669U)
												{
													if (num2 != 3849681832U)
													{
														goto IL_531B;
													}
													if (!(text4 == "3486"))
													{
														goto IL_531B;
													}
													new PetSpikeEnemy(probability, num, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1542"))
													{
														goto IL_531B;
													}
													new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 30).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1543"))
												{
													goto IL_531B;
												}
												new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 40).Start(this);
												goto IL_5341;
											}
										}
										IL_508E:
										new PetMakeDamagePercent(probability, num, text2).Start(this);
										goto IL_5341;
									}
									if (num2 <= 3145459618U)
									{
										if (num2 <= 2999436919U)
										{
											if (num2 <= 2949104062U)
											{
												if (num2 != 2933017991U)
												{
													if (num2 != 2949104062U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1723"))
													{
														goto IL_531B;
													}
												}
												else
												{
													if (!(text4 == "1768"))
													{
														goto IL_531B;
													}
													goto IL_4E46;
												}
											}
											else if (num2 != 2982659300U)
											{
												if (num2 != 2999436919U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1724"))
												{
													goto IL_531B;
												}
											}
											else if (!(text4 == "1725"))
											{
												goto IL_531B;
											}
											new PetAddDamageOnPlayerAboveOrBellowOfMe_Passive(probability, text2).Start(this);
											goto IL_5341;
										}
										if (num2 <= 3032992157U)
										{
											if (num2 != 3016214538U)
											{
												if (num2 != 3032992157U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1726"))
												{
													goto IL_531B;
												}
											}
											else if (!(text4 == "1727"))
											{
												goto IL_531B;
											}
											new PetChangeDamageWind(num, text2).Start(this);
											goto IL_5341;
										}
										if (num2 != 3061424428U)
										{
											if (num2 != 3066570302U)
											{
												if (num2 != 3145459618U)
												{
													goto IL_531B;
												}
												if (!(text4 == "4849"))
												{
													goto IL_531B;
												}
												new PE4849(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1820"))
												{
													goto IL_531B;
												}
												new ShowPic(text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "4858"))
											{
												goto IL_531B;
											}
											new AE4858(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 <= 3229200618U)
									{
										if (num2 <= 3162237237U)
										{
											if (num2 != 3162090142U)
											{
												if (num2 != 3162237237U)
												{
													goto IL_531B;
												}
												if (!(text4 == "4848"))
												{
													goto IL_531B;
												}
												new AE4848(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "4856"))
												{
													goto IL_531B;
												}
												new AE4856(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 3178867761U)
										{
											if (num2 != 3212570094U)
											{
												if (num2 != 3229200618U)
												{
													goto IL_531B;
												}
												if (!(text4 == "4852"))
												{
													goto IL_531B;
												}
												new AE4852(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "4845"))
												{
													goto IL_531B;
												}
												new AE4845(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "4857"))
											{
												goto IL_531B;
											}
											new AE4857(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 <= 3245978237U)
									{
										if (num2 != 3229347713U)
										{
											if (num2 != 3245978237U)
											{
												goto IL_531B;
											}
											if (!(text4 == "4853"))
											{
												goto IL_531B;
											}
											new PE4853(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										else
										{
											if (!(text4 == "4844"))
											{
												goto IL_531B;
											}
											new AE4844(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 != 3246125332U)
									{
										if (num2 != 3383697270U)
										{
											if (num2 != 3404254480U)
											{
												goto IL_531B;
											}
											if (!(text4 == "1567"))
											{
												goto IL_531B;
											}
											new PetViolentPosture_ReduceEnemyBaseGuard(1, probability, num, text2).Start(this);
											goto IL_5341;
										}
										else
										{
											if (!(text4 == "3224"))
											{
												goto IL_531B;
											}
											new PetReduceTakeCritDamage(-1, probability, num, text2).Start(this);
											goto IL_5341;
										}
									}
									else
									{
										if (!(text4 == "4843"))
										{
											goto IL_531B;
										}
										new PetPassiveSkills(coldDown, probability, gameType, num, delay, text2, 50).Start(this);
										goto IL_5341;
									}
								}
								else if (num2 <= 4190232090U)
								{
									if (num2 <= 4124254447U)
									{
										if (num2 <= 4089566376U)
										{
											if (num2 <= 3972123043U)
											{
												if (num2 != 3955345424U)
												{
													if (num2 != 3972123043U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1229"))
													{
														goto IL_531B;
													}
													new AE1229(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1228"))
													{
														goto IL_531B;
													}
													new AE1228(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 4039380614U)
											{
												if (num2 != 4056158233U)
												{
													if (num2 != 4089566376U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1220"))
													{
														goto IL_531B;
													}
													new AE1220(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1238"))
													{
														goto IL_531B;
													}
													new AE1238(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else
											{
												if (!(text4 == "1239"))
												{
													goto IL_531B;
												}
												new AE1239(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 4107476828U)
										{
											if (num2 != 4106343995U)
											{
												if (num2 != 4107476828U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1259"))
												{
													goto IL_531B;
												}
												new AE1259(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1221"))
												{
													goto IL_531B;
												}
												new AE1221(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 4117867372U)
										{
											if (num2 != 4123121614U)
											{
												if (num2 != 4124254447U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1258"))
												{
													goto IL_531B;
												}
												new AE1258(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1222"))
												{
													goto IL_531B;
												}
												new AE1222(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "4278"))
											{
												goto IL_531B;
											}
											new CE4278(1, num, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 <= 4156971042U)
									{
										if (num2 <= 4140046328U)
										{
											if (num2 != 4139899233U)
											{
												if (num2 != 4140046328U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1233"))
												{
													goto IL_531B;
												}
												goto IL_4424;
											}
											else
											{
												if (!(text4 == "1223"))
												{
													goto IL_531B;
												}
												new PE1223(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 4156676852U)
										{
											if (num2 != 4156823947U)
											{
												if (num2 != 4156971042U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1208"))
												{
													goto IL_531B;
												}
												new AE1208(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1232"))
												{
													goto IL_531B;
												}
												goto IL_4404;
											}
										}
										else
										{
											if (!(text4 == "1224"))
											{
												goto IL_531B;
											}
											new AE1224(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 <= 4173601566U)
									{
										if (num2 != 4173454471U)
										{
											if (num2 != 4173601566U)
											{
												goto IL_531B;
											}
											if (!(text4 == "1231"))
											{
												goto IL_531B;
											}
											new AE1231(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										else
										{
											if (!(text4 == "1225"))
											{
												goto IL_531B;
											}
											new AE1225(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 != 4173748661U)
									{
										if (num2 != 4174587304U)
										{
											if (num2 != 4190232090U)
											{
												goto IL_531B;
											}
											if (!(text4 == "1226"))
											{
												goto IL_531B;
											}
											new AE1226(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										else
										{
											if (!(text4 == "1255"))
											{
												goto IL_531B;
											}
											new AE1255(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else
									{
										if (!(text4 == "1209"))
										{
											goto IL_531B;
										}
										new AE1209(coldDown, probability, gameType, num, delay, text2).Start(this);
										goto IL_5341;
									}
								}
								else
								{
									if (num2 > 4224920161U)
									{
										if (num2 <= 4257636756U)
										{
											if (num2 <= 4240859137U)
											{
												if (num2 != 4240712042U)
												{
													if (num2 != 4240859137U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1205"))
													{
														goto IL_531B;
													}
													new AE1205(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else
												{
													if (!(text4 == "1235"))
													{
														goto IL_531B;
													}
													new PE1235(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
											}
											else if (num2 != 4246926800U)
											{
												if (num2 != 4257489661U)
												{
													if (num2 != 4257636756U)
													{
														goto IL_531B;
													}
													if (!(text4 == "1202"))
													{
														goto IL_531B;
													}
													new AE1202(coldDown, probability, gameType, num, delay, text2).Start(this);
													goto IL_5341;
												}
												else if (!(text4 == "1234"))
												{
													goto IL_531B;
												}
											}
											else
											{
												if (!(text4 == "1156"))
												{
													goto IL_531B;
												}
												new AE1156(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 <= 4274414375U)
										{
											if (num2 != 4258475399U)
											{
												if (num2 != 4274414375U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1203"))
												{
													goto IL_531B;
												}
												new AE1203(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else if (!(text4 == "1250"))
											{
												goto IL_531B;
											}
										}
										else if (num2 != 4275253018U)
										{
											if (num2 != 4280482038U)
											{
												if (num2 != 4291191994U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1200"))
												{
													goto IL_531B;
												}
												new PE1200(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1154"))
												{
													goto IL_531B;
												}
												new AE1154(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "1253"))
											{
												goto IL_531B;
											}
											new PE1253(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										new AE1250(coldDown, probability, gameType, num, delay, "1250").Start(this);
										goto IL_5341;
									}
									if (num2 <= 4207156804U)
									{
										if (num2 <= 4190526280U)
										{
											if (num2 != 4190379185U)
											{
												if (num2 != 4190526280U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1206"))
												{
													goto IL_531B;
												}
												new AE1206(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1230"))
												{
													goto IL_531B;
												}
												new AE1230(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else if (num2 != 4191364923U)
										{
											if (num2 != 4207009709U)
											{
												if (num2 != 4207156804U)
												{
													goto IL_531B;
												}
												if (!(text4 == "1237"))
												{
													goto IL_531B;
												}
												new PE1237(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
											else
											{
												if (!(text4 == "1227"))
												{
													goto IL_531B;
												}
												new AE1227(coldDown, probability, gameType, num, delay, text2).Start(this);
												goto IL_5341;
											}
										}
										else
										{
											if (!(text4 == "1254"))
											{
												goto IL_531B;
											}
											new AE1254(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 <= 4208142542U)
									{
										if (num2 != 4207303899U)
										{
											if (num2 != 4208142542U)
											{
												goto IL_531B;
											}
											if (!(text4 == "1257"))
											{
												goto IL_531B;
											}
											new AE1257(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										else
										{
											if (!(text4 == "1207"))
											{
												goto IL_531B;
											}
											new AE1207(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else if (num2 != 4223934423U)
									{
										if (num2 != 4224081518U)
										{
											if (num2 != 4224920161U)
											{
												goto IL_531B;
											}
											if (!(text4 == "1256"))
											{
												goto IL_531B;
											}
											new AE1256(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
										else
										{
											if (!(text4 == "1204"))
											{
												goto IL_531B;
											}
											new AE1204(coldDown, probability, gameType, num, delay, text2).Start(this);
											goto IL_5341;
										}
									}
									else
									{
										if (!(text4 == "1236"))
										{
											goto IL_531B;
										}
										new PE1236(coldDown, probability, gameType, num, delay, text2).Start(this);
										goto IL_5341;
									}
								}
								IL_4E46:
								new PetReduceTakeDamageEquip(probability, text2).Start(this);
							}
							IL_5341:
							goto IL_5342;
							IL_4404:
							new AE1248(coldDown, probability, gameType, num, delay, "1248").Start(this);
							goto IL_5341;
							IL_4424:
							new AE1249(coldDown, probability, gameType, num, delay, "1249").Start(this);
							goto IL_5341;
							IL_531B:
							Console.WriteLine("没有找到宠物技能: " + text2 + ", 宠物名字: " + this.m_pet.Name);
						}
						IL_5342:;
					}
				}
			}
		}

		// Token: 0x14000049 RID: 73
		// (add) Token: 0x060036D7 RID: 14039 RVA: 0x000D9BE4 File Offset: 0x000D7DE4
		// (remove) Token: 0x060036D8 RID: 14040 RVA: 0x000D9C1C File Offset: 0x000D7E1C
		[field: DebuggerBrowsable(DebuggerBrowsableState.Never)]
		public event PlayerEventHandle PlayerBuffSkillHorse;

		// Token: 0x060036D9 RID: 14041 RVA: 0x000D9C54 File Offset: 0x000D7E54
		public UserBufferInfo GetFightBuffByType(eBuffType buff)
		{
			foreach (UserBufferInfo userBufferInfo in this.m_player.FightBuffs)
			{
				bool flag = userBufferInfo.Type == (int)buff;
				if (flag)
				{
					return userBufferInfo;
				}
			}
			return null;
		}

		// Token: 0x060036DA RID: 14042 RVA: 0x000D9CC4 File Offset: 0x000D7EC4
		public bool ReduceEnergy(int value)
		{
			bool flag = value > this.m_energy;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				this.m_energy -= value;
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x060036DB RID: 14043 RVA: 0x000D9CF8 File Offset: 0x000D7EF8
		public bool AddEnergy(int value)
		{
			this.m_energy += value;
			return true;
		}

		// Token: 0x060036DC RID: 14044 RVA: 0x000D9D1C File Offset: 0x000D7F1C
		public override bool TakeDamage(Living source, ref int damageAmount, ref int criticalAmount, string msg)
		{
			bool flag = (source == this || source.Team == base.Team) && damageAmount + criticalAmount >= this.m_blood;
			if (flag)
			{
				damageAmount = this.m_blood - 1;
				criticalAmount = 0;
			}
			bool flag2 = base.TakeDamage(source, ref damageAmount, ref criticalAmount, msg);
			bool isLiving = base.IsLiving;
			if (isLiving)
			{
				base.AddDander(damageAmount * 500 / base.MaxBlood);
				bool flag3 = !base.Game.IsSpecialPVE() && base.Blood < base.MaxBlood / 100 * 30;
				if (flag3)
				{
					UserBufferInfo fightBuffByType = this.GetFightBuffByType(eBuffType.Save_Life);
					bool flag4 = fightBuffByType != null && this.m_player.UsePayBuff(eBuffType.Save_Life) && this.m_game.RoomType == eRoomType.Dungeon;
					if (flag4)
					{
						int num = base.MaxBlood / 100 * fightBuffByType.Value;
						this.AddBlood(num);
						this.m_game.SendEquipEffect(this, LanguageMgr.GetTranslation("GameServer.PayBuff.ReLife.UseNotice", new object[]
						{
							this.PlayerDetail.PlayerCharacter.NickName,
							num
						}));
					}
				}
			}
			return flag2;
		}

		// Token: 0x060036DD RID: 14045 RVA: 0x000D9E58 File Offset: 0x000D8058
		public void UseSpecialSkill()
		{
			bool flag = base.PetEffects.CurrentUseSkill != 0;
			if (flag)
			{
				this.m_game.SendGameUpdateDander(this);
			}
			else
			{
				bool flag2 = base.Dander >= 200;
				if (flag2)
				{
					this.SetBall(this.m_spBallId, true);
					this.m_ballCount = this.m_currentBall.Amount;
					base.SetDander(0);
					this.OnPlayerUseDander();
				}
			}
		}

		// Token: 0x060036DE RID: 14046 RVA: 0x0001953D File Offset: 0x0001773D
		public void SetBall(int ballId)
		{
			this.SetBall(ballId, false);
		}

		// Token: 0x060036DF RID: 14047 RVA: 0x000D9ED0 File Offset: 0x000D80D0
		public void SetBall(int ballId, bool special)
		{
			bool flag = ballId != this.m_currentBall.ID;
			if (flag)
			{
				bool flag2 = BallMgr.FindBall(ballId) != null;
				if (flag2)
				{
					this.m_currentBall = BallMgr.FindBall(ballId);
				}
				this.m_game.SendGameUpdateBall(this, special);
			}
		}

		// Token: 0x060036E0 RID: 14048 RVA: 0x000D9F20 File Offset: 0x000D8120
		public void SetCurrentWeapon(ItemInfo item)
		{
			this.m_weapon = item;
			BombConfigInfo bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.TemplateID);
			bool flag = this.m_weapon.GoldValidDate();
			if (flag)
			{
				bombConfigInfo = BallConfigMgr.FindBall(this.m_weapon.GoldEquip.TemplateID);
			}
			bool flag2 = this.ChangeSpecialBall > 0;
			if (flag2)
			{
				bombConfigInfo = BallConfigMgr.FindBall(70396);
			}
			this.m_mainBallId = bombConfigInfo.Common;
			this.m_spBallId = bombConfigInfo.Special;
			this.m_spsBallId = bombConfigInfo.SpecialII;
			this.m_AddWoundBallId = bombConfigInfo.CommonAddWound;
			this.m_MultiBallId = bombConfigInfo.CommonMultiBall;
			this.SetBall(this.m_mainBallId);
		}

		// Token: 0x060036E1 RID: 14049 RVA: 0x000D9FD0 File Offset: 0x000D81D0
		public override void StartMoving()
		{
			bool flag = this.m_map == null;
			if (!flag)
			{
				Point point = this.m_map.FindYLineNotEmptyPointDown(this.m_x, this.m_y);
				bool isEmpty = point.IsEmpty;
				if (isEmpty)
				{
					bool flag2 = this.m_map.Ground != null;
					if (flag2)
					{
						this.m_y = this.m_map.Ground.Height;
					}
				}
				else
				{
					this.m_x = point.X;
					this.m_y = point.Y;
				}
				bool isEmpty2 = point.IsEmpty;
				if (isEmpty2)
				{
					this.m_syncAtTime = false;
					this.Die();
					bool flag3 = base.Game.CurrentLiving != this && base.Game.CurrentLiving is Player && this != null && base.Team != base.Game.CurrentLiving.Team;
					if (flag3)
					{
						Player player = base.Game.CurrentLiving as Player;
						player.PlayerDetail.OnKillingLiving(this.m_game, 1, base.Id, base.IsLiving, 0);
						base.Game.CurrentLiving.TotalKill++;
						player.CalculatePlayerOffer(this);
					}
				}
			}
		}

		// Token: 0x060036E2 RID: 14050 RVA: 0x000DA11C File Offset: 0x000D831C
		public override void StartMoving(int delay, int speed)
		{
			bool flag = this.m_map == null;
			if (!flag)
			{
				Point point = this.m_map.FindYLineNotEmptyPointDown(this.m_x, this.m_y);
				bool isEmpty = point.IsEmpty;
				if (isEmpty)
				{
					this.m_y = this.m_map.Ground.Height;
				}
				else
				{
					this.m_x = point.X;
					this.m_y = point.Y;
				}
				base.StartMoving(delay, speed);
				bool isEmpty2 = point.IsEmpty;
				if (isEmpty2)
				{
					this.m_syncAtTime = false;
					this.Die();
					bool flag2 = base.Game.CurrentLiving != this && base.Game.CurrentLiving is Player && this != null && base.Team != base.Game.CurrentLiving.Team;
					if (flag2)
					{
						Player player = base.Game.CurrentLiving as Player;
						player.PlayerDetail.OnKillingLiving(this.m_game, 1, base.Id, base.IsLiving, 0);
						base.Game.CurrentLiving.TotalKill++;
						player.CalculatePlayerOffer(this);
					}
				}
			}
		}

		// Token: 0x060036E3 RID: 14051 RVA: 0x000DA25C File Offset: 0x000D845C
		public void StartSpeedMult(int x, int y, int delay)
		{
			bool flag = (base.FightBuffers.CardDoNotMoveLv1 > 0 || base.FightBuffers.CardDoNotMoveLv2 > 0 || base.FightBuffers.CardDoNotMoveLv3 > 0 || base.FightBuffers.CardDoNotMoveLv4 > 0) && (base.Game as PVEGame).Info.ID == 5;
			if (flag)
			{
				x = this.X;
			}
			Point point = new Point(x - this.X, y - this.Y);
			this.m_game.AddAction(new PlayerSpeedMultAction(this, new Point(this.X + point.X, this.Y + point.Y), delay));
		}

		// Token: 0x060036E4 RID: 14052 RVA: 0x000DA318 File Offset: 0x000D8518
		public void StartGhostMoving()
		{
			bool flag = !this.TargetPoint.IsEmpty;
			if (flag)
			{
				Point point = new Point(this.TargetPoint.X - this.X, this.TargetPoint.Y - this.Y);
				Point targetPoint = this.TargetPoint;
				bool flag2 = point.Length() > 160.0;
				if (flag2)
				{
					point.Normalize(160);
				}
				this.m_game.AddAction(new GhostMoveAction(this, new Point(this.X + point.X, this.Y + point.Y)));
			}
		}

		// Token: 0x060036E5 RID: 14053 RVA: 0x000DA3C8 File Offset: 0x000D85C8
		public override void SetXY(int x, int y)
		{
			bool flag = this.m_x == x && this.m_y == y;
			if (!flag)
			{
				int num = (int)((double)Math.Abs(x - this.m_x) * ((double)this.PowerRatio / 100.0));
				this.m_x = x;
				this.m_y = y;
				bool isLiving = base.IsLiving;
				if (isLiving)
				{
					bool flag2 = this.m_game.IsPVE();
					if (flag2)
					{
						((PVEGame)this.m_game).OnMoving();
					}
					bool flag3 = !this.LimitEnergy;
					if (flag3)
					{
						this.ReduceEnergy(num);
						this.OnPlayerMoving();
					}
					bool flag4 = this.isLockEmery;
					if (flag4)
					{
						this.ReduceEnergy(0);
					}
				}
				else
				{
					bool flag5 = this.m_map == null;
					if (!flag5)
					{
						Rectangle rect = this.m_rect;
						rect.Offset(this.m_x, this.m_y);
						Physics[] array = this.m_map.FindPhysicalObjects(rect, this);
						Physics[] array2 = array;
						foreach (Physics physics in array2)
						{
							bool flag6 = physics is SimpleBox;
							if (flag6)
							{
								SimpleBox simpleBox = physics as SimpleBox;
								this.PickBox(simpleBox);
								this.OpenBox(simpleBox.Id);
							}
						}
					}
				}
			}
		}

		// Token: 0x060036E6 RID: 14054 RVA: 0x000DA528 File Offset: 0x000D8728
		public override void Die()
		{
			bool isLiving = base.IsLiving;
			if (isLiving)
			{
				this.m_y -= 70;
				base.Die();
			}
		}

		// Token: 0x060036E7 RID: 14055 RVA: 0x00019549 File Offset: 0x00017749
		public void StartRotate(int rotation, int speed, string endPlay, int delay)
		{
			this.m_game.AddAction(new LivingRotateTurnAction(this, rotation, speed, endPlay, delay));
		}

		// Token: 0x060036E8 RID: 14056 RVA: 0x00019563 File Offset: 0x00017763
		public override void PickBox(SimpleBox box)
		{
			this.m_tempBoxes.Add(box);
			base.PickBox(box);
		}

		// Token: 0x060036E9 RID: 14057 RVA: 0x000DA558 File Offset: 0x000D8758
		public void OpenBox(int boxId)
		{
			SimpleBox simpleBox = null;
			foreach (object obj in this.m_tempBoxes)
			{
				SimpleBox simpleBox2 = (SimpleBox)obj;
				bool flag = simpleBox2.Id == boxId;
				if (flag)
				{
					simpleBox = simpleBox2;
					break;
				}
			}
			bool flag2 = simpleBox == null || simpleBox.Item == null;
			if (!flag2)
			{
				ItemInfo item = simpleBox.Item;
				int templateID = item.TemplateID;
				int num = templateID;
				if (num != -300)
				{
					if (num != -200)
					{
						if (num != -100)
						{
							bool flag3 = item.Template.CategoryID == 10;
							if (flag3)
							{
								this.m_player.AddTemplate(item, eBageType.FightBag, item.Count);
							}
							else
							{
								this.m_player.AddTemplate(item, eBageType.TempBag, item.Count);
							}
						}
						else
						{
							this.m_player.AddGold(item.Count);
						}
					}
					else
					{
						this.m_player.AddMoney(item.Count);
						this.m_player.LogAddMoney(AddMoneyType.Box, AddMoneyType.Box_Open, this.m_player.PlayerCharacter.ID, item.Count, this.m_player.PlayerCharacter.Money);
					}
				}
				else
				{
					this.m_player.AddGiftToken(item.Count);
				}
				this.m_tempBoxes.Remove(simpleBox);
			}
		}

		// Token: 0x060036EA RID: 14058 RVA: 0x000DA6E0 File Offset: 0x000D88E0
		public override void PrepareNewTurn()
		{
			bool currentIsHitTarget = this.CurrentIsHitTarget;
			if (currentIsHitTarget)
			{
				this.TotalHitTargetCount++;
			}
			this.m_energy = (int)this.Agility / 30 + 240;
			bool flag = base.FightBuffers.ConsortionAddEnergy > 0;
			if (flag)
			{
				this.m_energy += base.FightBuffers.ConsortionAddEnergy;
			}
			bool limitEnergy = this.LimitEnergy;
			if (limitEnergy)
			{
				this.m_energy = this.TotalCureEnergy;
			}
			this.m_shootCount = 1;
			this.m_ballCount = 1;
			this.AttackInformation = true;
			this.DefenceInformation = true;
			this.AttackEffectTrigger = false;
			this.DefenceEffectTrigger = false;
			base.PetEffects.CurrentUseSkill = 0;
			base.PetEffects.Delay = 500;
			base.SpecialSkillDelay = 0;
			this.EffectTrigger = false;
			this.PetEffectTrigger = false;
			base.PetEffects.DisibleActiveSkill = false;
			this.SetCurrentWeapon(this.PlayerDetail.MainWeapon);
			bool flag2 = this.m_currentBall.ID != this.m_mainBallId;
			if (flag2)
			{
				this.m_currentBall = BallMgr.FindBall(this.m_mainBallId);
			}
			bool flag3 = !base.IsLiving;
			if (flag3)
			{
				this.SoulPropCount = 0;
				this.StartGhostMoving();
				this.TargetPoint = Point.Empty;
			}
			base.PrepareNewTurn();
		}

		// Token: 0x060036EB RID: 14059 RVA: 0x000DA83C File Offset: 0x000D8A3C
		public override void PrepareSelfTurn()
		{
			this.DefaultDelay = this.m_delay;
			this._flyCoolDown--;
			base.PetEffects.BallType = 0;
			bool flag = base.IsFrost || base.BlockTurn;
			if (flag)
			{
				base.AddDelay(this.GetTurnDelay());
			}
			bool flag2 = base.FightBuffers.CardTurnAddDanderLv1 > 0;
			if (flag2)
			{
				base.AddDander(base.FightBuffers.CardTurnAddDanderLv1);
			}
			bool flag3 = base.FightBuffers.CardTurnAddDanderLv2 > 0;
			if (flag3)
			{
				base.AddDander(base.FightBuffers.CardTurnAddDanderLv2);
			}
			bool flag4 = base.FightBuffers.CardTurnAddDanderLv3 > 0;
			if (flag4)
			{
				base.AddDander(base.FightBuffers.CardTurnAddDanderLv3);
			}
			bool flag5 = base.FightBuffers.CardTurnAddDanderLv4 > 0;
			if (flag5)
			{
				base.AddDander(base.FightBuffers.CardTurnAddDanderLv4);
			}
			this.m_game.SendRoundOneEnd(this);
			this.jaUsouSkill = false;
			base.PrepareSelfTurn();
			bool flag6 = this.m_pet == null;
			if (!flag6)
			{
				foreach (int num in this._petSkillCd.Keys)
				{
					bool flag7 = this._petSkillCd[num].Turn > 0;
					if (flag7)
					{
						PetSkillInfo petSkillInfo = this._petSkillCd[num];
						int turn = petSkillInfo.Turn;
						petSkillInfo.Turn = turn - 1;
					}
				}
			}
		}

		// Token: 0x060036EC RID: 14060 RVA: 0x000DA9E4 File Offset: 0x000D8BE4
		public override void CollidedByObject(Physics phy)
		{
			base.CollidedByObject(phy);
			bool flag = phy is SimpleBomb;
			if (flag)
			{
				this.OnCollidedByObject();
			}
		}

		// Token: 0x060036ED RID: 14061 RVA: 0x000DAA10 File Offset: 0x000D8C10
		public override void StartAttacking()
		{
			bool flag = !base.IsAttacking;
			if (flag)
			{
				base.AddDelay(this.GetTurnDelay());
				base.StartAttacking();
			}
		}

		// Token: 0x060036EE RID: 14062 RVA: 0x000DAA44 File Offset: 0x000D8C44
		public override void Skip(int spendTime)
		{
			bool isAttacking = base.IsAttacking;
			if (isAttacking)
			{
				base.Game.SendSkipNext(this);
				this.m_prop.Clear();
				base.AddDelay(100);
				base.AddDander(40);
				base.AddPetMP(10);
				this.OnPlayerSkip();
				base.Skip(spendTime);
			}
		}

		// Token: 0x060036EF RID: 14063 RVA: 0x0001957B File Offset: 0x0001777B
		public void SkipAttack()
		{
			base.Game.SendSkipNext(this);
			this.m_prop.Clear();
			base.AddDelay(100);
			base.Skip(1000);
		}

		// Token: 0x060036F0 RID: 14064 RVA: 0x000DAAA4 File Offset: 0x000D8CA4
		public void PrepareShoot(byte speedTime)
		{
			int turnWaitTime = this.m_game.GetTurnWaitTime();
			int num = (((int)speedTime > turnWaitTime) ? turnWaitTime : ((int)speedTime));
			base.AddDelay(num * 20);
			this.TotalShootCount++;
		}

		// Token: 0x060036F1 RID: 14065 RVA: 0x000DAAE0 File Offset: 0x000D8CE0
		public bool Shoot(int x, int y, int force, int angle)
		{
			bool yhm_UseSkillPetWithProp = this.YHM_UseSkillPetWithProp;
			if (yhm_UseSkillPetWithProp)
			{
				this.m_shootCount = 1;
				this.m_ballCount = 1;
				this.YHM_UseSkillPetWithProp = false;
			}
			bool flag = this.m_shootCount > 0;
			if (flag)
			{
				this.EffectTrigger = false;
				this.OnPlayerShoot();
				int num = this.m_currentBall.ID;
				bool flag2 = !this.IsSpecialSkill && !this.IsBlackFlame;
				if (flag2)
				{
					this.buffs = new int[9];
					foreach (int num2 in this.Prop)
					{
						int num3 = num2;
						if (num3 != 10001)
						{
							if (num3 != 10004)
							{
								if (num3 == 10007)
								{
									this.buffs[7]++;
								}
							}
							else
							{
								this.buffs[4]++;
							}
						}
						else
						{
							this.buffs[1]++;
						}
					}
					bool flag3 = this.buffs[1] >= 2;
					if (flag3)
					{
						num = this.m_MultiBallId;
					}
					else
					{
						bool flag4 = this.buffs[4] >= 2;
						if (flag4)
						{
							num = this.m_AddWoundBallId;
						}
						else
						{
							bool flag5 = this.buffs[7] >= 4;
							if (flag5)
							{
								num = this.m_spsBallId;
							}
						}
					}
				}
				bool isSpecialSkill = this.IsSpecialSkill;
				if (isSpecialSkill)
				{
					this.ControlBall = false;
					base.SpecialSkillDelay = 2000;
				}
				bool flag6 = this.CurrentBall.ID != 1 && this.CurrentBall.ID != 64 && this.CurrentBall.ID != 3;
				if (flag6)
				{
					this.OnBeforePlayerShoot(this.CurrentBall.ID);
				}
				this.OnPlayerAnyShellThrow();
				bool flag7 = base.ShootImp(num, x, y, force, angle, this.m_ballCount, this.ShootCount);
				if (flag7)
				{
					bool flag8 = num == 4;
					if (flag8)
					{
						BaseGame game = this.m_game;
						eAcrobaciaType eAcrobaciaType = eAcrobaciaType.ExplosaoNucleardoSuperHomem;
						int idAcrobacias = this.IdAcrobacias;
						this.IdAcrobacias = idAcrobacias + 1;
						game.AddAction(new FightAchievementAction(this, eAcrobaciaType, idAcrobacias, 1200));
					}
					this.m_shootCount--;
					bool flag9 = this.m_shootCount <= 0 || !base.IsLiving;
					if (flag9)
					{
						this.StopAttacking();
						base.AddDelay(this.m_currentBall.Delay + this.m_weapon.Template.Property8);
						base.AddDander(40);
						base.AddPetMP(10);
						this.m_prop.Clear();
						bool canGetProp = this.CanGetProp;
						if (canGetProp)
						{
							SpecialItemDataInfo specialItemDataInfo = new SpecialItemDataInfo();
							List<ItemInfo> list = null;
							bool flag10 = DropInventory.FireDrop(this.m_game.RoomType, ref list) && list != null;
							if (flag10)
							{
								foreach (ItemInfo itemInfo in list)
								{
									ItemInfo.FindSpecialItemInfo(itemInfo, specialItemDataInfo);
									bool flag11 = itemInfo != null;
									if (flag11)
									{
										int templateID = itemInfo.TemplateID;
										this.PlayerDetail.AddTemplate(itemInfo, eBageType.FightBag, itemInfo.Count);
									}
								}
								this.PlayerDetail.AddGold(specialItemDataInfo.Gold);
								this.PlayerDetail.AddMoney(specialItemDataInfo.Money);
								this.PlayerDetail.LogAddMoney(AddMoneyType.Game, AddMoneyType.Game_Shoot, this.PlayerDetail.PlayerCharacter.ID, specialItemDataInfo.Money, this.PlayerDetail.PlayerCharacter.Money);
								this.PlayerDetail.AddGiftToken(specialItemDataInfo.GiftToken);
							}
						}
						this.OnPlayerCompleteShoot();
					}
					this.SendAttackInformation();
					this.OnAfterPlayerShoot();
					return true;
				}
			}
			return false;
		}

		// Token: 0x060036F2 RID: 14066 RVA: 0x000195AC File Offset: 0x000177AC
		public void unlockProp(int templateid)
		{
			this.propsBloqueados.Remove(templateid);
		}

		// Token: 0x060036F3 RID: 14067 RVA: 0x000DAEF4 File Offset: 0x000D90F4
		public void lockProp(int templateid)
		{
			bool flag = !this.propsBloqueados.Contains(templateid);
			if (flag)
			{
				this.propsBloqueados.Add(templateid);
			}
		}

		// Token: 0x060036F4 RID: 14068 RVA: 0x000DAF24 File Offset: 0x000D9124
		public bool CanUseItem(ItemTemplateInfo item)
		{
			bool flag = item == null;
			bool flag2;
			if (flag)
			{
				flag2 = false;
			}
			else
			{
				bool isAutoBot = this.PlayerDetail.PlayerCharacter.IsAutoBot;
				if (isAutoBot)
				{
					flag2 = true;
				}
				else
				{
					bool flag3 = !base.IsLiving;
					if (flag3)
					{
						bool flag4 = base.PlaceProp < 0;
						if (flag4)
						{
							flag2 = base.psychic >= item.Property7 && this.SoulPropCount < this.MaxSoulPropUsedCount;
						}
						else
						{
							flag2 = this.SoulPropCount < this.MaxSoulPropUsedCount;
						}
					}
					else
					{
						bool flag5 = this.propsBloqueados.Contains(item.TemplateID);
						if (flag5)
						{
							flag2 = false;
						}
						else
						{
							bool flag6 = !base.IsAttacking;
							if (flag6)
							{
								bool flag7 = !base.IsLiving && base.Team == this.m_game.CurrentLiving.Team;
								flag2 = flag7 && this.IsActive;
							}
							else
							{
								flag2 = this.m_energy >= item.Property4 && (base.IsAttacking || (!base.IsLiving && base.Team == this.m_game.CurrentLiving.Team));
							}
						}
					}
				}
			}
			return flag2;
		}

		// Token: 0x060036F5 RID: 14069 RVA: 0x000DB060 File Offset: 0x000D9260
		public bool UseItem(ItemTemplateInfo item)
		{
			bool flag = this.CanUseItem(item);
			bool flag4;
			if (flag)
			{
				bool isLiving = base.IsLiving;
				if (isLiving)
				{
					this.m_energy -= item.Property4;
					bool limitEnergy = this.LimitEnergy;
					if (limitEnergy)
					{
						this.TotalCureEnergy -= item.Property4;
					}
					this.m_delay += item.Property5;
				}
				else
				{
					bool flag2 = base.PlaceProp < 0;
					if (flag2)
					{
						base.psychic -= item.Property7;
					}
					bool flag3 = base.Game.CurrentLiving != null;
					if (flag3)
					{
						base.Game.CurrentLiving.AddDelay(item.Property5);
					}
					this.SoulPropCount++;
				}
				this.m_game.SendPlayerUseProp(this, -2, -2, item.TemplateID, this);
				SpellMgr.ExecuteSpell(this.m_game, this, item);
				this.Prop.Add(item.TemplateID);
				base.OnBeginUseProp();
				flag4 = true;
			}
			else
			{
				flag4 = false;
			}
			return flag4;
		}

		// Token: 0x060036F6 RID: 14070 RVA: 0x000DB17C File Offset: 0x000D937C
		public void UseFlySkill()
		{
			bool flag = this._flyCoolDown <= 0;
			if (flag)
			{
				this.m_game.SendPlayerUseProp(this, -2, -2, Player.CARRY_TEMPLATE_ID);
				this.SetBall(3);
				this._flyCoolDown = 2;
				this.m_energy -= 150;
			}
		}

		// Token: 0x060036F7 RID: 14071 RVA: 0x000DB1D4 File Offset: 0x000D93D4
		public void UseSecondWeapon()
		{
			bool flag = this.m_DeputyWeapon == null || !this.CanUseItem(this.m_DeputyWeapon.Template);
			if (!flag)
			{
				bool flag2 = this.m_DeputyWeapon.Template.Property3 == 31;
				if (flag2)
				{
					bool flag3 = false;
					bool flag4 = new List<int> { 17006, 17012, 17013 }.Contains(this.m_DeputyWeapon.TemplateID);
					if (flag4)
					{
						flag3 = true;
					}
					new AddGuardEquipEffect((int)base.GetHertAddition(this.m_DeputyWeapon), 1, flag3).Start(this);
					this.OnPlayerGuard();
				}
				else
				{
					this.SetCurrentWeapon(this.m_DeputyWeapon);
					this.OnPlayerCure();
				}
				this.ShootCount = 1;
				this.m_energy -= this.m_DeputyWeapon.Template.Property4;
				this.m_delay += this.m_DeputyWeapon.Template.Property5;
				this.m_game.SendPlayerUseProp(this, -2, -2, this.m_DeputyWeapon.Template.TemplateID);
				bool flag5 = this.deputyWeaponResCount > 0;
				if (flag5)
				{
					this.deputyWeaponResCount--;
					this.m_game.SendUseDeputyWeapon(this, this.deputyWeaponResCount);
				}
				this.OnPlayerUseSecondWeapon(this.m_DeputyWeapon.Template.Property3);
			}
		}

		// Token: 0x060036F8 RID: 14072 RVA: 0x000DB350 File Offset: 0x000D9550
		public void DeadLink()
		{
			this.m_isActive = false;
			bool isLiving = base.IsLiving;
			if (isLiving)
			{
				this.Die();
			}
		}

		// Token: 0x060036F9 RID: 14073 RVA: 0x000DB378 File Offset: 0x000D9578
		public bool CheckShootPoint(int x, int y)
		{
			bool flag = Math.Abs(this.X - x) > 100;
			bool flag2;
			if (flag)
			{
				string userName = this.m_player.PlayerCharacter.UserName;
				string nickName = this.m_player.PlayerCharacter.NickName;
				this.m_player.Disconnect();
				flag2 = false;
			}
			else
			{
				flag2 = true;
			}
			return flag2;
		}

		// Token: 0x060036FA RID: 14074 RVA: 0x000DB3D4 File Offset: 0x000D95D4
		public void SendAttackInformation()
		{
			bool flag = this.AttackEffectTrigger && this.AttackInformation;
			if (flag)
			{
				base.Game.SendMessage(this.PlayerDetail, LanguageMgr.GetTranslation("PlayerEquipEffect.Success", Array.Empty<object>()), LanguageMgr.GetTranslation("PlayerEquipEffect.Success1", new object[] { this.PlayerDetail.PlayerCharacter.NickName }), 3);
				this.AttackEffectTrigger = false;
				this.AttackInformation = false;
			}
		}

		// Token: 0x060036FB RID: 14075 RVA: 0x000DB44C File Offset: 0x000D964C
		public override void OnAfterKillingLiving(Living target, int damageAmount, int criticalAmount)
		{
			base.OnAfterKillingLiving(target, damageAmount, criticalAmount);
			bool flag = target is Player;
			if (flag)
			{
				this.m_player.OnKillingLiving(this.m_game, 1, target.Id, target.IsLiving, damageAmount + criticalAmount);
				this.CalculatePlayerOffer(target as Player);
			}
			else
			{
				int num = 0;
				bool flag2 = target is SimpleBoss;
				if (flag2)
				{
					SimpleBoss simpleBoss = target as SimpleBoss;
					num = simpleBoss.NpcInfo.ID;
				}
				bool flag3 = target is SimpleNpc;
				if (flag3)
				{
					SimpleNpc simpleNpc = target as SimpleNpc;
					num = simpleNpc.NpcInfo.ID;
				}
				this.m_player.OnKillingLiving(this.m_game, 2, num, target.IsLiving, damageAmount + criticalAmount);
			}
			bool flag4 = base.FightBuffers.CardAddDanderLv1 > 0;
			if (flag4)
			{
				base.AddDander(base.FightBuffers.CardAddDanderLv1);
			}
			bool flag5 = base.FightBuffers.CardAddDanderLv2 > 0;
			if (flag5)
			{
				base.AddDander(base.FightBuffers.CardAddDanderLv2);
			}
			bool flag6 = base.FightBuffers.CardAddDanderLv3 > 0;
			if (flag6)
			{
				base.AddDander(base.FightBuffers.CardAddDanderLv3);
			}
			bool flag7 = base.FightBuffers.CardAddDanderLv4 > 0;
			if (flag7)
			{
				base.AddDander(base.FightBuffers.CardAddDanderLv4);
			}
		}

		// Token: 0x060036FC RID: 14076 RVA: 0x000DB5B0 File Offset: 0x000D97B0
		protected void OnLoadingCompleted()
		{
			bool flag = this.LoadingCompleted != null;
			if (flag)
			{
				this.LoadingCompleted(this);
			}
		}

		// Token: 0x060036FD RID: 14077 RVA: 0x000DB5DC File Offset: 0x000D97DC
		public void OnPlayerShoot()
		{
			bool flag = this.PlayerShoot != null;
			if (flag)
			{
				this.PlayerShoot(this);
			}
		}

		// Token: 0x060036FE RID: 14078 RVA: 0x000DB608 File Offset: 0x000D9808
		public void OnPlayerSkip()
		{
			bool flag = this.PlayerSkip != null;
			if (flag)
			{
				this.PlayerSkip(this);
			}
		}

		// Token: 0x060036FF RID: 14079 RVA: 0x000DB634 File Offset: 0x000D9834
		public void OnPlayerAfterBuffSkillPet()
		{
			bool flag = this.PlayerAfterBuffSkillPet != null;
			if (flag)
			{
				this.PlayerAfterBuffSkillPet(this);
			}
		}

		// Token: 0x06003700 RID: 14080 RVA: 0x000DB660 File Offset: 0x000D9860
		public void OnPlayerBuffSkill()
		{
			bool flag = this.PlayerBuffSkill != null;
			if (flag)
			{
				this.PlayerBuffSkill(this);
			}
		}

		// Token: 0x06003701 RID: 14081 RVA: 0x000DB68C File Offset: 0x000D988C
		public void OnPlayerAnyShellThrow()
		{
			bool flag = this.PlayerAnyShellThrow != null;
			if (flag)
			{
				this.PlayerAnyShellThrow(this);
			}
		}

		// Token: 0x06003702 RID: 14082 RVA: 0x000DB6B8 File Offset: 0x000D98B8
		public void OnPlayerShootCure()
		{
			bool flag = this.PlayerShootCure != null;
			if (flag)
			{
				this.PlayerShootCure(this);
			}
		}

		// Token: 0x06003703 RID: 14083 RVA: 0x000DB6E4 File Offset: 0x000D98E4
		protected void OnPlayerMoving()
		{
			bool flag = this.PlayerBeginMoving != null;
			if (flag)
			{
				this.PlayerBeginMoving(this);
			}
		}

		// Token: 0x06003704 RID: 14084 RVA: 0x000DB710 File Offset: 0x000D9910
		protected void OnBeforePlayerShoot(int ball)
		{
			bool flag = this.BeforePlayerShoot != null;
			if (flag)
			{
				this.BeforePlayerShoot(this, ball);
			}
		}

		// Token: 0x06003705 RID: 14085 RVA: 0x000DB73C File Offset: 0x000D993C
		protected void OnAfterPlayerShoot()
		{
			bool flag = this.AfterPlayerShooted != null;
			if (flag)
			{
				this.AfterPlayerShooted(this);
			}
		}

		// Token: 0x06003706 RID: 14086 RVA: 0x000DB768 File Offset: 0x000D9968
		protected void OnPlayerCompleteShoot()
		{
			bool flag = this.PlayerCompleteShoot != null;
			if (flag)
			{
				this.PlayerCompleteShoot(this);
			}
		}

		// Token: 0x06003707 RID: 14087 RVA: 0x000DB794 File Offset: 0x000D9994
		public void OnPlayerGuard()
		{
			bool flag = this.PlayerGuard != null;
			if (flag)
			{
				this.PlayerGuard(this);
			}
		}

		// Token: 0x06003708 RID: 14088 RVA: 0x000DB7C0 File Offset: 0x000D99C0
		public void OnPlayerCure()
		{
			bool flag = this.PlayerCure != null;
			if (flag)
			{
				this.PlayerCure(this);
			}
		}

		// Token: 0x06003709 RID: 14089 RVA: 0x000DB7EC File Offset: 0x000D99EC
		public void OnPlayerBuffSkillPet()
		{
			this.jaUsouSkill = true;
			bool flag = this.PlayerBuffSkillPet != null;
			if (flag)
			{
				this.PlayerBuffSkillPet(this);
			}
		}

		// Token: 0x0600370A RID: 14090 RVA: 0x000DB820 File Offset: 0x000D9A20
		public void OnPlayerClearBuffSkillPet()
		{
			bool flag = this.PlayerClearBuffSkillPet != null;
			if (flag)
			{
				this.PlayerClearBuffSkillPet(this);
			}
		}

		// Token: 0x0600370B RID: 14091 RVA: 0x000DB84C File Offset: 0x000D9A4C
		protected void OnCollidedByObject()
		{
			bool flag = this.CollidByObject != null;
			if (flag)
			{
				this.CollidByObject(this);
			}
		}

		// Token: 0x0600370C RID: 14092 RVA: 0x000DB878 File Offset: 0x000D9A78
		public void OnPlayerUseDander()
		{
			bool flag = this.PlayerUseDander != null;
			if (flag)
			{
				this.PlayerUseDander(this);
			}
		}

		// Token: 0x0600370D RID: 14093 RVA: 0x000DB8A4 File Offset: 0x000D9AA4
		public void OnPlayerBeforeReset()
		{
			bool flag = this.PlayerBeforeReset != null;
			if (flag)
			{
				this.PlayerBeforeReset(this);
			}
		}

		// Token: 0x0600370E RID: 14094 RVA: 0x000DB8D0 File Offset: 0x000D9AD0
		public void OnPlayerAfterReset()
		{
			bool flag = this.PlayerAfterReset != null;
			if (flag)
			{
				this.PlayerAfterReset(this);
			}
		}

		// Token: 0x0600370F RID: 14095 RVA: 0x000DB8FC File Offset: 0x000D9AFC
		public void OnPlayerUseSecondWeapon(int type)
		{
			bool flag = this.PlayerUseSecondWeapon != null;
			if (flag)
			{
				this.PlayerUseSecondWeapon(this, type);
			}
		}

		// Token: 0x06003710 RID: 14096 RVA: 0x000DB928 File Offset: 0x000D9B28
		public void OnPlayerUsePetMP(int value)
		{
			bool flag = this.PlayerUsePetMP != null;
			if (flag)
			{
				this.PlayerUsePetMP(this, value);
			}
		}

		// Token: 0x06003711 RID: 14097 RVA: 0x000DB954 File Offset: 0x000D9B54
		public bool IsSkillPet(int skillID)
		{
			if (skillID <= 126)
			{
				if (skillID - 25 > 11 && skillID - 103 > 2 && skillID - 124 > 2)
				{
					goto IL_0062;
				}
			}
			else if (skillID <= 162)
			{
				if (skillID - 143 > 2 && skillID - 160 > 2)
				{
					goto IL_0062;
				}
			}
			else if (skillID - 178 > 2 && skillID - 195 > 2)
			{
				goto IL_0062;
			}
			return true;
			IL_0062:
			return false;
		}

		// Token: 0x06003712 RID: 14098 RVA: 0x000DB9C8 File Offset: 0x000D9BC8
		public void PetUseKill(int skillId)
		{
			bool flag = !this.CanUsePetSkill || !this.PetSkillCD.ContainsKey(skillId) || base.PetEffects.DisibleActiveSkill || this.jaUsouSkill;
			if (!flag)
			{
				PetSkillInfo petSkillInfo = this._petSkillCd[skillId];
				bool flag2 = petSkillInfo.CostFlag == 0;
				if (flag2)
				{
					bool flag3 = base.PetMP > 0 && base.PetMP >= petSkillInfo.CostMP;
					if (flag3)
					{
						bool flag4 = petSkillInfo.Turn > 0;
						if (flag4)
						{
							this.m_player.SendMessage(LanguageMgr.GetTranslation("PlayerPetColdDownNotEnough", Array.Empty<object>()));
						}
						else
						{
							bool flag5 = petSkillInfo.NewBallID != -1;
							if (flag5)
							{
								base.PetEffects.Delay += petSkillInfo.Delay;
								this.SetBall(petSkillInfo.NewBallID);
							}
							base.PetMP -= petSkillInfo.CostMP;
							this.OnPlayerUsePetMP(petSkillInfo.CostMP);
							base.PetEffects.CurrentUseSkill = skillId;
							base.PetEffects.BallType = petSkillInfo.BallType;
							this.OnPlayerBuffSkillPet();
							this.m_game.SendPetUseKill(this);
							petSkillInfo.Turn = petSkillInfo.ColdDown + 1;
							this.OnPlayerAfterBuffSkillPet();
							this.OnPlayerBuffSkill();
						}
					}
					else
					{
						this.m_player.SendMessage(LanguageMgr.GetTranslation("PlayerPetMpNotEnough", Array.Empty<object>()));
					}
				}
				else
				{
					bool flag6 = base.PetFlag > 0 && base.PetFlag >= petSkillInfo.CostFlag;
					if (flag6)
					{
						bool flag7 = petSkillInfo.Turn > 0;
						if (flag7)
						{
							this.m_player.SendMessage(LanguageMgr.GetTranslation("PlayerPetColdDownNotEnough", Array.Empty<object>()));
						}
						else
						{
							bool flag8 = petSkillInfo.NewBallID != -1;
							if (flag8)
							{
								base.PetEffects.Delay += petSkillInfo.Delay;
								this.SetBall(petSkillInfo.NewBallID);
							}
							base.PetFlag -= petSkillInfo.CostFlag;
							base.PetEffects.CurrentUseSkill = skillId;
							base.PetEffects.BallType = petSkillInfo.BallType;
							this.OnPlayerBuffSkillPet();
							this.m_game.SendPetUseKill(this);
							petSkillInfo.Turn = petSkillInfo.ColdDown + 1;
							this.OnPlayerAfterBuffSkillPet();
						}
					}
					else
					{
						this.m_player.SendMessage(LanguageMgr.GetTranslation("PlayerPetMpNotEnough", Array.Empty<object>()));
					}
				}
			}
		}

		// Token: 0x06003713 RID: 14099 RVA: 0x000DBC5C File Offset: 0x000D9E5C
		public void ResetSkillCd()
		{
			bool flag = this.m_pet == null;
			if (!flag)
			{
				string[] array = this.m_pet.SkillEquip.Split(new char[] { '|' });
				string[] array2 = array;
				foreach (string text in array2)
				{
					int num = int.Parse(text.Split(new char[] { ',' })[0]);
					bool flag2 = this._petSkillCd.ContainsKey(num);
					if (flag2)
					{
						this._petSkillCd[num].Turn = this._petSkillCd[num].ColdDown;
					}
				}
			}
		}

		// Token: 0x06003714 RID: 14100 RVA: 0x000DBD10 File Offset: 0x000D9F10
		public bool IsCure()
		{
			int templateID = this.Weapon.TemplateID;
			int num = templateID;
			switch (num)
			{
			case 17000:
			case 17001:
			case 17002:
			case 17005:
			case 17007:
			case 17010:
			case 17011:
			case 17015:
			case 17017:
			case 17018:
			case 17019:
			case 17020:
				break;
			case 17003:
			case 17004:
			case 17006:
			case 17008:
			case 17009:
			case 17012:
			case 17016:
			case 17021:
			case 17022:
			case 17023:
				goto IL_009B;
			case 17013:
			case 17014:
				goto IL_009F;
			default:
				switch (num)
				{
				case 17100:
				case 17102:
					break;
				case 17101:
					goto IL_009B;
				default:
					goto IL_009F;
				}
				break;
			}
			return true;
			IL_009B:
			return false;
			IL_009F:
			return false;
		}

		// Token: 0x04002124 RID: 8484
		private IGamePlayer m_player;

		// Token: 0x04002125 RID: 8485
		private bool m_isActive;

		// Token: 0x04002126 RID: 8486
		private ItemInfo m_weapon;

		// Token: 0x04002127 RID: 8487
		private ItemInfo m_DeputyWeapon;

		// Token: 0x04002128 RID: 8488
		private int m_mainBallId;

		// Token: 0x04002129 RID: 8489
		public List<int> ListObject = new List<int>();

		// Token: 0x0400212A RID: 8490
		private int m_spBallId;

		// Token: 0x0400212B RID: 8491
		private int m_spsBallId;

		// Token: 0x0400212C RID: 8492
		private int m_AddWoundBallId;

		// Token: 0x0400212D RID: 8493
		private int m_MultiBallId;

		// Token: 0x0400212E RID: 8494
		private BallInfo m_currentBall;

		// Token: 0x0400212F RID: 8495
		private int m_changeSpecialball;

		// Token: 0x04002130 RID: 8496
		private int m_energy;

		// Token: 0x04002131 RID: 8497
		private List<int> m_prop;

		// Token: 0x04002132 RID: 8498
		private int m_ratioPower;

		// Token: 0x04002133 RID: 8499
		public Point TargetPoint;

		// Token: 0x04002134 RID: 8500
		public int GainGP;

		// Token: 0x04002135 RID: 8501
		public int GainOffer;

		// Token: 0x04002136 RID: 8502
		public bool LockDirection = false;

		// Token: 0x04002137 RID: 8503
		public bool isLockEmery = false;

		// Token: 0x04002138 RID: 8504
		public bool isLockXY = false;

		// Token: 0x04002139 RID: 8505
		private bool m_canGetProp;

		// Token: 0x0400213A RID: 8506
		private List<int> propsBloqueados;

		// Token: 0x0400213B RID: 8507
		public int TotalAllHurt;

		// Token: 0x0400213C RID: 8508
		public int TotalAllHitTargetCount;

		// Token: 0x0400213D RID: 8509
		public int TotalAllShootCount;

		// Token: 0x0400213E RID: 8510
		public int TotalAllKill;

		// Token: 0x0400213F RID: 8511
		public int TotalAllExperience;

		// Token: 0x04002140 RID: 8512
		public int TotalAllScore;

		// Token: 0x04002141 RID: 8513
		public int TotalAllCure;

		// Token: 0x04002142 RID: 8514
		public int CanTakeOut;

		// Token: 0x04002143 RID: 8515
		public bool FinishTakeCard;

		// Token: 0x04002144 RID: 8516
		public bool HasPaymentTakeCard;

		// Token: 0x04002145 RID: 8517
		public int BossCardCount;

		// Token: 0x04002146 RID: 8518
		public bool Ready;

		// Token: 0x04002147 RID: 8519
		public bool AttackInformation;

		// Token: 0x04002148 RID: 8520
		public bool DefenceInformation;

		// Token: 0x04002149 RID: 8521
		public int TotalCureEnergy;

		// Token: 0x0400214A RID: 8522
		public bool LimitEnergy;

		// Token: 0x0400214B RID: 8523
		public bool CanUsePetSkill;

		// Token: 0x0400214C RID: 8524
		public int MaxSoulPropUsedCount = 2;

		// Token: 0x0400214D RID: 8525
		public int SoulPropCount;

		// Token: 0x0400214E RID: 8526
		public bool IsShadown;

		// Token: 0x0400214F RID: 8527
		private Dictionary<int, PetSkillInfo> _petSkillCd;

		// Token: 0x04002150 RID: 8528
		private UserPetInfo m_pet;

		// Token: 0x04002151 RID: 8529
		private UserBufferInfo m_bufferPoint;

		// Token: 0x04002152 RID: 8530
		private int m_killedPunishmentOffer;

		// Token: 0x04002153 RID: 8531
		private int m_loadingProcess;

		// Token: 0x04002154 RID: 8532
		private int m_shootCount;

		// Token: 0x04002155 RID: 8533
		private int m_ballCount;

		// Token: 0x04002156 RID: 8534
		private ArrayList m_tempBoxes = new ArrayList();

		// Token: 0x04002157 RID: 8535
		private int[] buffs;

		// Token: 0x04002158 RID: 8536
		private static readonly int CARRY_TEMPLATE_ID = 10016;

		// Token: 0x04002159 RID: 8537
		private int _flyCoolDown;

		// Token: 0x0400215A RID: 8538
		private int deputyWeaponResCount = 0;

		// Token: 0x020004F0 RID: 1264
		// (Invoke) Token: 0x06003717 RID: 14103
		public delegate void PlayerUsePetMPEventHandle(Player player, int value);
	}
}
