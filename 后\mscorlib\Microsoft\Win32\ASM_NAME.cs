﻿using System;

namespace Microsoft.Win32
{
	// Token: 0x0200000C RID: 12
	internal static class ASM_NAME
	{
		// Token: 0x04000057 RID: 87
		public const uint PUBLIC_KEY = 0U;

		// Token: 0x04000058 RID: 88
		public const uint PUBLIC_KEY_TOKEN = 1U;

		// Token: 0x04000059 RID: 89
		public const uint HASH_VALUE = 2U;

		// Token: 0x0400005A RID: 90
		public const uint NAME = 3U;

		// Token: 0x0400005B RID: 91
		public const uint MAJOR_VERSION = 4U;

		// Token: 0x0400005C RID: 92
		public const uint MINOR_VERSION = 5U;

		// Token: 0x0400005D RID: 93
		public const uint BUILD_NUMBER = 6U;

		// Token: 0x0400005E RID: 94
		public const uint REVISION_NUMBER = 7U;

		// Token: 0x0400005F RID: 95
		public const uint CULTURE = 8U;

		// Token: 0x04000060 RID: 96
		public const uint PROCESSOR_ID_ARRAY = 9U;

		// Token: 0x04000061 RID: 97
		public const uint OSINFO_ARRAY = 10U;

		// Token: 0x04000062 RID: 98
		public const uint HASH_ALGID = 11U;

		// Token: 0x04000063 RID: 99
		public const uint ALIAS = 12U;

		// Token: 0x04000064 RID: 100
		public const uint CODEBASE_URL = 13U;

		// Token: 0x04000065 RID: 101
		public const uint CODEBASE_LASTMOD = 14U;

		// Token: 0x04000066 RID: 102
		public const uint NULL_PUBLIC_KEY = 15U;

		// Token: 0x04000067 RID: 103
		public const uint NULL_PUBLIC_KEY_TOKEN = 16U;

		// Token: 0x04000068 RID: 104
		public const uint CUSTOM = 17U;

		// Token: 0x04000069 RID: 105
		public const uint NULL_CUSTOM = 18U;

		// Token: 0x0400006A RID: 106
		public const uint MVID = 19U;

		// Token: 0x0400006B RID: 107
		public const uint _32_BIT_ONLY = 20U;

		// Token: 0x0400006C RID: 108
		public const uint MAX_PARAMS = 21U;
	}
}
