﻿using System;
using System.Runtime.CompilerServices;

namespace System.Collections.Generic
{
	// Token: 0x020004D0 RID: 1232
	[TypeDependency("System.SZArrayHelper")]
	[__DynamicallyInvokable]
	public interface ICollection<T> : IEnumerable<T>, IEnumerable
	{
		// Token: 0x170008E6 RID: 2278
		// (get) Token: 0x06003ACD RID: 15053
		[__DynamicallyInvokable]
		int Count
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x170008E7 RID: 2279
		// (get) Token: 0x06003ACE RID: 15054
		[__DynamicallyInvokable]
		bool IsReadOnly
		{
			[__DynamicallyInvokable]
			get;
		}

		// Token: 0x06003ACF RID: 15055
		[__DynamicallyInvokable]
		void Add(T item);

		// Token: 0x06003AD0 RID: 15056
		[__DynamicallyInvokable]
		void Clear();

		// Token: 0x06003AD1 RID: 15057
		[__DynamicallyInvokable]
		bool Contains(T item);

		// Token: 0x06003AD2 RID: 15058
		[__DynamicallyInvokable]
		void CopyTo(T[] array, int arrayIndex);

		// Token: 0x06003AD3 RID: 15059
		[__DynamicallyInvokable]
		bool Remove(T item);
	}
}
