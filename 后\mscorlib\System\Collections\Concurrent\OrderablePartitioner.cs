﻿using System;
using System.Collections.Generic;
using System.Security.Permissions;

namespace System.Collections.Concurrent
{
	// Token: 0x020004B2 RID: 1202
	[__DynamicallyInvokable]
	[HostProtection(SecurityAction.LinkDemand, Synchronization = true, ExternalThreading = true)]
	public abstract class OrderablePartitioner<TSource> : Partitioner<TSource>
	{
		// Token: 0x0600399C RID: 14748 RVA: 0x000DC92E File Offset: 0x000DAB2E
		[__DynamicallyInvokable]
		protected OrderablePartitioner(bool keysOrderedInEachPartition, bool keysOrderedAcrossPartitions, bool keysNormalized)
		{
			this.KeysOrderedInEachPartition = keysOrderedInEachPartition;
			this.KeysOrderedAcrossPartitions = keysOrderedAcrossPartitions;
			this.KeysNormalized = keysNormalized;
		}

		// Token: 0x0600399D RID: 14749
		[__DynamicallyInvokable]
		public abstract IList<IEnumerator<KeyValuePair<long, TSource>>> GetOrderablePartitions(int partitionCount);

		// Token: 0x0600399E RID: 14750 RVA: 0x000DC94B File Offset: 0x000DAB4B
		[__DynamicallyInvokable]
		public virtual IEnumerable<KeyValuePair<long, TSource>> GetOrderableDynamicPartitions()
		{
			throw new NotSupportedException(Environment.GetResourceString("Partitioner_DynamicPartitionsNotSupported"));
		}

		// Token: 0x170008A3 RID: 2211
		// (get) Token: 0x0600399F RID: 14751 RVA: 0x000DC95C File Offset: 0x000DAB5C
		// (set) Token: 0x060039A0 RID: 14752 RVA: 0x000DC964 File Offset: 0x000DAB64
		[__DynamicallyInvokable]
		public bool KeysOrderedInEachPartition
		{
			[__DynamicallyInvokable]
			get;
			private set; }

		// Token: 0x170008A4 RID: 2212
		// (get) Token: 0x060039A1 RID: 14753 RVA: 0x000DC96D File Offset: 0x000DAB6D
		// (set) Token: 0x060039A2 RID: 14754 RVA: 0x000DC975 File Offset: 0x000DAB75
		[__DynamicallyInvokable]
		public bool KeysOrderedAcrossPartitions
		{
			[__DynamicallyInvokable]
			get;
			private set; }

		// Token: 0x170008A5 RID: 2213
		// (get) Token: 0x060039A3 RID: 14755 RVA: 0x000DC97E File Offset: 0x000DAB7E
		// (set) Token: 0x060039A4 RID: 14756 RVA: 0x000DC986 File Offset: 0x000DAB86
		[__DynamicallyInvokable]
		public bool KeysNormalized
		{
			[__DynamicallyInvokable]
			get;
			private set; }

		// Token: 0x060039A5 RID: 14757 RVA: 0x000DC990 File Offset: 0x000DAB90
		[__DynamicallyInvokable]
		public override IList<IEnumerator<TSource>> GetPartitions(int partitionCount)
		{
			IList<IEnumerator<KeyValuePair<long, TSource>>> orderablePartitions = this.GetOrderablePartitions(partitionCount);
			if (orderablePartitions.Count != partitionCount)
			{
				throw new InvalidOperationException("OrderablePartitioner_GetPartitions_WrongNumberOfPartitions");
			}
			IEnumerator<TSource>[] array = new IEnumerator<TSource>[partitionCount];
			for (int i = 0; i < partitionCount; i++)
			{
				array[i] = new OrderablePartitioner<TSource>.EnumeratorDropIndices(orderablePartitions[i]);
			}
			return array;
		}

		// Token: 0x060039A6 RID: 14758 RVA: 0x000DC9DC File Offset: 0x000DABDC
		[__DynamicallyInvokable]
		public override IEnumerable<TSource> GetDynamicPartitions()
		{
			IEnumerable<KeyValuePair<long, TSource>> orderableDynamicPartitions = this.GetOrderableDynamicPartitions();
			return new OrderablePartitioner<TSource>.EnumerableDropIndices(orderableDynamicPartitions);
		}

		// Token: 0x02000BCB RID: 3019
		private class EnumerableDropIndices : IEnumerable<TSource>, IEnumerable, IDisposable
		{
			// Token: 0x06006EA2 RID: 28322 RVA: 0x0017DB17 File Offset: 0x0017BD17
			public EnumerableDropIndices(IEnumerable<KeyValuePair<long, TSource>> source)
			{
				this.m_source = source;
			}

			// Token: 0x06006EA3 RID: 28323 RVA: 0x0017DB26 File Offset: 0x0017BD26
			public IEnumerator<TSource> GetEnumerator()
			{
				return new OrderablePartitioner<TSource>.EnumeratorDropIndices(this.m_source.GetEnumerator());
			}

			// Token: 0x06006EA4 RID: 28324 RVA: 0x0017DB38 File Offset: 0x0017BD38
			IEnumerator IEnumerable.GetEnumerator()
			{
				return this.GetEnumerator();
			}

			// Token: 0x06006EA5 RID: 28325 RVA: 0x0017DB40 File Offset: 0x0017BD40
			public void Dispose()
			{
				IDisposable disposable = this.m_source as IDisposable;
				if (disposable != null)
				{
					disposable.Dispose();
				}
			}

			// Token: 0x040035C5 RID: 13765
			private readonly IEnumerable<KeyValuePair<long, TSource>> m_source;
		}

		// Token: 0x02000BCC RID: 3020
		private class EnumeratorDropIndices : IEnumerator<TSource>, IDisposable, IEnumerator
		{
			// Token: 0x06006EA6 RID: 28326 RVA: 0x0017DB62 File Offset: 0x0017BD62
			public EnumeratorDropIndices(IEnumerator<KeyValuePair<long, TSource>> source)
			{
				this.m_source = source;
			}

			// Token: 0x06006EA7 RID: 28327 RVA: 0x0017DB71 File Offset: 0x0017BD71
			public bool MoveNext()
			{
				return this.m_source.MoveNext();
			}

			// Token: 0x170012E9 RID: 4841
			// (get) Token: 0x06006EA8 RID: 28328 RVA: 0x0017DB80 File Offset: 0x0017BD80
			public TSource Current
			{
				get
				{
					KeyValuePair<long, TSource> keyValuePair = this.m_source.Current;
					return keyValuePair.Value;
				}
			}

			// Token: 0x170012EA RID: 4842
			// (get) Token: 0x06006EA9 RID: 28329 RVA: 0x0017DBA0 File Offset: 0x0017BDA0
			object IEnumerator.Current
			{
				get
				{
					return this.Current;
				}
			}

			// Token: 0x06006EAA RID: 28330 RVA: 0x0017DBAD File Offset: 0x0017BDAD
			public void Dispose()
			{
				this.m_source.Dispose();
			}

			// Token: 0x06006EAB RID: 28331 RVA: 0x0017DBBA File Offset: 0x0017BDBA
			public void Reset()
			{
				this.m_source.Reset();
			}

			// Token: 0x040035C6 RID: 13766
			private readonly IEnumerator<KeyValuePair<long, TSource>> m_source;
		}
	}
}
